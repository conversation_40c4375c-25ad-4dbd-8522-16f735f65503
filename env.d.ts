/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-08-28 14:54:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-12-18 10:25:54
 * @FilePath: /corp-elf-web-consumer/env.d.ts
 * @Description:
 */
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly SENTRY_AUTH_TOKEN: string
  readonly VITE_BASE_API: string
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
