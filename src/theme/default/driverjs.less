@primary-color: var(--g-primary-color, #6553ee);

.driver-popover {
  padding: 16px !important;
  max-width: 500px !important;
}

.driver-popover-title {
  font-size: 18px !important;
}
.driver-popover-description {
  display: inline-flex !important;
  flex-direction: column !important;
  font-size: 16px !important;
  margin-top: 10px !important;
  gap: 8px;
}

.driver-btn {
  outline: none;
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  background-color: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  user-select: none;
  touch-action: manipulation;
  line-height: 1.5714285714285714;
  color: rgba(0, 0, 0, 0.88);
  text-shadow: none !important;
  border: none !important;
  font-size: 14px !important;
  height: 24px !important;
  padding: 0px 7px !important;
  border-radius: 4px !important;
  box-sizing: border-box !important;
}
.driver-btn-prev {
  background-color: #ffffff !important;
  border: 1px solid transparent !important;
  border-color: #d9d9d9 !important;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02) !important;
}
.driver-btn-next {
  color: #fff !important;
  background-color: @primary-color !important;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02) !important;
}

.tourItem {
  display: flex;
  position: relative;
  align-items: start;
  &::before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    background-image: url('@/assets/images/point_icon.png');
    background-repeat: no-repeat;
    background-size: contain;
    margin-right: 8px;
    margin-top: 4px;
  }
}
