<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-11-07 11:14:02
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-11-25 10:54:26
 * @FilePath: /corp-elf-web-consumer/src/views/invite/index.vue
 * @Description: 
-->
<template>
  <div class="max-w900px mx-auto invite">
    <img src="@/assets/images/reward_banner.png" />

    <div class="flex items-center w-80% my-48px mx-auto mb-80px">
      <a-input :value="shareUrl" read-only class="flex1" size="large"></a-input>
      <a-button @click="copyUrl" class="ml-16px" size="large">复制专属链接</a-button>
    </div>

    <a-space direction="vertical" :size="80">
      <a-card :bordered="false" :bodyStyle="{ paddingTop: '48px', paddingBottom: '32px' }" class="br-40px">
        <div>
          <img src="@/assets/images/reward1.svg" class="width-186px top--30px absolute l-0" />
        </div>
        <p class="primaryColor mb-16px fs-16px">
          被邀请人注册180天内首次购买会员，您获得实付金额50%现金分成，现金将在开通会员7个工作日后发放。
        </p>

        <a-row :gutter="16">
          <a-col :span="8">
            <div class="rewardOneCard flex flex-direction-column items-center text-center">
              <p class="stepText color-#bd71ff fs-18px fw-700">step1</p>
              <div class="icon"><iconfont-icon icon="icon-share" class="color-#fff fs-32px!"></iconfont-icon></div>
              <div class="tips mt12px primaryColor fs-16px text-center">复制专属链接 <br />并将链接分享给好友</div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="rewardOneCard flex flex-direction-column items-center text-center">
              <p class="stepText color-#bd71ff fs-18px fw-700">step2</p>
              <div class="icon"><iconfont-icon icon="icon-usergroup" class="color-#fff fs-32px!"></iconfont-icon></div>
              <div class="tips mt12px primaryColor fs-16px text-center">好友扫码注册</div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="rewardOneCard flex flex-direction-column items-center text-center">
              <p class="stepText color-#bd71ff fs-18px fw-700">step3</p>
              <div class="icon"><iconfont-icon icon="icon-money-circle" class="color-#fff fs-32px!"></iconfont-icon></div>
              <div class="tips mt12px primaryColor fs-16px text-center">好友购买会员 <br />你得现金奖励</div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <a-card :bordered="false" :bodyStyle="{ paddingTop: '48px', paddingBottom: '32px' }" class="br-40px">
        <div>
          <img src="@/assets/images/reward2.svg" class="width-186px top--30px absolute l-0" />
        </div>
        <p class="primaryColor mb-16px fs-16px">邀请好友注册商瞳，双方都可获得 1 个月商瞳会员。邀请方最多获得 12月会员。</p>

        <div class="flex items-center h-180px relative">
          <div class="flex-1">
            <div class="rewardTwoCard">
              <logo :is-link="false" />
              <p class="primaryColor fs-18px fw-600 mt-8px ml-16px">1个月会员权益</p>
              <span class="rewardTwoCardRightTop">双方都会获得</span>
              <img src="@/assets/images/reward_vip.svg" class="absolute r-0 b-0 h52px" />
            </div>
          </div>
          <div class="absolute r-45px top--20px">
            <!-- flex-1 relative text-center -->
            <img src="@/assets/images/reward_img.png" class="w200px rotate-y-180" />
          </div>
        </div>
      </a-card>

      <a-card :bordered="false" :bodyStyle="{ paddingTop: '48px', paddingBottom: '32px', minHeight: '536px' }" class="br-40px">
        <div>
          <img src="@/assets/images/reward_invite.svg" class="width-186px top--30px absolute l-0" />
        </div>

        <p class="primaryColor mb-16px fs-16px">
          您已累计获得：
          {{ statisticsData?.totalPrices || 0 }}
          元，
          {{ statisticsData?.memberMonth || 0 }}
          个月商瞳个人版会员。
        </p>

        <div class="text-center mb-32px">
          <a-config-provider
            :theme="{
              components: {
                Segmented: {
                  controlPaddingHorizontal: 48,
                  lineWidth: 0,
                  colorText: '#9d6dff',
                  colorBgLayout: '#6553ee',
                  colorTextLabel: '#fff'
                }
              }
            }"
          >
            <a-segmented v-model:value="activeTab" :options="['我的奖励', '邀请记录']" @change="handlerActiveKeyChange" size="large" />
          </a-config-provider>
        </div>

        <template v-if="activeTab === '我的奖励'">
          <a-config-provider>
            <template #renderEmpty>
              <empty emptyText="暂无奖励"> </empty>
            </template>

            <a-table :dataSource="rewardDataList" :columns="rewardColumns" :pagination="false" :loading="rewardLoading">
              <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'rewardType'">{{ text === 'MONEY' ? '现金' : '会员' }}</template>
                <template v-if="column.dataIndex === 'reward'">
                  <template v-if="record.rewardType === 'MONEY'"> ¥ {{ text }} </template>
                  <template v-else> {{ text }}个月商瞳个人版会员 </template>
                </template>
                <template v-if="column.dataIndex === 'state'">
                  <a-button v-if="text === 0" type="primary" @click="receiveReward(record)">领取</a-button>
                  <template v-else> 已领取 </template>
                </template>
              </template>
            </a-table>
          </a-config-provider>
        </template>
        <template v-else>
          <a-table :dataSource="invitationDataList" :columns="invitationColumns" :pagination="false" :loading="invitationLoading">
            <template #bodyCell="{ text, column }">
              <template v-if="column.dataIndex === 'registerTime'">{{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-' }}</template>
              <template v-if="column.dataIndex === 'openMemberTime'">{{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-' }}</template>
            </template>
          </a-table>
        </template>
      </a-card>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import useListLoading from '@/hooks/useListLoading'
import useRequest from '@/hooks/useRequest'
import {
  userInviteRegisterCode,
  userInviteRegisterStatistics,
  userInviteRegisterRewardList,
  userInviteRegisterRecordList,
  userInviteRegisterRewardReceive
} from '@/api/api'
import { userInviteRegisterRewardListResType } from '~/types/api/userInviteRegister/rewardList'
import { computed } from 'vue'
import { useClipboard } from '@vueuse/core'
import { message, Modal } from 'ant-design-vue'
import { useUserStore } from '@/store'
import empty from '@/components/empty/index.vue'
import { ref } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { h } from 'vue'
import dayjs from 'dayjs'
import logo from '@comp/layouts/headerMenu/logo.vue'

const userStore = useUserStore()

const { dataList: code } = useRequest(userInviteRegisterCode)
const shareUrl = computed(() => `https://insight.bengine.com.cn/register?registerCode=${code.value}`)

// 获取统计金额，会员月数
const { dataList: statisticsData } = useRequest(userInviteRegisterStatistics)

const activeTab = ref<'我的奖励' | '邀请记录'>('我的奖励')
function handlerActiveKeyChange(e: '我的奖励' | '邀请记录') {
  if (e === '我的奖励') {
    refreshRewardData()
  } else {
    refreshInvitationData()
  }
}
// 我的奖励列表
const rewardColumns = [
  { title: '奖励类型', dataIndex: 'rewardType', ellipsis: true },
  { title: '奖励内容', dataIndex: 'reward', ellipsis: true },
  { title: '操作', dataIndex: 'state', ellipsis: true, width: 160 }
]
const { dataList: rewardDataList, loading: rewardLoading, getData: refreshRewardData } = useRequest(userInviteRegisterRewardList)

// 邀请记录列表
const invitationColumns = [
  { title: '被邀请人', dataIndex: 'registerUserName', ellipsis: true },
  { title: '注册时间', dataIndex: 'registerTime', ellipsis: true },
  { title: '开通会员时间', dataIndex: 'openMemberTime', ellipsis: true }
]
const {
  dataList: invitationDataList,
  loading: invitationLoading,
  refresh: refreshInvitationData
} = useListLoading(userInviteRegisterRecordList, { pageSize: 1000 })

// 领取奖励
function receiveReward(item: userInviteRegisterRewardListResType) {
  Modal.confirm({
    title: item.rewardType === 'MONEY' ? '现金领取提示' : '会员领取提示',
    content:
      item.rewardType === 'MONEY'
        ? `是否领取${item.reward}金额到您${userStore.getUserInfo.mobile}商瞳账号关联的微信账户中？领取后不可撤销。`
        : `是否领取${item.reward}月商瞳个人版会员到您${userStore.getUserInfo.mobile}的商瞳账号？领取后不可撤销。`,
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    onOk: async () => {
      try {
        rewardLoading.value = true
        await userInviteRegisterRewardReceive({ ids: item.ids! })
        refreshRewardData()
        userStore.getUserMemberInfo()
        message.success('领取成功')
        rewardLoading.value = false
      } catch (error) {
        rewardLoading.value = false
        console.error(error)
      }
    }
  })
}

const { copy } = useClipboard({ legacy: true })
function copyUrl() {
  copy(`通过我的链接注册，立享超值优惠！\n${shareUrl.value}`)
    .then(() => {
      message.success('复制成功')
    })
    .catch(err => {
      console.error(err)
      message.error('复制失败')
    })
}
</script>

<style lang="less" scoped>
.rewardOneCard {
  .stepText {
  }
  .icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(180deg, #d198ff, #6553ee);
    border-radius: 50%;
    margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .tips {
  }
}
.invite {
  .rewardTwoCard {
    background: linear-gradient(165deg, #ecd0ff, #6553ee);
    // flex: 1;
    width: 438px;
    height: 127px;
    border-radius: 20px;
    background: linear-gradient(165deg, #e8f2ff, #aca2fa);
    box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.12);
    padding: 16px;
    box-sizing: border-box;
    position: relative;
    margin: 0 16px;

    &RightTop {
      // width: 189px;
      height: 29px;
      line-height: 29px;
      border-radius: 0 20px 0 12px;
      padding: 0 32px;
      background: #6553ee;
      position: absolute;
      top: 0;
      right: 0;
      text-align: center;
      color: #fff;
      font-size: 12px;
    }
  }
}
</style>
