<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-06 16:19:27
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-17 20:11:38
 * @FilePath: /corp-elf-web-consumer/src/views/findCompany/index.vue
 * @Description: 
-->
<template>
  <a-row :gutter="16" :wrap="false">
    <a-col flex="260px">
      <a-affix :offset-top="80">
        <ConditionAside v-model:compConfig="compConfig" />
      </a-affix>
    </a-col>

    <a-col flex="auto">
      <KeepAlive>
        <component :is="comp" v-bind="compProps"></component>
      </KeepAlive>
    </a-col>
  </a-row>
</template>

<script setup lang="ts" name="find-company-overview">
import ConditionAside from './components/conditionAside/index.vue'
import AdvanceSearch from '@/views/search/advance/index.vue'
import FollowCompanyTable from '@/views/followCompany/followCompanyList/components/followCompanyTable.vue'
import ModelTable from './components/modelTable/index.vue'
import { useRoute } from 'vue-router'
import { computed, onMounted, ref } from 'vue'

const route = useRoute()
export type CompConfigType = {
  // 展示的表格类型
  type: 'search' | 'model' | 'followCompany' | 'ignore'
  // 表格查询的id
  id: string
  // 一些特定的打开弹窗
  activity?: 'openCustomModel' | 'openSavedSearch'
  activateIndex: string
}

const compConfig = ref<CompConfigType>({
  type: 'search',
  id: '',
  activateIndex: '高级搜索'
})
const comp = computed(() => {
  if (compConfig.value.type === 'search') {
    return AdvanceSearch
  } else if (compConfig.value.type === 'model' || compConfig.value.type === 'ignore') {
    return ModelTable
  } else if (compConfig.value.type === 'followCompany') {
    return FollowCompanyTable
  }
})
const compProps = computed(() => {
  if (compConfig.value.type === 'followCompany') {
    return { collectId: compConfig.value.id }
  } else if (compConfig.value.type === 'model' || compConfig.value.type === 'ignore') {
    return { id: compConfig.value.id }
  } else {
    return {}
  }
})

onMounted(() => {
  const { type = '', id = '', activity = '' } = route.query
  console.log('type: ', type)
  console.log('id: ', id)
  console.log('activity: ', activity)
  const activateIndexMap = {
    search: '高级搜索',
    model: id,
    followCompany: id,
    ignore: '潜客模型'
  }
  compConfig.value = {
    ...compConfig.value,
    type: type as CompConfigType['type'],
    id: id as string,
    activateIndex: activateIndexMap[type as string]
  }
  console.log('compConfig.value: ', compConfig.value)
})
</script>

<style scoped></style>
