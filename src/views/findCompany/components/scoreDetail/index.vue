<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-30 14:09:31
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-09 16:03:21
 * @FilePath: /corp-elf-web-consumer/src/views/findCompany/potentialCustomerModel/components/scoreDetail/index.vue
 * @Description: 
-->
<template>
  <a-modal title="推荐指数详情" width="600px" v-model:open="visible" @cancel="handleClose" :footer="null">
    <div class="scoreDetail">
      <p class="title">{{ title }}</p>

      <div v-for="(scoreItem, index) in scoreList" :key="index" class="scoreItem">
        <div class="conditionTitle flexCenter">
          <span class="totalScore color-#6553ee">{{ scoreItem.score }}</span>
          <p>{{ scoreItem.title }}</p>
        </div>

        <div class="conditionBox">
          <ul class="conditionItem">
            <li
              v-for="(item, index) in scoreItem.popupDescDtos"
              :key="index"
              :class="[item.isActive ? '' : 'color-#828282', 'flex items-center']"
            >
              <iconfontIcon
                :icon="item.isActive ? 'icon-check-circle' : 'icon-close-circle'"
                :style="{ marginRight: '4px' }"
                :class="item.isActive ? 'color-#6553ee' : ''"
              />

              <span class="active">{{ item.description }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script langt="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { defineComponent } from 'vue'

export default defineComponent({
  components: { iconfontIcon },
  data() {
    return {
      visible: false,
      title: '',
      score: '',
      scoreList: []
    }
  },
  methods: {
    onOpen({ title, score, popupDtos }) {
      this.title = title
      this.score = score
      this.scoreList = popupDtos
      this.visible = true
    },
    handleClose() {
      this.visible = false
    }
  }
})
</script>

<style lang="less" scoped>
.scoreDetail {
  color: #303133;
  .title {
    margin-bottom: 10px;
  }
  li {
    line-height: 2;
  }

  .scoreItem {
    position: relative;

    .conditionTitle {
      font-size: 14px;
      font-weight: 500;

      position: absolute;
      top: -20px;
      left: 0;

      .totalScore {
        border-radius: 50%;
        background-color: #eae7fb;
        font-size: 16px;
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        margin-right: 4px;
      }
    }

    .conditionBox {
      border-radius: 8px;
      background-color: #eae7fba3;
      padding: 24px 8px 8px 16px;
      margin-left: 16px;
      margin-top: 32px;
    }
  }
}
</style>
