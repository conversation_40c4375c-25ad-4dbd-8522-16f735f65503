<template>
  <a-spin :spinning="loading">
    <sideNavMenu :navMenu="navMenu" @click="handleSideNavMenuChange" :selectedKeys="activateIndex">
      <template #titleText="navItem">
        <template v-if="navItem.key === '潜客模型'">
          <div class="flex items-center overflow-hidden">
            <h5 class="flex-1">{{ navItem.label }}</h5>
            <div class="flex items-center">
              <span class="block fs-12px color-#999 fw-400 mr-4px">{{ conditionList.length }}/2</span>

              <a-tooltip>
                <template #title>每个用户可以创建2个模型</template>
                <a @click="handleAddConditionBtn" class="addConditionBtn">
                  <iconfont-icon icon="icon-add-circle"></iconfont-icon>
                </a>
              </a-tooltip>
            </div>
          </div>
        </template>
        <template v-else-if="navItem.key === '忽略企业'">
          <span class="hoverPrimaryColor">{{ navItem.label }}</span>
        </template>
        <template v-else-if="navItem.key === '关注的企业'">
          <div class="flex items-center overflow-hidden">
            <h5 class="flex-1">{{ navItem.label }}</h5>
            <div class="flex items-center">
              <a @click="addGroup">
                <iconfont-icon icon="icon-add-circle"></iconfont-icon>
              </a>
            </div>
          </div>
        </template>
        <template v-else>
          {{ navItem.label }}
        </template>
      </template>

      <template #childrenText="navItem">
        <!-- 自定义模型 -->
        <template v-if="conditionList.find(item => item.id === navItem.key)">
          <div class="flex items-center conditionItem">
            <p class="ellipsis flex-1">
              {{ navItem.label }}
            </p>

            <div class="extra">
              <a-space>
                <iconfont-icon
                  icon="icon-edit-1"
                  :extraCommonProps="{ class: 'color-#6553ee fs-18px! hoverPrimaryColor' }"
                  @click.stop="
                    conditionDialogRef?.show({
                      type: 'edit',
                      item: navItem.rawData,
                      nameList: conditionList
                        .map(conditionItem => conditionItem.name)
                        .filter(conditionItem => conditionItem !== navItem.label)
                    })
                  "
                />

                <iconfont-icon
                  icon="icon-delete"
                  :extraCommonProps="{ class: 'color-#6553ee fs-18px! hoverPrimaryColor' }"
                  @click.stop="handleDeleteCondition(navItem.key)"
                />
              </a-space>
            </div>
          </div>
        </template>
        <!-- 关注的企业 -->
        <template v-else-if="navItem.type === 'followCompany'">
          <div class="flex items-center followGrouping">
            <p class="ellipsis">{{ navItem.label }}</p>
            <span class="num color-#8c8c8c flex-1">（{{ navItem.rawData.companyNum || 0 }}）</span>

            <template v-if="navItem.label !== '全部'">
              <div class="extra">
                <a-space>
                  <iconfont-icon
                    icon="icon-edit-1"
                    :extraCommonProps="{ class: 'color-#6553ee fs-18px! hoverPrimaryColor' }"
                    @click.stop="showGroupModal('编辑分组', navItem.rawData)"
                  />
                  <iconfont-icon
                    icon="icon-delete"
                    :extraCommonProps="{ class: 'color-#6553ee fs-18px! hoverPrimaryColor' }"
                    @click.stop="deleteGroup(navItem.rawData)"
                  />
                </a-space>
              </div>
            </template>
          </div>
        </template>
        <template v-else>{{ navItem.label }}</template>
      </template>
    </sideNavMenu>
  </a-spin>

  <condition-dialog ref="conditionDialogRef" @refreshConditionList="handleConditionRefresh" />
  <MyselfGroupModal ref="myselfGroupModalRef" @refresh="getCollect" />
</template>

<script setup lang="ts" name="ConditionAside">
import { message, Modal } from 'ant-design-vue'
import { computed, ref, useTemplateRef, h, onMounted, nextTick } from 'vue'
import { companyRemoveSearchCondition, companyShowSearchConditionV2, customerCollectDelete, getCustomerCollectGroup } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import ConditionDialog from './conditionDialog.vue'
import useListLoading from '@/hooks/useListLoading'
import sideNavMenu, { BaseNavItem } from '@/components/tools/sideNavMenu'
import useRequest from '@/hooks/useRequest'
import { getCustomerCollectGroupResponseType } from '~/types/api/customer/getCustomerCollectGroup'
import MyselfGroupModal from '@/views/followList/components/groupCard/components/myselfGroupModal.vue'
import { LocationQuery, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { isEmpty } from 'lodash-es'

// const themeStore = useThemeStore()

// const activateIndex = ref('搜索')
// const emits = defineEmits<{ (e: 'update:compConfig', value: CompConfigType): void }>()
// const props = defineProps<{ compConfig: CompConfigType }>()
// const compConfig = useVModel(props, 'compConfig', emits)

const navMenu = computed(() => [
  {
    label: '搜索',
    key: '搜索',
    icon: 'icon-chart-bubble',
    type: 'search',
    children: [
      { label: '高级搜索', key: '高级搜索', icon: 'icon-point', type: 'search' },
      { label: '我保存的搜索项', key: '我保存的搜索项', icon: 'icon-point', type: 'search' }
    ]
  },
  {
    label: '潜客模型',
    key: '潜客模型',
    icon: 'icon-chart-bubble',
    type: 'model',
    children: conditionList.value.map(item => ({ label: item.name, key: item.id, icon: 'icon-point', rawData: item, type: 'model' }))
  },
  {
    label: '关注的企业',
    key: '关注的企业',
    icon: 'icon-chart-bubble',
    type: 'followCompany',
    children: (collectDataList.value || []).map(item => ({
      label: item.collectName,
      key: item.collectId,
      icon: 'icon-point',
      rawData: item,
      type: 'followCompany'
    }))
  },
  { label: '忽略企业', key: '忽略企业', icon: 'icon-chart-bubble', type: 'ignore' }
])

const loading = computed(() => conditionListLoading.value || collectLoading.value)

// 获取自定义条件列表
const {
  loading: conditionListLoading,
  dataList: conditionList,
  getData,
  pageParams
} = useListLoading(
  companyShowSearchConditionV2,
  { type: 'RECOMMEND_SEARCH', version: 'V_3_0' },
  {
    pageParams: { pageSize: 1000 }
  }
)

const conditionDialogRef = useTemplateRef('conditionDialogRef')

/**
 * 添加潜客模型按钮点击处理函数
 * @description 检查模型数量是否达到上限(2个)，未达到则打开添加模型对话框
 */
function handleAddConditionBtn() {
  if (pageParams.value.total! >= 2) {
    message.warning('联系管理员增加模型上限数量')
  } else {
    conditionDialogRef.value?.show({
      type: 'add',
      nameList: conditionList.value.map(conditionItem => conditionItem.name)
    })
  }
}

/**
 * 删除潜客模型
 * @description 删除指定ID的潜客模型，删除前会弹出确认框
 * @param id 要删除的潜客模型ID
 */
function handleDeleteCondition(id: string) {
  Modal.confirm({
    title: '提示',
    content: '是否删除该潜客模型？',
    autoFocusButton: null,
    onOk: async () => {
      try {
        conditionListLoading.value = true
        const { message: msg } = await companyRemoveSearchCondition({ ids: [id] })
        message.success(msg)
        await getData()
        // 判断当前高亮和删除是否同一个，如果是就判断还有无数据，就默认选中第一个，没有就选中高级搜索
        if (activateIndex.value === id) {
          handleSideNavMenuChange(
            conditionList.value.length && conditionList.value[0].id
              ? { type: 'model', label: conditionList.value[0].name, key: conditionList.value[0].id }
              : { type: 'search', label: '高级搜索', key: '高级搜索' }
          )
        }
        conditionListLoading.value = false
      } catch (error) {
        conditionListLoading.value = false
        console.error(error)
      }
    }
  })
}

/**
 * @description: 处理潜客模型编辑或新增后刷新界面
 * @param e 事件参数
 */
function handleConditionRefresh(e: { type: 'add' | 'edit'; id?: string }) {
  getData()
  // 如果是编辑且编辑项是当前高亮的需要刷新界面
  if (e.type === 'edit' && activateIndex.value === e.id) {
    const conditionItem = conditionList.value.find(item => item.id === e.id)
    !isEmpty(conditionItem) && handleSideNavMenuChange({ type: 'model', label: conditionItem?.name, key: conditionItem?.id })
  }
}

// 获取关注企业分组
const { loading: collectLoading, dataList: collectDataList, getData: getCollect } = useRequest(getCustomerCollectGroup, { appType: 'LITE' })
// 新增、修改个人分组模态框引用
const myselfGroupModalRef = useTemplateRef('myselfGroupModalRef')

/**
 * 显示分组模态框
 * @description 用于显示新增或编辑分组的模态框
 * @param title 模态框标题，'新增分组' 或 '编辑分组'
 * @param editItem 编辑时的分组数据
 */
function showGroupModal(title: '新增分组' | '编辑分组', editItem?: getCustomerCollectGroupResponseType) {
  const nameList = collectDataList
    .value!.map(item => item.collectName)
    .filter(item => (title === '编辑分组' ? item !== editItem?.collectName : true)) // 如果是编辑，排除自己
  console.log('nameList: ', nameList)

  myselfGroupModalRef.value?.show(title, nameList, editItem)
}

/**
 * 添加分组
 * @description 点击添加分组按钮时的处理函数，检查分组数量限制并打开新增分组模态框
 */
function addGroup() {
  if (collectDataList.value!.filter(item => item.collectName !== '全部').length >= 6) {
    message.warning('最多可添加6个分组')
    return
  }
  showGroupModal('新增分组')
}

/**
 * 删除分组
 * @description 删除指定的分组，会先检查分组内是否有企业，有则不允许删除
 * @param item 要删除的分组信息
 */
async function deleteGroup(item: getCustomerCollectGroupResponseType) {
  if (item.companyNum !== 0) {
    message.error('删除失败！在执行删除操作前，请确保将该分组中的企业重新分配到其他已存在的分组中')
    return
  }
  Modal.confirm({
    title: '提示',
    content: '是否删除该分组？',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    onOk: async () => {
      try {
        collectLoading.value = true
        const { message: msg } = await customerCollectDelete({ appType: 'LITE', collectId: item.collectId })
        message.success(msg)
        await getCollect()
        collectLoading.value = false
        // 判断当前高亮和删除是否同一个，如果是就判断还有无数据，就默认选中第一个，没有就选中高级搜索
        if (item.collectId === activateIndex.value) {
          handleSideNavMenuChange(
            collectDataList.value?.length && collectDataList.value[0].collectId
              ? { type: 'followCompany', label: collectDataList.value[0].collectName, key: collectDataList.value[0].collectId }
              : { type: 'search', label: '高级搜索', key: '高级搜索' }
          )
        }
      } catch (error) {
        console.error(error)
        collectLoading.value = false
      }
    }
  })
}

// 处理侧边导航菜单点击事件
const router = useRouter()
const route = useRoute()
function handleSideNavMenuChange(item: BaseNavItem) {
  if (['搜索', '潜客模型', '关注的企业'].includes(item.label)) return
  switch (item.type) {
    case 'search':
      router.replace({
        path: '/findCompany/advanceSearch',
        query: { type: item.type, activity: item.label === '我保存的搜索项' ? 'openSavedSearch' : undefined, t: new Date().valueOf() }
      })
      break
    case 'model':
      router.replace({ path: '/findCompany/myModel', query: { type: item.type, id: item.key, t: new Date().valueOf() } })
      break
    case 'followCompany':
      router.replace({ path: '/findCompany/followCompany', query: { type: item.type, collectId: item.key, t: new Date().valueOf() } })
      break
    case 'ignore':
      router.replace({ path: '/findCompany/ignore', query: { type: item.type, id: 'ignore', t: new Date().valueOf() } })
      break

    default:
      break
  }
}

const activateIndex = ref('高级搜索')
function setActivateIndex(queryParams: LocationQuery) {
  const { type, id, collectId } = queryParams
  console.log('type: ', type)
  switch (type) {
    case 'search':
      activateIndex.value = '高级搜索'
      break
    case 'model':
      activateIndex.value = id as string
      break
    case 'followCompany':
      activateIndex.value = collectId as string
      break
    case 'ignore':
      activateIndex.value = '忽略企业'
      break

    default:
      break
  }
}

onMounted(() => {
  setActivateIndex(route.query)

  nextTick(() => {
    const { activity } = route.query
    if (activity === 'openCustomModel') {
      handleAddConditionBtn()
    }
  })
})

onBeforeRouteUpdate(to => {
  setActivateIndex(to.query)
})

// // 用于刷新列表数据的事件
// const { emit: refreshListData } = useEventBus('refreshData')

// // 当前选中的模型ID，只有在find-company-overview-model路由下才会有值
// const activeId = computed(() => {
//   return route.name === 'find-company-overview-model' ? route.params.id : ''
// })

// // 处理点击自定义条件变动
// function handleConditionChange(type: 'recommend' | 'ignore' | string) {
//   console.log('type: ', type)

//   // const preId = route.params.id
//   // router.push({ path: `/findCompany/overview/model/${type}` }).finally(() => {
//   //   const nextId = route.params.id
//   //   // 如果是重复点击相同目标元素则刷新列表页面
//   //   preId === nextId && refreshListData()
//   // })
// }
</script>

<style lang="less" scoped>
.extra {
  height: 30px;
  display: none;
  text-align: right;
}

.conditionItem,
.followGrouping {
  &:hover {
    .extra {
      display: block;
    }
    .num {
      color: #baabff;
    }
  }
}
</style>
