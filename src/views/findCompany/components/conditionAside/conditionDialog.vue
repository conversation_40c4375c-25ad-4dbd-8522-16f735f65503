<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-29 15:11:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 15:17:23
 * @FilePath: /corp-elf-web-consumer/src/views/findCompany/components/conditionAside/conditionDialog.vue
 * @Description: 
-->
<template>
  <a-modal
    forceRender
    :title="title"
    v-model:open="visible"
    width="1200px"
    @cancel="hide"
    @ok="submit"
    :confirmLoading="confirmLoading"
    :okText="userStore.isVip ? '确定' : '开通会员'"
  >
    <a-spin :spinning="loading">
      <a-form :model="saveConditionForm" :rules="rules" ref="saveConditionFormRef">
        <a-form-item label="模型名称" name="name">
          <a-input v-model:value="saveConditionForm.name" style="width: 180px" placeholder="模型名称" />
        </a-form-item>

        <p style="margin-bottom: 8px">筛选范围：</p>
        <searchBox
          ref="searchBoxRef"
          v-model:conditions="saveConditionForm.filterRange"
          :isSetDefaultSearchForm="false"
          condition-btn-text="筛选项"
          :areaLimit="true"
        />
        <!-- :isHaveScore="true" -->

        <div v-for="(item, index) in saveConditionForm.conditionalDimension" :key="index" class="conditionalDimension">
          <a-form-item
            :label="`维度${nzhcn.encodeS(index + 1)}`"
            :name="['conditionalDimension', index, 'name']"
            :rules="{ required: true, message: '请输入', trigger: 'blur' }"
          >
            <div class="flexCenter conditionalDimensionItem">
              <a-input v-model:value="item.name" style="width: 180px" :placeholder="`维度${nzhcn.encodeS(index + 1)}名称`" />
              <a-space class="btns">
                <a :class="['flexCenter', index === 0 ? 'disabledBtn' : '']" @click="handlerBtnClick(index, 'up')">
                  <iconfont-icon icon="icon-chevron-up-circle"></iconfont-icon>
                  上移
                </a>
                <a
                  :class="['flexCenter', index === saveConditionForm.conditionalDimension?.length - 1 ? 'disabledBtn' : '']"
                  @click="handlerBtnClick(index, 'down')"
                >
                  <iconfont-icon icon="icon-chevron-down-circle"></iconfont-icon>
                  下移
                </a>
                <a class="flexCenter" @click="handlerBtnClick(index, 'delete')">
                  <iconfont-icon icon="icon-delete"></iconfont-icon>
                  删除
                </a>
              </a-space>
            </div>
          </a-form-item>

          <searchBox
            ref="conditionalDimensionRef"
            v-model:conditions="item.condition"
            :isHaveScore="true"
            :isSetDefaultSearchForm="false"
            :only-condition="false"
            :defaultScore="5"
            condition-btn-text="条件"
            :areaLimit="true"
          />
        </div>

        <a-button
          type="link"
          class="flexCenter"
          @click="handlerAddConditionGroup"
          :disabled="saveConditionForm.conditionalDimension?.length === 4"
        >
          <iconfont-icon v-show="saveConditionForm.conditionalDimension?.length !== 4" icon="icon-add-circle"></iconfont-icon>
          <span> {{ saveConditionForm.conditionalDimension?.length !== 4 ? '维度' : '最多添加4项维度' }} </span>
        </a-button>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { companyCreateSearchCondition, companyUpdateSearchCondition } from '@/api/api'
import { cloneDeep, isEmpty } from 'lodash-es'
import { ref, computed, nextTick } from 'vue'
import searchBox from '@/components/searchCriteria/index.vue'
import { message } from 'ant-design-vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import nzhcn from 'nzh/cn'
import { searchFormItemType } from '~/types/common/searchForm'
import { CompanySearchConditionDto } from '~/types/api/company/showSearchConditionV2'
import { useUserStore } from '@/store'
import { useEventBus } from '@vueuse/core'

interface conditionFormType {
  id?: string
  name: string
  filterRange: searchFormItemType[]
  conditionalDimension: Array<{
    name: string
    condition: searchFormItemType[]
  }>
}
const userStore = useUserStore()
const { emit: openVipModal } = useEventBus('openVipModal')
const modelType = ref('add')
const repeatNameList = ref<string[]>()
const visible = ref(false)
const loading = ref(false)
const confirmLoading = ref(false)
const saveConditionForm = ref<conditionFormType>({
  name: '',
  filterRange: [],
  conditionalDimension: []
})
// const defaultSearchForm = ref([])
const rules = ref({
  name: [{ required: true, message: '请输入', trigger: 'blur' }]
})
const emit = defineEmits(['refreshConditionList'])

const saveConditionFormRef = ref()
const searchBoxRef = ref()
const conditionalDimensionRef = ref()

const title = computed(() => `${modelType.value === 'add' ? '新增' : '编辑'}模型`)

async function show({ type = 'add', item, nameList = [] }: { type: 'add' | 'edit'; nameList: string[]; item?: CompanySearchConditionDto }) {
  console.log('nameList: ', nameList)
  console.log('item: ', item)
  repeatNameList.value = nameList
  modelType.value = type
  visible.value = true

  await nextTick()
  // setTimeout(() => {
  // this.$refs.searchBoxRef.isReady
  console.log('this.$refs.searchBoxRef.isReady: ', searchBoxRef.value)

  if (modelType.value === 'add') {
    // saveConditionForm.value.filterRange.push({
    //   uuid: randomUUID(),
    //   isShowSearchFieldBox: false,
    //   fieldIndex: 'open_status',
    //   searchIndex: 'open_status',
    //   fieldName: '经营状态',
    //   fieldType: 'ENUM_1',
    //   path: ['基础信息', '工商信息', '经营状态'],
    //   conditionType: 'ANY',
    //   conditionValue: true,
    //   dates: [],
    //   nums: [],
    //   values: ['存续', '迁入', '迁出', '在业']
    // })
    handlerAddConditionGroup() // 默认添加一条条件维度
  } else {
    const temp = cloneDeep(item)
    console.log('temp: ', temp)
    saveConditionForm.value.id = temp!.id
    saveConditionForm.value.name = temp!.name
    const { commonFilter, recommendFilters } = temp!.searchContent

    // 筛选条件
    saveConditionForm.value.filterRange = searchBoxRef.value.translateSearchDataToConditionsRaw(commonFilter.conditions)
    // 维度
    saveConditionForm.value.conditionalDimension = recommendFilters.map(item => ({
      name: item.name,
      condition: searchBoxRef.value.translateSearchDataToConditionsRaw(item.conditions)
    }))
    console.log('saveConditionForm.value: ', saveConditionForm.value)
  }
}
function hide() {
  searchBoxRef.value.resetForm()
  saveConditionFormRef.value.resetFields()
  visible.value = false
  saveConditionForm.value.filterRange = []
  saveConditionForm.value = { name: undefined, filterRange: [], conditionalDimension: [] }
  // this.$refs.searchBoxRef.setDefaultSearchForm()
}
async function submit() {
  try {
    if (!userStore.isVip) {
      openVipModal()
      return
    }
    console.log('saveConditionForm.value.conditionalDimension: ', saveConditionForm.value)

    await saveConditionFormRef.value.validate()
    const { conditions: commonFilter } = await searchBoxRef.value.submit()
    console.log('commonFilter: ', commonFilter)
    if (isEmpty(commonFilter)) {
      message.warning('请添加条件')
      return
    }

    if (repeatNameList.value?.includes(saveConditionForm.value.name!)) {
      message.warning('该模型名称已存在，请重新输入')
      return
    }

    if (saveConditionForm.value.conditionalDimension?.length === 0) {
      message.warning('请输入并选择维度')
      return
    }

    const conditionalDimensionList = conditionalDimensionRef.value || []
    const recommendFilters: Array<any> = []
    for (let index = 0; index < conditionalDimensionList.length; index++) {
      const element = conditionalDimensionList[index]
      const { conditions } = await element.submit()
      const recommendFilter = {
        name: saveConditionForm.value.conditionalDimension[index].name,
        conditions: conditions.filter(item => item.searchIndex !== 'open_status')
      }
      recommendFilters.push(recommendFilter)
    }

    // console.log('conditions: ', conditions)
    // // 准备参数
    const params = {
      ...saveConditionForm.value,
      searchContent: { commonFilter: { conditions: commonFilter }, recommendFilters },
      version: 'V_3_0',
      type: 'RECOMMEND_SEARCH'
    }
    delete params.filterRange
    delete params.conditionalDimension
    console.log('params: ', params)
    loading.value = true
    confirmLoading.value = true
    // 判断掉用接口类型
    const reqApi = modelType.value === 'add' ? companyCreateSearchCondition : companyUpdateSearchCondition
    // 掉用接口
    const { result } = await reqApi(params)
    console.log('result: ', result)
    loading.value = false
    confirmLoading.value = false
    message.success('保存成功')
    emit('refreshConditionList', { type: modelType.value, id: params.id })
    hide()
  } catch (error) {
    console.error(error)

    loading.value = false
    confirmLoading.value = false
    message.error('保存失败')
  }
}

function handlerAddConditionGroup() {
  saveConditionForm.value.conditionalDimension?.push({ name: undefined, condition: [] })
}

function handlerBtnClick(index: number, type: 'up' | 'down' | 'delete') {
  if ((index === 0 && type === 'up') || (index === saveConditionForm.value.conditionalDimension?.length - 1 && type === 'down')) {
    return
  }

  let tempItem, currentItem
  switch (type) {
    case 'up':
      tempItem = saveConditionForm.value.conditionalDimension[index - 1]
      currentItem = saveConditionForm.value.conditionalDimension[index]
      saveConditionForm.value.conditionalDimension[index - 1] = currentItem
      saveConditionForm.value.conditionalDimension[index] = tempItem
      break

    case 'down':
      tempItem = saveConditionForm.value.conditionalDimension[index + 1]
      currentItem = saveConditionForm.value.conditionalDimension[index]
      saveConditionForm.value.conditionalDimension[index + 1] = currentItem
      saveConditionForm.value.conditionalDimension[index] = tempItem
      break

    case 'delete':
      saveConditionForm.value.conditionalDimension?.splice(index, 1)
      break

    default:
      break
  }
}

defineExpose({ show })
</script>

<style lang="less" scoped>
.addConditionGroup {
  display: inline-flex;
  align-items: center;
}
.conditionalDimension {
  .conditionalDimensionItem {
    justify-content: space-between;
    .disabledBtn {
      cursor: no-drop !important;
      color: #ccc;
      &:hover {
        color: #ccc !important;
      }
    }
  }
  .btns {
    visibility: hidden;
  }
  &:hover {
    .btns {
      visibility: inherit;
    }
  }
}
</style>
