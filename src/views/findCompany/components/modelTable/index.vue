<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 18:19:51
 * @FilePath: /corp-elf-web-consumer/src/views/findCompany/components/modelTable/index.vue
 * @Description: 找企业-潜客推荐
-->
<template>
  <Table
    title="企业列表"
    :dataSource="listData"
    :columns="tableColumns"
    :loading="loading"
    :pagination="paginationParams"
    :tablePageError="tablePageError"
    :isHaveCheckBox="props.id !== 'ignore'"
    @selectedRowsChange="handlerSelectedRowsChange"
  >
    <template #button>
      <a-button v-show="checkCompanyList.length !== 0 || props.id !== 'ignore'" @click="batchAddFollow">关注</a-button>
    </template>
    <template #sort>
      <a-dropdown trigger="click">
        <template #overlay>
          <a-menu>
            <a-menu-item v-for="(item, index) in sortOptions" :key="index" @click="handlerSortTypeChange(item)">
              <span :class="{ 'color-#6553ee': sortType === item.value }"> {{ item.label }} </span>
            </a-menu-item>
          </a-menu>
        </template>
        <a :style="{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }">
          <iconfontIcon icon="icon-order-descending" style="font-size: 24px"></iconfontIcon>
        </a>
      </a-dropdown>
    </template>
    <template #extra>
      <div style="height: 32px; line-height: 32px">
        <filterForm
          v-model:value="filterFormState"
          :config="filterFormConfig"
          :disabled="tablePageError.isError"
          @ok="handlerFilterFun"
          @reset="handlerFilterReset"
        />
      </div>
    </template>

    <template #actions="rowItem">
      <moreIcon v-show="rowItem.visibility" :menuList="menuList" @click="menuItem => handlerMoreIconClick(menuItem, rowItem)" />
    </template>

    <template #popupScore="{ text, record }">
      <a @click="showScoreDetail(record)">{{ text }}</a>
    </template>

    <template #emptyText>
      <a-empty
        :image="empty"
        :image-style="{
          height: '164px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }"
      >
        <template #description>
          <div class="emptyDescription">
            <template v-if="isRunModel">
              <p>评分正在计算中...</p>
              <p>这可能需要5~30分钟时间，这取决于条件的复杂程度。</p>
            </template>
            <template v-else> 暂无数据 </template>
          </div>
        </template>
      </a-empty>
    </template>
  </Table>

  <addFollowModal ref="addFollowModalRef" @refresh="getList" />
  <scoreDetail ref="scoreDetailRef" />
</template>

<script setup lang="ts" name="potentialCustomers-recommend">
import { companyAddRecommendIgnore, companyListRecommendIgnore, companyListRecommendV3, companyRemoveRecommendIgnore } from '@/api/api'
import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue'
import { cloneDeep, has } from 'lodash-es'
import filterForm, { filterFromProps } from '@/components/filterForm/index.vue'
import { useTransformCascaderData } from '@/hooks/useTransformCascaderData'
import industryClassificationOptions from '@/assets/json/industryClassification.json'
import provincesAndRegionsOptions from '@/assets/json/provincesAndRegions.json'
import Table from '@comp/table/index'
import moreIcon from '@/components/actionIcon/more.vue'
import scoreDetail from '../scoreDetail/index.vue'
import { message } from 'ant-design-vue'
import { useSearchField, useUserStore } from '@/store'
import iconfontIcon from '@/components/tools/iconfontIcon'
import annualRevenueMap from '@/assets/json/annualRevenues.json'
import empty from '@/assets/empty.svg'
import { companyCardType } from '~/types/common/companyCardType'
import { CompanySearchResType } from '~/types/common/companySearch'
import useRequest from '@/hooks/useRequest'
import { CascaderProps } from 'ant-design-vue/es/vc-cascader'
import addFollowModal from '@/components/addFollowModal/index.vue'
import { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import { useRoute } from 'vue-router'

// const recommendId = ref<'recommend' | 'ignore' | string>()
const searchFieldStore = useSearchField()
const props = defineProps<{ id: 'recommend' | 'ignore' | string }>()
const route = useRoute()

// // 监听路由变化，刷新数据
// onBeforeRouteUpdate(to => {
//   initData(to.params.id as string)
// })

// const bus = useEventBus<string>('refreshData')
// bus.on(() => {
//   handlerCurrentChange(1, paginationParams.value.pageSize)
// })

function initData() {
  console.log('props.id: ', props.id)
  sortType.value = ['recommend', 'ignore'].includes(props.id) ? 'powerful_rank_score' : 'recommend_score' // 自定义推荐模型的排序按照“推荐指数”，其他按照实力指数
  handlerCurrentChange(1, paginationParams.value.pageSize)
}

const isCustomerModel = computed(() => !['recommend', 'ignore'].includes(props.id))

const loading = ref(false)
const isRunModel = ref(false)
const listData = ref<CompanySearchResType[]>([])
const tableColumns = computed(() => {
  const colList = [
    { title: '企业', slotName: 'companyCard' },
    { title: '所属地区', dataIndex: 'address', slotName: 'address', width: '13%', ellipsis: true },
    { title: '成立日期', dataIndex: 'startDate', width: '13%', ellipsis: true },
    { title: '营收规模', dataIndex: 'annualRevenue', slotName: 'annualRevenue', width: '13%', ellipsis: true },
    { title: '实力指数', dataIndex: 'powerfulRankScore', width: '8%', ellipsis: true },
    { title: '推荐指数', dataIndex: 'popupScore', slotName: 'popupScore', width: '8%', ellipsis: true, align: 'center' },
    { title: '', slotName: 'actions', width: 100, ellipsis: true }
  ]
  if (!isCustomerModel.value) {
    colList.splice(5, 1)
  }
  return colList
})
//  分页参数
const paginationParams = ref({
  current: 1, // 当前页数
  pageSize: 30, // 每页显示条目个数
  total: 0, // 总条目数
  showSizeChanger: true, // 显示pagesize修改
  showQuickJumper: false, // 显示快速跳转
  hideOnSinglePage: true, // 只有一页时是否隐藏
  pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
  responsive: true, // 当 size 未指定时，根据屏幕宽度自动调整尺寸
  showTotal: (total: number) => `共 ${total} 条`,
  onChange: handlerCurrentChange
})

const tablePageError = ref({
  isError: false,
  errorMsg: '此帐号无权获取更多数据，请联系商务开通相关权限。'
})
// 排序方法
// const sortType = ref<'powerful_rank_score' | 'annual_revenue'>('powerful_rank_score')
// watch(sortType, () => handlerSizeChange(1, pageParams.value.pageSize))

// 排序方法
interface SortItemType {
  label: string
  value: SortValueType
}
type SortValueType = 'powerful_rank_score' | 'annual_revenue' | 'recommend_score'
const sortType = ref<SortValueType>('powerful_rank_score')
const sortOptions = computed<SortItemType[]>(() => {
  const sortList: SortItemType[] = [
    { label: '营收规模', value: 'annual_revenue' },
    { label: '实力指数', value: 'powerful_rank_score' },
    { label: '推荐指数', value: 'recommend_score' }
  ]
  if (!isCustomerModel.value) {
    sortList.splice(2, 1)
  }
  return sortList
})

function handlerSortTypeChange(_sortType: SortItemType) {
  console.log('_sortType: ', _sortType)
  sortType.value = _sortType.value!
  handlerCurrentChange(1, paginationParams.value.pageSize)
}

// 获取推荐企业
async function getList() {
  try {
    loading.value = true

    const params = {
      filter: { ...filterParams.value, orderColumns: [{ columnName: sortType.value, asc: false }] },
      pageNo: paginationParams.value.current,
      pageSize: paginationParams.value.pageSize,
      recommendId: isCustomerModel.value ? props.id : null
    }

    console.log('params: ', params)
    const reqApi = props.id === 'ignore' ? companyListRecommendIgnore : companyListRecommendV3
    const { result, code } = await reqApi(params)
    isRunModel.value = code === 'WAITING' // 用于判断用户自定义潜客模型是否在生成中
    if (code === 'WAITING') {
      listData.value = []
      paginationParams.value.total = 0
    } else {
      const { total = 0, data = [] } = result
      listData.value = data
      tablePageError.value.isError = false
      paginationParams.value.total = total > 10000 ? 10000 : total
    }
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
    if ((error as Error).message.includes('此帐号无权获取更多数据，请联系商务开通相关权限。')) {
      tablePageError.value.isError = true
    }
  }
}
// 分页改变
function handlerCurrentChange(pageNo: number, pageSize: number) {
  paginationParams.value.current = pageNo
  paginationParams.value.pageSize = pageSize
  getList()
}

// 筛选器相关
const filterFormConfig = computed(() => {
  const baseFilter: filterFromProps['config'] = [
    {
      label: '企业名称',
      key: 'searchContent',
      formItemType: 'input',
      placeholder: '工商名称'
    },
    {
      label: '商瞳行业',
      key: 'tags',
      formItemType: 'cascader',
      mode: 'multiple',
      optionsData: searchFieldStore.industryList
    },
    {
      label: '工商行业',
      key: 'industryClassification',
      formItemType: 'cascader',
      optionsData: industryClassificationOptions,
      mode: 'multiple'
    },
    {
      label: '所属地区',
      key: 'provincesAndRegions',
      formItemType: 'cascader',
      optionsData: provincesAndRegionsOptions,
      mode: 'multiple',
      placeholder: '省/市/区'
    },
    {
      label: '成立年限',
      key: 'establishmentTime',
      formItemType: 'rangeInputNumber',
      max: Infinity,
      placeholder: ['开始年限', '结束年限']
    },
    {
      label: '营收规模',
      key: 'annualRevenues',
      formItemType: 'select',
      mode: 'multiple',
      optionsData: annualRevenueMap
    },
    {
      label: '实力指数',
      key: 'rankPowerfulScore',
      formItemType: 'rangeInputNumber',
      max: Infinity,
      placeholder: ['最低分', '最高分']
    },
    {
      label: '推荐指数',
      key: 'recommendScore',
      formItemType: 'rangeInputNumber',
      placeholder: ['最低分', '最高分']
    }
    // {
    //   label: '人员规模',
    //   key: 'staffSizes',
    //   formItemType: 'select',
    //   mode: 'multiple',
    //   optionsData: [
    //     { label: '小于50人', value: '小于50人' },
    //     { label: '50-99人', value: '50-99人' },
    //     { label: '100-499人', value: '100-499人' },
    //     { label: '500-999人', value: '500-999人' },
    //     { label: '1000-4999人', value: '1000-4999人' },
    //     { label: '5000-9999人', value: '5000-9999人' },
    //     { label: '1万人以上', value: '1万人以上' }
    //   ]
    // },
    // {
    //   label: '客户范围',
    //   key: 'companyRange',
    //   formItemType: 'select',
    //   optionsData: [
    //     { label: '全部', value: 'ALL_COMPANY' },
    //     { label: '未领取', value: 'NON_CUSTOMER_COMPANY' },
    //     { label: '我的客户', value: 'MINE_CUSTOMER_COMPANY' },
    //     { label: '企业客户', value: 'ALL_CUSTOMER_COMPANY' }
    //   ]
    // }
  ]
  if (!isCustomerModel.value) {
    baseFilter.splice(7, 1)
  }
  return baseFilter
})
const filterFormState = ref<Record<string, any>>({}) // 筛选默认值
// 筛选确定回掉
function handlerFilterFun() {
  handlerCurrentChange(1, paginationParams.value.pageSize)
}
// 筛选重置回掉
function handlerFilterReset() {
  filterFormState.value = {}
  handlerCurrentChange(1, paginationParams.value.pageSize)
}
// 准备筛选器的参数

const filterParams = computed(() => {
  const _filterFormState = cloneDeep(filterFormState.value)

  let returnData = {
    ..._filterFormState
    // ...establishmentTime,
    // ...rankPowerfulScore,
    // tags,
    // industryClassification,
    // provincesAndRegions
  }
  // 成立时间
  let establishmentTime = {}
  if (has(_filterFormState, 'establishmentTime')) {
    establishmentTime = {
      minEstablishmentTime: _filterFormState.establishmentTime[0] || null,
      maxEstablishmentTime: _filterFormState.establishmentTime[1] || null
    }
    delete returnData.establishmentTime
    returnData = {
      ...returnData,
      ...establishmentTime
    }
  }
  // 实力指数
  let rankPowerfulScore = {}
  if (has(_filterFormState, 'rankPowerfulScore')) {
    rankPowerfulScore = {
      minRankPowerfulScore: _filterFormState.rankPowerfulScore[0] || null,
      maxRankPowerfulScore: _filterFormState.rankPowerfulScore[1] || null
    }
    delete returnData.rankPowerfulScore
    returnData = {
      ...returnData,
      ...rankPowerfulScore
    }
  }
  // 推荐指数
  let recommendScore = {}
  if (has(_filterFormState, 'recommendScore')) {
    recommendScore = {
      minRecommendScore: _filterFormState.recommendScore[0] || null,
      maxRecommendScore: _filterFormState.recommendScore[1] || null
    }
    delete returnData.recommendScore
    returnData = {
      ...returnData,
      ...recommendScore
    }
  }
  // 商瞳行业
  let tags
  if (has(_filterFormState, 'tags')) {
    tags = useTransformCascaderData(_filterFormState.tags, searchFieldStore.industryList as unknown as CascaderProps['options']).map(
      item => item.label
    )
    returnData = {
      ...returnData,
      tags
    }
  }
  // 工商行业
  let industryClassification
  if (has(_filterFormState, 'industryClassification')) {
    industryClassification = useTransformCascaderData(_filterFormState.industryClassification, industryClassificationOptions).map(
      item => item.value
    )
    returnData = {
      ...returnData,
      ...{ industryClassification }
    }
  }
  // 所属地区
  let provincesAndRegions
  if (has(_filterFormState, 'provincesAndRegions')) {
    provincesAndRegions = useTransformCascaderData(_filterFormState.provincesAndRegions, provincesAndRegionsOptions).map(item => item.value)
    returnData = {
      ...returnData,
      ...{ provincesAndRegions }
    }
  }

  console.log('returnData: ', returnData)
  return returnData
})
// 指派、领取、更多按钮相关
const userStore = useUserStore()
const userInfo = userStore.getUserInfo
const addFollowModalRef = ref<InstanceType<typeof addFollowModal>>()
const menuList = computed(() => {
  const baseMenu =
    props.id !== 'ignore'
      ? [
          { title: '关注', key: 'addFollow' },
          { title: '不再推荐', key: 'noLongerRecommended' }
        ]
      : [{ title: '移除', key: 'remove' }]
  return baseMenu
})
// 更多按钮点击事件
function handlerMoreIconClick(menuItem: MenuInfo, rowItem: { text: string; record: companyCardType }) {
  switch (menuItem.key) {
    case 'addFollow':
      addFollowModalRef.value?.show({ companyId: rowItem.record.cid, companyName: rowItem.record.entName })
      break
    case 'noLongerRecommended':
      loading.value = true
      companyAddRecommendIgnore({ companyId: rowItem.record.cid, userId: userInfo.id })
        .then(({ message: msg }) => {
          message.success(msg)
          getList()
          loading.value = false
        })
        .catch(err => {
          console.error(err)
          loading.value = false
        })
      break
    case 'remove':
      loading.value = true
      companyRemoveRecommendIgnore({ id: rowItem.record.id, userId: userInfo.id })
        .then(({ message: msg }) => {
          message.success(msg)
          getList()
          loading.value = false
        })
        .catch(err => {
          console.error(err)
          loading.value = false
        })
      break

    default:
      break
  }
}

// 选中企业相关
const checkCompanyList = ref<companyCardType[]>([])
function handlerSelectedRowsChange(_keys: string, rows: companyCardType[]) {
  checkCompanyList.value = rows
}

// 显示推荐指数详情
const scoreDetailRef = useTemplateRef('scoreDetailRef')
function showScoreDetail(record) {
  scoreDetailRef.value?.onOpen(record.popupDto)
}

// 批量关注
function batchAddFollow() {
  addFollowModalRef.value?.show(checkCompanyList.value.map(item => ({ companyId: item.cid, companyName: item.entName })))
}

onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped></style>
