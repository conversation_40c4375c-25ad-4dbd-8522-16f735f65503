<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 17:17:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-22 15:13:00
 * @FilePath: /corp-elf-web-consumer/src/views/home/<USER>/activityList.vue
 * @Description: 行业热点
-->
<template>
  <a-card :bordered="false" :bodyStyle="{ padding: 0, overflow: 'hidden' }" class="dynamicNews" style="width: 100%">
    <template #title>
      <p class="inline-block">关注活动</p>
      <a-divider type="vertical" />
      <a-segmented v-model:value="activeKey" :options="['高管言论', '企业动态']" @change="handlerActiveKeyChange" />
    </template>

    <template #extra v-if="activeKey === '企业动态'">
      <a-select
        size="small"
        v-model:value="activeCustomerGroupKey"
        :options="customerGroupData"
        @change="refresh"
        :bordered="false"
        :fieldNames="{ label: 'collectName', value: 'collectId' }"
        :dropdownMatchSelectWidth="120"
      ></a-select>
    </template>

    <a-config-provider>
      <template #renderEmpty>
        <div class="text-center">
          <empty>
            <template #description>
              <p class="fs-16px my16px">暂无内容</p>
              <p class="block mb16px color-#ccc">开始添加您的关注目标</p>
              <a-button type="primary" shape="round" class="inline-flex items-center justify-center" @click="addFollowCompanyRef?.show()">
                <template #icon><PlusOutlined /></template>
                关注企业
              </a-button>
            </template>
          </empty>
        </div>
      </template>
      <a-tabs v-model:activeKey="activeKey" :animated="{ inkBar: true, tabPane: true }">
        <template #renderTabBar> </template>

        <a-tab-pane key="高管言论">
          <a-list
            class="list"
            item-layout="horizontal"
            :data-source="executiveDataList"
            :loading="{ spinning: infiniteLoading, tip: '加载中...' }"
          >
            <template #loadMore>
              <div v-if="executiveDataList.length > 0" class="loadMore py-8px">
                <div
                  v-if="isEmpty(userStore.getToken)"
                  class="inline-flex items-center justify-center hoverPrimaryColor endText"
                  @click="showLoginModal"
                >
                  <iconfontIcon icon="icon-lock-on" />
                  <p class="mx4px">登录即可获取更多数据</p>
                  <iconfontIcon icon="icon-chevron-down" />
                </div>
                <div v-else v-intersection-observer="handlerIntersectionObserver" class="endText">
                  <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                  <p v-else>没有更多了</p>
                </div>
              </div>
            </template>
            <template #renderItem="{ item }">
              <executiveSaidCard :key="item.url" :item="item" class="background-#ffffff00! hover:bg-#f4f0ff! hover:cursor-pointer" />
            </template>
          </a-list>
        </a-tab-pane>

        <a-tab-pane key="企业动态" forceRender>
          <a-list
            class="list"
            item-layout="horizontal"
            :data-source="companyDataList"
            :loading="{ spinning: infiniteLoading, tip: '加载中...' }"
          >
            <template #loadMore>
              <div v-if="companyDataList.length > 0" class="loadMore py-8px">
                <div
                  v-if="isEmpty(userStore.getToken)"
                  class="inline-flex items-center justify-center hoverPrimaryColor endText"
                  @click="showLoginModal"
                >
                  <iconfontIcon icon="icon-lock-on" />
                  <p class="mx4px">登录即可获取更多数据</p>
                  <iconfontIcon icon="icon-chevron-down" />
                </div>
                <div v-else v-intersection-observer="handlerIntersectionObserver" class="endText">
                  <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                  <p v-else>没有更多了</p>
                </div>
              </div>
            </template>
            <template #renderItem="{ item }">
              <newsCard :key="item.oriUrl" :newsData="item" @click="newsCardClick(item)" />
            </template>
          </a-list>
        </a-tab-pane>
      </a-tabs>
    </a-config-provider>
  </a-card>

  <addFollowCompany ref="addFollowCompanyRef" :collect-id="activeCustomerGroupKey" @refresh="refresh" modal-type="1" />
</template>

<script setup lang="ts">
import { industryHotNews, executiveSaidList, getCustomerCollectGroup } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { useUserStore } from '@/store'
import { computed, ref, onMounted } from 'vue'
import { vIntersectionObserver } from '@vueuse/components'
import newsCard from '@/components/newsCard/index.vue'
import { HotNewsRequest, HotNewsResponse } from '~/types/api/industry/hotNews'
import empty from '@/components/empty/index.vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import addFollowCompany from '@comp/addFollowModal/index.vue'
import { isEmpty } from 'lodash-es'
import showLoginModal from '@/components/loginModal'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { executiveSaidListReqType } from '~/types/api/executiveSaid/executiveSaidList'
import executiveSaidCard from '@/components/executiveSaidCard/index.vue'
import { getCustomerCollectGroupResponseType } from '~/types/api/customer/getCustomerCollectGroup'

const userStore = useUserStore()
// const newsDetailModalRef = ref()

const addFollowCompanyRef = ref<InstanceType<typeof addFollowCompany>>() // 添加关注企业弹窗
const activeKey = ref<'企业动态' | '高管言论'>('高管言论') // 当前选中的分组类型
function handlerActiveKeyChange() {
  // 判断切换的类型是否需要刷新
  if (
    (activeKey.value === '企业动态' && companyDataList.value.length === 0) ||
    (activeKey.value === '高管言论' && executiveDataList.value.length === 0)
  ) {
    refresh()
  }
}

const activeCustomerGroupKey = ref<string>('-1') // 当前选中的分组类型
const customerGroupData = ref<getCustomerCollectGroupResponseType[]>([{ collectName: '全部', collectId: '-1', companyNum: 0 }])
// 获取关注企业类型列表
async function getCustomerGroup() {
  try {
    const { result } = await getCustomerCollectGroup({ appType: 'LITE' })
    console.log('result: ', result)
    customerGroupData.value = result
  } catch (error) {
    console.error(error)
  }
}

type executiveParamsType = Pick<executiveSaidListReqType, 'showPostType' | 'allFlag'>
// 企业动态参数
const companyParams = computed<HotNewsRequest>(() => ({
  collectId: activeCustomerGroupKey.value,
  timeRange: 'ALL'
}))
// 高管言论参数
const executiveParams: executiveParamsType = { showPostType: 1, allFlag: true }

const {
  dataList: companyDataList,
  loading: companyLoading,
  noMore: companyNoMore,
  onLoadMore: companyOnLoadMore,
  refresh: companyRefresh
} = useInfiniteLoading(industryHotNews, companyParams, { immediateReqData: false })

const {
  dataList: executiveDataList,
  loading: executiveLoading,
  noMore: executiveNoMore,
  onLoadMore: executiveOnLoadMore,
  refresh: executiveRefresh
} = useInfiniteLoading(executiveSaidList, executiveParams)

const infiniteLoading = computed(() => (activeKey.value === '企业动态' ? companyLoading.value : executiveLoading.value))
const noMore = computed(() => (activeKey.value === '企业动态' ? companyNoMore.value : executiveNoMore.value))
const onLoadMore = () => (activeKey.value === '企业动态' ? companyOnLoadMore() : executiveOnLoadMore())
const refresh = () => (activeKey.value === '企业动态' ? companyRefresh() : executiveRefresh())

// 滚动到界面底部回调方法
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !infiniteLoading.value && !noMore.value) {
    onLoadMore()
  }
}

function newsCardClick(item: HotNewsResponse) {
  window.open(item.oriUrl, '_blank')
}

onMounted(() => {
  getCustomerGroup()
})
</script>

<style lang="less" scoped>
.dynamicNews {
  :deep(.ant-select-selector) {
    color: #999;
  }
}
</style>
