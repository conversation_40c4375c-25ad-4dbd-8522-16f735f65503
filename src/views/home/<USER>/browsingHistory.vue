<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-11 14:12:20
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-15 14:43:41
 * @FilePath: /corp-elf-web-consumer/src/views/home/<USER>/browsingHistory.vue
 * @Description: 
-->
<template>
  <a-card v-if="dataList?.length" :bordered="false" title="最近浏览" :bodyStyle="{ padding: '8px 16px 16px' }" :loading="loading">
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
      <div v-for="item in dataList" :key="item.cid" class="p8px itemBorderBottom">
        <!-- 公司 -->
        <template v-if="!has(item, 'executiveId')">
          <p class="color-#000000E0 ellipsis-1 hoverPrimaryColor fs-16px" @click="openCompanyInfo(item)">
            {{ item.companyName }}
          </p>
        </template>
        <!-- 高管 -->
        <template v-else>
          <a-space>
            <p class="color-#000000E0 ellipsis-1 hoverPrimaryColor fs-16px" @click="openExecutiveInfo(item)">
              {{ item.executiveName }}
            </p>
            <span class="color-#7F7F7F hoverPrimaryColor ellipsis" @click="openCompanyInfo(item)">{{ item.companyName }}</span>
          </a-space>
        </template>
      </div>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { indexListRecentBrowsing } from '@/api/api'
import { chunk, has } from 'lodash-es'
import { useRouter } from 'vue-router'
import { listRecentBrowsingResType } from '~/types/api/company/listRecentBrowsingResType'
import useRequest from '@/hooks/useRequest'

const { loading, dataList } = useRequest(
  indexListRecentBrowsing,
  {},
  {
    transformResult: res => {
      return chunk(res, 5)[0] || []
    }
  }
)

const router = useRouter()
function openCompanyInfo(item: listRecentBrowsingResType) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.cid,
      companyName: item.companyName
    }
  })
}

function openExecutiveInfo(item: listRecentBrowsingResType) {
  router.push({
    name: 'executiveComments-detail',
    path: '/executiveComments/detail',
    query: { executiveId: item.executiveId, executiveName: item.executiveName }
  })
}
</script>

<style lang="less" scoped>
.itemBorderBottom {
  border-bottom: 1px solid #eeeeee;
}
</style>
