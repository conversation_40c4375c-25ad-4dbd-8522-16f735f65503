<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-11 11:40:04
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-15 15:27:30
 * @FilePath: /corp-elf-web-consumer/src/views/home/<USER>/hotData.vue
 * @Description: 
-->
<template>
  <a-card :bordered="false" style="width: 100%; overflow: hidden">
    <template #title>
      <p class="inline-block">热门</p>
      <a-divider type="vertical" />
      <a-segmented v-model:value="activeKey" :options="['高管', '企业']" />
    </template>

    <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
      <a-tabs v-model:activeKey="activeKey" :animated="{ inkBar: true, tabPane: true }">
        <template #renderTabBar> </template>

        <a-tab-pane key="高管">
          <div class="h-380px">
            <a-row :gutter="[16, 16]" class="h100%">
              <a-col
                v-for="(item, index) in executiveDataList"
                :key="index"
                @click="openExecutiveInfo(item)"
                :span="8"
                class="flex flex-direction-column items-center justify-center cursor-pointer executiveItem"
              >
                <a-avatar :size="72" :src="item.imgUrl">
                  <template #icon><UserOutlined /></template>
                </a-avatar>
                <p class="mt-8px fs-16px color-#000000E0">{{ item.executiveName }}</p>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <a-tab-pane key="企业" forceRender>
          <div class="h-380px">
            <a-table size="small" :dataSource="companyDataList" :columns="companyColumns" :loading="loading" :pagination="false">
              <template #bodyCell="{ text, column, record }: { text: string, column: TableColumnProps, record: listActiveCompanyResType }">
                <template v-if="column.dataIndex === 'companyName'">
                  <span class="color-#000000E0 ellipsis hoverPrimaryColor" @click="openCompanyInfo(record)">
                    {{ record.companyName }}
                  </span>
                </template>
                <template v-if="column.dataIndex === 'num'">
                  <span :style="{ color: theme.getColorPrimary }">
                    {{ text ? text : '-' }}
                  </span>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { executiveHot, executiveSaidListActiveCompany } from '@/api/api'
import { computed, onMounted, ref } from 'vue'
import { useThemeStore } from '@/store'
import useRequest from '@/hooks/useRequest'
import { useRouter } from 'vue-router'
import { executiveHotResType } from '~/types/api/executiveSaid/executiveHot'
import { listActiveCompanyResType } from '~/types/api/executiveSaid/listActiveCompany'
import { TableColumnProps } from 'ant-design-vue'
import { UserOutlined } from '@ant-design/icons-vue'
import { chunk } from 'lodash-es'

const theme = useThemeStore()

const activeKey = ref<'企业' | '高管'>('高管')

const companyColumns = [
  { title: '企业', dataIndex: 'companyName', ellipsis: true },
  { title: '言论次数', dataIndex: 'num', width: 80, align: 'right' }
]

const { loading: companyLoading, dataList: companyDataList } = useRequest(
  executiveSaidListActiveCompany,
  { followCompany: false },
  {
    transformResult: res => {
      return chunk(res, 8)[0] || []
    }
  }
)
const { loading: executiveLoading, dataList: executiveDataList } = useRequest(executiveHot)

const loading = computed(() => companyLoading.value || executiveLoading.value)

const router = useRouter()
function openCompanyInfo(item: listActiveCompanyResType) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: { companyId: item.companyUniId, companyName: item.companyName }
  })
}

function openExecutiveInfo(item: executiveHotResType) {
  router.push({
    name: 'executiveComments-detail',
    path: '/executiveComments/detail',
    query: { executiveId: item.executiveId, executiveName: item.executiveName }
  })
}

onMounted(() => {})
</script>

<style lang="less" scoped>
.executiveItem {
  p {
    &:hover {
      color: var(--g-primary-hover-color);
    }
  }
}
</style>
