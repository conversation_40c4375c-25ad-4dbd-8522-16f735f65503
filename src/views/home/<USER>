<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-10 14:22:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 15:10:51
 * @FilePath: /corp-elf-web-consumer/src/views/home/<USER>
 * @Description: 
-->
<template>
  <div class="home_container">
    <a-row :gutter="16" :wrap="false">
      <a-col flex="256px">
        <a-affix :offset-top="80">
          <NavCard class="mb-16px" />

          <router-link to="/invite">
            <img src="@/assets/images/invite.png" class="w-80% mx-auto" />
            <p class="mt-8px fs-18px text-center">邀请得现金</p>
          </router-link>
        </a-affix>
      </a-col>

      <a-col flex="auto">
        <ActivityList />
      </a-col>

      <a-col flex="400px">
        <a-space :size="16" direction="vertical">
          <a-alert type="info" class="">
            <template #description>
              <a-space direction="vertical" style="width: 100%">
                <h3 class="fs-18px fw-500">俯瞰商业全局，洞察先机</h3>
                <ul class="fs-16px">
                  <li class="mb8px">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" /> 精准查找目标企业，挖掘潜在客户
                  </li>
                  <li class="mb8px">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
                    追踪高管的观点，洞悉企业战略与决策倾向
                  </li>
                  <li class="mb8px">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" /> 解析企业关联的圈子，系统规划触达路径
                  </li>
                  <li class="mb8px">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" /> 汇聚行业趋势与变化信息，洞察业务机会
                  </li>
                </ul>
                <!-- <a-button size="mini" type="primary" href="https://www.bengine.com.cn/product/eia" target="_block">了解团队版</a-button> -->
              </a-space>
            </template>
          </a-alert>
          <HotData />
          <BrowsingHistory />
          <Circles />
          <a-affix :offset-top="80">
            <companyWrapper />
          </a-affix>
        </a-space>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import HotData from './components/hotData.vue'
import Circles from './components/circles.vue'
import ActivityList from './components/activityList.vue'
import NavCard from './components/navCard.vue'
import BrowsingHistory from './components/browsingHistory.vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import companyWrapper from '@/components/companyWrapper/index.vue'
import { ref } from 'vue'
import { MessageOutlined } from '@ant-design/icons-vue'

const showTips = ref(localStorage.getItem('tips_close') !== '1')

// 在localStorage中存储当前用户点击记录
function handlerTipsClose() {
  localStorage.setItem('tips_close', '1')
}
</script>

<style scoped lang="less">
.home_container {
}
</style>
