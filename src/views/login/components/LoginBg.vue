<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-05 11:54:14
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-19 18:31:52
 * @FilePath: /corp-elf-web-consumer/src/views/login/components/LoginBg.vue
 * @Description: 
-->
<template>
  <div class="left-container">
    <img :src="companyLogo" class="logo" />

    <!-- :modules="[Pagination, Autoplay]" -->
    <swiper
      :autoplay="{ delay: 5000 }"
      :speed="600"
      :loop="true"
      :pagination="{ el: '.pagination', type: 'bullets' }"
    >
      <swiper-slide class="swiper-slide" v-for="(item, index) in bgList" :key="index">
        <div class="slide-container">
          <div class="slide-item">
            <h1>{{ item.title }}</h1>
            <div>
              <p v-for="(subTitle, index) in item.subTitle" :key="index">
                {{ subTitle }}
              </p>
            </div>
            <img :src="item.url" />
          </div>
        </div>
      </swiper-slide>
    </swiper>

    <div class="pagination"></div>

    <div class="bottomInfo">Powered by Datastory</div>
  </div>
</template>

<script setup lang="ts">
import bg1 from '@/assets/images/<EMAIL>'
import bg2 from '@/assets/images/<EMAIL>'
import bg3 from '@/assets/images/<EMAIL>'
import bg4 from '@/assets/images/<EMAIL>'
import companyLogo from '@/assets/productLogo/default/logo_white.svg'
// swiper
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import 'swiper/less/pagination'
import 'swiper/less/autoplay'
import 'swiper/less/scrollbar'

// 背景数据
const bgList = [
  {
    name: '行业洞察',
    url: bg1,
    title: '洞察行业全局，制定市场目标',
    subTitle: ['对行业的分析与洞察，为营销团队提供视野更全面的决策支撑', '基于历史成功案例的客户画像，帮助企业制定TAM（总目标市场）']
  },
  {
    name: '垂直赛道',
    url: bg2,
    title: '行业模型，精准支撑市场规划',
    subTitle: ['支持基于不同行业从业务需求角度出发，定制行业特定模型', '帮助用户把握目标客户的需求，更有针对性地规划业务&产品销售计划。']
  },
  {
    name: '企业全景',
    url: bg3,
    title: '精选500万高价值企业',
    subTitle: ['精选各行业实力、潜力、影响力TOP企业', '专注于对企业、高管、产品、品牌等维度进行更深层次的情报收集与分析。']
  },
  {
    name: '商机预测',
    url: bg4,
    title: '敏察商机，先人一步行动',
    subTitle: ['智能识别企业客户动态，捕捉、提取动态中的事件，推测客户意图。', '用户透过智能商情可实现先人一步的商机洞察，进而采取行动。']
  }
]
</script>

<style lang="less" scoped>
.left-container {
  --swiper-theme-color: #fff;
  --swiper-pagination-color: #fff; /* 两种都可以 */
  --swiper-pagination-bullet-inactive-color: rgba(255, 255, 255, 0.7);
  --swiper-pagination-bullet-width: 6px;
  --swiper-pagination-bullet-height: 6px;

  background: linear-gradient(149deg, #0a376e 0%, #041427 100%);
  overflow: hidden;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;

  .logo {
    position: absolute;
    top: 40px;
    left: 60px;
    width: 10vw;
    min-width: 160px;
    z-index: 99;
  }
  .swiper {
    width: 100%;
    height: 100%;

    :deep(.swiper-pagination-bullet-active) {
      width: 8px;
      height: 8px;
    }
    .swiper-wrapper {
      height: 100% !important;
    }
    .swiper-slide {
      height: 100% !important;

      .slide-container {
        width: 100%;
        position: absolute;
        height: 100%;
        display: flex;
        align-items: center;

        .slide-item {
          color: #ffffff;
          text-align: center;
          width: 60vw;
          min-width: 825px;
          margin: 0 auto;
          cursor: default;

          h1 {
            color: #ffffff;
            opacity: 1;
            margin: 0 auto;

            cursor: default;
            font-size: 2.6vw;
            font-weight: 550;
            margin-bottom: 1.1vh;
          }

          div {
            margin: 8px auto 0;
            cursor: default;
            // width: 30vw;
            min-width: 440px;

            p {
              color: #e5e5e5;
              font-size: 0.83vw;
              opacity: 0.9;
              // height: 2.7vh;
              // line-height: 2.7vh;
            }
          }

          img {
            min-height: 470px;
            height: 60vh;
            min-width: 730px;
            width: 90%;
            object-fit: contain;
          }
        }

        @media screen and (max-height: 750px) {
          .slide-item {
            img {
              margin-top: 0;
            }
          }
        }
        @media screen and (max-height: 900px) {
          .slide-item {
            h1 {
              margin-top: 30px;
            }
          }
        }
        @media screen and (max-width: 1280px) {
          .slide-item {
            min-width: 730px;
          }
          img {
            max-width: 730px;
          }
        }
        @media screen and (max-width: 1690px) {
          .slide-item {
            h1 {
              font-size: 32px;
              // margin-top: 120px;
            }
            div {
              p {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .pagination {
    padding-bottom: 6vh;
    align-items: center;
    display: flex;
    justify-content: center;
    position: absolute;
  }

  .bottomInfo {
    width: 100%;
    font-size: 12px;
    color: #ffffff;
    opacity: 0.6;
    position: absolute;
    bottom: 2.78vh;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    text-align: center;
  }
}
</style>
