<template>
  <div class="loginForm">
    <img :src="productLogo" class="productLogo" />

    <a-form ref="formRef" class="form" name="login" autocomplete="off" :model="loginForm" :rules="rules" @finish="onFinish">
      <a-form-item name="username">
        <a-input v-model:value="loginForm.username" placeholder="用户名">
          <template #prefix>
            <iconfontIcon icon="icon-user" class="site-form-item-icon"></iconfontIcon>
          </template>
        </a-input>
      </a-form-item>

      <a-form-item name="password">
        <a-input-password v-model:value="loginForm.password" class="password" placeholder="密码">
          <template #prefix>
            <iconfontIcon icon="icon-lock-on" class="site-form-item-icon"></iconfontIcon>
          </template>
        </a-input-password>
      </a-form-item>

      <a-form-item>
        <a-button size="large" type="primary" html-type="submit" class="loginBtn" :loading="loginBtnLoading"> 确定 </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { FormInstance, notification } from 'ant-design-vue'
import { ref } from 'vue'
import { timeFix } from '@/utils/util'
import { useRouter } from 'vue-router'
import type { Rule } from 'ant-design-vue/es/form'
import productLogo from '@/assets/productLogo/default/logo.svg'
import { useUserStore } from '@/store'
import { useThemeStore } from '@/store/modules/theme'
import { login } from '@/api/api'

const { getColorPrimary } = useThemeStore()

// import type { authLoginReqType } from '~/types/response'

const router = useRouter()
const userStore = useUserStore()

const formRef = ref<FormInstance>()
const loginBtnLoading = ref(false)

const loginForm = ref({
  username: '',
  password: '',
  remember_me: false
})
const rules: Record<string, Rule[]> = {
  username: [{ required: true, message: '请输入' }],
  password: [{ required: true, message: '请输入' }]
}

// 登录成功
async function onFinish() {
  try {
    loginBtnLoading.value = true
    const loginParams = { ...loginForm.value, checkKey: new Date().getTime() }
    const { result } = await login(loginParams)
    userStore.loginSuccess(result)
    notification.success({ message: '欢迎', description: `${timeFix()}，欢迎回来` })
    router
      .push({ path: '/' })
      .catch(e => console.error('登录跳转首页出错,这个错误从哪里来的', e))
      .finally(() => {
        loginBtnLoading.value = false
      })
  } catch (error) {
    console.error(error)
    loginBtnLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.loginForm {
  width: 550px;
  height: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .title {
    margin-bottom: 30px;
    text-align: left;
    font-size: 18px;
    font-weight: 550;
    line-height: 31px;
    color: v-bind(getColorPrimary);
    opacity: 1;
  }

  .productLogo {
    height: 68px;
    margin-bottom: 40px;
  }

  .form {
    width: 340px;

    :deep(.ant-input-affix-wrapper) {
      height: 50px;
      width: 100%;
      color: #333;
      font-size: 16px;
      font-weight: normal;
      box-shadow: 0px 3px 4px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      box-sizing: border-box;
      border: 1px solid #d5d5d5;
      background-color: #fff;

      input {
        &:-webkit-autofill {
          -webkit-box-shadow: 0 0 0 1000px #fff inset;
        }

        &:-internal-autofill-selected {
          transition: background-color 5000s ease-in-out 0s !important;
        }
      }
    }

    .password {
      :deep(.ant-input) {
        letter-spacing: 4px;
      }
    }
  }

  .loginBtn {
    width: 100%;
    height: 48px;
    font-size: 20px;
    padding: 0px 20px;
    color: #fff;

    border-radius: 5px;
    letter-spacing: 2px;
    text-align: center;
  }
}
</style>
