<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 16:46:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-01-23 11:27:26
 * @FilePath: /corp-elf-web-consumer/src/views/login/index.vue
 * @Description: 
-->
<template>
  <div class="login">
    <div class="loginContainer w-full h-full relative flex content-center">
      <Login-bg />
      <LoginForm />
    </div>
  </div>
</template>

<script setup lang="ts" name="defaultLogin">
import LoginBg from './components/LoginBg.vue'
import LoginForm from './components/loginForm.vue'
</script>

<style lang="less" scoped>
.login {
  height: 100%;
  width: 100%;
  font-size: 14px;
  background-color: #f1f5f8;
  .scroll_container {
    padding: 16px 0;
    width: 1400px;
    margin: 0 auto;
  }

  &Container {
    min-width: 1280px;
    min-height: 720px;
  }
}
</style>
