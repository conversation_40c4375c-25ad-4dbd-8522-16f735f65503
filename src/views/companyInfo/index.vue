<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-01-18 17:29:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:34:49
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/index.vue
 * @Description: 
-->

<template>
  <div class="companyInfo">
    <div class="mb16px flex items-center justify-between">
      <a @click="goBack" class="flexCenter z4"> <iconfontIcon icon="icon-chevron-left" /> 返回</a>

      <div class="btns">
        <a-spin :spinning="btnsLoading">
          <a-space>
            <!-- <div v-if="inChargeName.followName" class="flexCenter">
              <iconfontIcon icon="icon-user" />
              <span>负责人：{{ inChargeName.followName }}</span> -->
            <!-- </div> -->

            <!-- <template v-for="(item, index) in companyActionBtn"> -->
            <!-- 解锁 -->
            <!-- <a-button size="mini" :key="index" v-if="item.type === 'UNLOCK'" @click.native="unlock">
                {{ item.label }}
              </a-button> -->
            <!-- 领取 -->
            <!-- <a-button size="middle" type="primary" @click="receive" :key="index" v-if="item.type === 'RECEIVE'">
                {{ item.label }}
              </a-button> -->
            <!-- 取消关注 -->
            <!-- <a-button size="middle" @click="cancel" :key="index" v-if="item.type === 'CANCEL'">
                {{ item.label }}
              </a-button> -->
            <!-- 指派、重新指派 -->
            <!-- <a-button size="middle" @click="handlerAssign" v-if="item.type === 'ASSIGN' || item.type === 'REASSIGN'" :key="index">
                {{ item.label }}
              </a-button> -->
            <!-- </template> -->
            <!-- <a-button disabled size="middle"> 发送CRM </a-button> -->
            <a-button size="middle" @click="showErrorDialog"> 数据反馈 </a-button>
            <a-button
              v-if="isEmpty(inChargeName.followName)"
              size="middle"
              @click="addFollowModalRef?.show({ companyName: companyInfo.entName, companyId: companyInfo.cid })"
            >
              关注
            </a-button>
            <a-button v-else size="middle" @click="cancel"> 取消关注 </a-button>
          </a-space>
        </a-spin>
      </div>
    </div>

    <a-row :gutter="16">
      <a-col :span="6">
        <SurveyCard />
      </a-col>
      <a-col :span="18">
        <SalesBattleCard />
      </a-col>
    </a-row>

    <errorInfoModal ref="errorInfoModalRef" :companyId="id" />
    <addFollowModal ref="addFollowModalRef" @refresh="handlerAssignDialog" />
    <!-- <assignDialog ref="assignDialogRef" @getList="handlerAssignDialog"></assignDialog>
    <receiveDialog ref="receiveDialogRef" @getList="handlerAssignDialog"></receiveDialog> -->
  </div>
</template>

<script lang="ts">
const routerGuard = (to: RouteLocationNormalized, _from: RouteLocationNormalized) => {
  const { companyId, companyName } = to.query
  if (isEmpty(companyId) && isEmpty(companyName)) {
    message.error('企业id和企业名称违规')
    return false
  }

  commonGetCompanyType({ companyIdOrCompanyName: (companyId as string) || (companyName as string) })
    .then(({ result }) => {
      const { isEnterDetail, companyType, notEnterMsg } = result
      if (!isEnterDetail) {
        message.warning(notEnterMsg)
        throw new Error(notEnterMsg)
      } else {
        // if (companyType === 'company') {
        // } else
        if (companyType === 'layer') {
          return {
            path: '/circleLayer/detail',
            query: { name: companyName, id: companyId }
          }
        }
      }
    })
    .catch(err => {
      console.error(err)
      return false
    })
}

export default {
  beforeRouteEnter: routerGuard,
  beforeRouteUpdate: routerGuard
}
</script>

<script setup lang="ts" name="companyInfo-index">
// 导入组件
// import basicsInfo from './components/basicsInfo/index.vue'
// import followRecord from './components/followRecord/index.vue'
// import similarEnterprises from './components/similarEnterprises/index.vue'
import { isEmpty } from 'lodash-es'
// import { assignDialog, receiveDialog } from '@/components/companyAction'
import errorInfoModal from './components/errorInfoModal/index.vue'
import { companyDetailBaseInfo, customerFollowInfo, customerCancel, indexSaveRecentBrowsing, commonGetCompanyType } from '@/api/api'
import { onActivated, onMounted, ref, h } from 'vue'
import { NavigationGuardNext, RouteLocationNormalized, useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
// import { companyBaseInfoResponse, companyFollowInfoResponse } from '~/types/response'
import SurveyCard from './components/leftCard/index.vue'
import SalesBattleCard from './components/salesBattleCard/index.vue'
// import AffiliatedOrg from './components/affiliatedOrg/index.vue'
import { useKeepAliveCache } from '@/store'
import iconfontIcon from '@/components/tools/iconfontIcon'
import addFollowModal from '@comp/addFollowModal/index.vue'
import { customerFollowInfoResType } from '~/types/api/customer/followInfo'

const addFollowModalRef = ref<InstanceType<typeof addFollowModal>>()
const route = useRoute()
const router = useRouter()
const keepAliveCache = useKeepAliveCache()

// const tabsActiveName = ref('销售战斗卡')
// const followRecordRef = ref()
const btnsLoading = ref(false)
const id = ref('') // 企业id
const companyInfo = ref() // 企业信息
// const unLockUserName = ref('') // 解锁人名称
const inChargeName = ref<Omit<customerFollowInfoResType, 'type'>>({ followName: '', unlockName: '', userId: '' }) // 负责人
// const companyActionBtn = ref<Array<{ label: string; type: string }>>([]) // 企业可以操作的按钮

const { companyId } = route.query
console.log('companyId: ', companyId)
id.value = companyId as string

// 获取企业信息
async function getCompanyInfo() {
  try {
    const { result } = await companyDetailBaseInfo({ cId: id.value })
    if (!isEmpty(result)) {
      companyInfo.value = result
    }
  } catch (error) {
    console.error(error)
  }
}

// 保存浏览记录
const isFirstRender = ref(true)
async function saveRecentBrowsing() {
  // const useStore = useUserStore()

  try {
    const { result } = await indexSaveRecentBrowsing({
      cid: companyInfo.value?.cid,
      entLogo: companyInfo.value?.entLogo,
      companyName: companyInfo.value?.entName
      // userId: useStore.getUserInfo.id
    })
    isFirstRender.value = false
    console.log('result : ', result)
  } catch (error) {
    console.error(error)
  }
}

// 获取解锁人和负责人
async function getCustomerFollowInfo() {
  try {
    btnsLoading.value = true
    const { result } = await customerFollowInfo({ companyId: id.value, appType: 'LITE' })
    inChargeName.value = result
    console.log('inChargeName.value: ', inChargeName.value)
    btnsLoading.value = false
  } catch (error) {
    console.error(error)
    btnsLoading.value = false
  }
}

// // 获取操作按钮
// async function isUnlock() {
//   try {
//     btnsLoading.value = true
//     const { result } = await customerIsUnlock({ companyId: id.value })
//     companyActionBtn.value = result
//     btnsLoading.value = false
//   } catch (error) {
//     console.error(error)
//     btnsLoading.value = false
//   }
// }

// // 解锁企业
// function unlock() {
//   Modal.confirm({
//     title: '提示',
//     content: '是否解锁该企业?',
//     icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
//     autoFocusButton: null,
//     onOk() {
//       customerUnlock({
//         companyId: [id.value]
//       })
//         .then(({ message: msg }) => {
//           message.success(msg)
//           handlerAssignDialog()
//           // if (tabsActiveName.value === 'publicContact') {
//           //   this.$refs.publicContact.isLock = false
//           // }
//         })
//         .catch(err => {
//           console.error(err)
//         })
//     }
//   })
// }

// 取消关注企业
function cancel() {
  Modal.confirm({
    title: '提示',
    content: '是否取消关注该企业?',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onOk() {
      customerCancel({ companyId: [id.value] })
        .then(() => {
          handlerAssignDialog()
        })
        .catch(err => {
          console.error(err)
        })
    }
  })
}

// // 领取企业
// const receiveDialogRef = ref()
// function receive() {
//   receiveDialogRef.value.show([{ companyId: id.value, companyName: companyInfo.value?.entName }])
// }

// // 打开指派人员弹窗
// const assignDialogRef = ref()
// function handlerAssign() {
//   assignDialogRef.value.show([{ companyId: id.value, companyName: companyInfo.value?.entName }], [inChargeName.value.userId])
// }

// 指派、重新指派回掉
function handlerAssignDialog() {
  // isUnlock()
  getCustomerFollowInfo()

  // if (tabsActiveName.value === '跟进记录') {
  //   followRecordRef.value.getCustomerRecord()
  // }
}

// 返回事件
async function goBack() {
  router.back()
  keepAliveCache.delCachedView(route) // 消除当前路由的缓存
}

const errorInfoModalRef = ref()
function showErrorDialog() {
  errorInfoModalRef.value.show()
}

onMounted(async () => {
  try {
    getCustomerFollowInfo() // 获取解锁人和负责人
    // isUnlock() // 判断企业是否解锁
    await getCompanyInfo()
    saveRecentBrowsing()
  } catch (error) {
    console.error(error)
  }
})

onActivated(() => {
  if (!isFirstRender.value) {
    saveRecentBrowsing()
  }
})

// onDeactivated(() => {
//   tabsActiveName.value = '销售战斗卡'
// })
</script>

<style lang="less">
.companyInfo {
  .tabs {
    background-color: #fff;
    padding-top: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;
    .ant-tabs-tab {
      width: 120px;
      justify-content: center;
    }
    .ant-tabs-nav {
      margin: 0;
    }
  }

  // tab内子组件用
  .company_card {
    margin-bottom: 16px;

    .base_info {
      display: flex;
      align-items: flex-start;

      .logo_box {
        width: 120px;
        text-align: center;
        margin-right: 16px;

        .ant-image {
          margin-bottom: 16px;
        }

        .logo_box_bottom {
          .matching,
          .scale {
            display: flex;
            align-items: center;

            span {
              &:first-child {
                width: 70px;
                text-align: justify;
                text-align-last: justify;
                text-justify: inter-ideograph;
              }

              &:last-child {
                text-align: center;
                width: 30px;
                flex: 1;
              }
            }
          }

          .matching {
            .num {
              background-color: #fcf4e9;
              color: #ef864b;
            }
          }
        }
      }

      .detail {
        flex: 1;
        display: flex;
        flex-direction: column;

        .companyNameAndBtnBox {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .companyNameBox {
            // line-height: 32px;
            h1 {
              font-size: 20px;
              font-weight: 550;
            }

            a {
              margin-left: 8px;
              font-size: 14px;
            }
          }
        }

        .companyTag {
          overflow: hidden;
          height: 27px;
          margin-bottom: 8px;
        }

        .middle {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          .ant-descriptions {
            background-color: #f1f0ff;
            border-radius: 2px;
            padding: 16px 16px 0;
          }
        }

        .bottom {
          margin-top: 10px;
          color: #333;

          .leftBox {
            display: inline-flex;
            align-items: center;
            height: 30px;

            .parentCompany {
              display: flex;
              align-items: center;
              margin-right: 10px;
            }
          }

          .rightBox {
            height: 30px;
            float: right;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  .ant-tabs {
    overflow: inherit;
    .ant-tabs-tab {
      padding-top: 0;
    }
  }

  // .other_info {
  //   margin-top: 24px;
  //   display: flex;
  //   // align-items: flex-start;

  //   .detail_info {
  //     position: relative;
  //     flex: 1;
  //     // border: 1px solid #ebeef5;
  //     // background-color: #fff;
  //     //margin-right: 16px;

  //     .tabs {
  //       position: sticky;
  //       top: 80px;
  //       box-shadow: none;
  //       border: none;
  //       z-index: 999;
  //       background-color: #f4f6f9;

  //       p {
  //         display: inline-block;
  //         // max-width: 152px;
  //         width: 100px;
  //         height: 48px;
  //         line-height: 48px;
  //         text-align: center;
  //         background: #fff;
  //         opacity: 0.7;
  //         border-radius: 4px 4px 0 0;
  //         border-radius: 2px 2px 0px 0px;
  //         transform: all 0.3s;

  //         + P {
  //           margin-left: 8px;
  //         }

  //         &:hover {
  //           cursor: pointer;
  //           opacity: 1;
  //           color: ;
  //         }
  //       }
  //     }

  //     .tab_content {
  //       padding: 20px 32px;
  //       background-color: #fff;
  //       position: relative;
  //       // min-height: 795px;
  //     }
  //   }
  // }
}
</style>
