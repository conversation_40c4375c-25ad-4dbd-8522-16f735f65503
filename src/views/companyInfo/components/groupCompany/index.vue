<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-12 17:16:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 16:20:04
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/groupCompany/index.vue
 * @Description: 所属集团
-->
<template>
  <p class="groupCompany" v-if="isShowGroupName">
    所属集团：
    <a-skeleton active :loading="loading && !visible" :paragraph="{ rows: 1, width: 80 }" :title="false">
      <a @click="visible = true"> {{ groupData.groupName }} </a>
    </a-skeleton>
  </p>

  <a-modal class="groupCompanyDialog" title="所属集团" v-model:open="visible" width="1000px" :footer="null">
    <div class="company_list">
      <a-spin :spinning="loading">
        <div class="groupInfo">
          <div class="logo_box">
            <a-image :width="80" :height="80" :src="groupData.groupLogo" :fallback="companyDefaultLogo" :preview="false" />
          </div>

          <div class="textBox">
            <h3>{{ groupData.groupName }}</h3>
            <div class="groupName">
              <p>主公司：</p>
              <a @click="handlerCompanyClickHandler(groupData.baseCompanyId, groupData.baseCompany)">
                {{ groupData.baseCompany }}
              </a>
            </div>
          </div>
        </div>

        <div class="record">
          <span class="main_color"> {{ paginationParams.total }} </span>
          家成员企业
        </div>

        <!-- <a-list
          item-layout="vertical"
          size="large"
          :pagination="paginationParams"
          :data-source="listData"
          :split="false"
        >
          <template #renderItem="{ item }">
            <company-card :companyInfo="item" @openCompanyInfo="handlerCompanyClickHandler(item.cid, item.entName)">
            </company-card>
          </template>
        </a-list> -->

        <a-table :loading="loading" :columns="tableColumns" :dataSource="listData" :pagination="paginationParams">
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.slotName === 'companyCard'">
              <companyItem :companyInfo="record" />
            </template>
            <template v-if="column.slotName === 'address'">
              {{ transformLocation({ province: record.province, city: record.city }) }}
            </template>
            <template v-if="column.slotName === 'annualRevenue'">
              {{ transformAnnualRevenue({ annualRevenue: text || '' }) }}
            </template>
            <!-- <template v-if="column.slotName === 'tags'">
              <div class="tags">
                <a-tag
                  v-for="(item, index) in record.relationOrgTypeTags"
                  :key="index"
                  :style="{
                    backgroundColor: item.backgroundColor,
                    color: item.fontColor
                  }"
                >
                  {{ item.tagLabel }}
                </a-tag>
              </div>
            </template> -->
          </template>
        </a-table>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { companyDetailGroup } from '@/api/api'
import companyDefaultLogo from '@/assets/icon/companyDefaultLogo.svg'
import { toNumber } from 'lodash-es'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import companyItem from '@comp/table/companyItem'
import { transformAnnualRevenue, transformLocation } from '@/utils/util'

const router = useRouter()

const props = defineProps<{ companyId: string }>()

const visible = ref(false)
const loading = ref(false)
const groupData = ref({
  baseCompany: '', // 主体公司
  baseCompanyId: '', // 主体公司id
  groupName: '', // 集团名
  groupLogo: '' // 集团logo
})
const tableColumns = [
  { title: '企业', slotName: 'companyCard' },
  { title: '地区', dataIndex: 'address', slotName: 'address', width: '13%', ellipsis: true },
  { title: '成立日期', dataIndex: 'startDate', width: '13%', ellipsis: true },
  { title: '营收规模', dataIndex: 'annualRevenue', slotName: 'annualRevenue', width: '13%', ellipsis: true },
  { title: '实力指数', dataIndex: 'powerfulRankScore', width: '13%', ellipsis: true }
  // { title: '', slotName: 'tags', width: '13%' }
]
const listData = ref([])
const isShowGroupName = computed(() => listData.value.length > 0 || loading.value)
//  分页参数
const paginationParams = ref({
  current: 1, // 当前页数
  pageSize: 10, // 每页显示条目个数
  total: 0, // 总条目数
  showSizeChanger: false, // 不显示pagesize修改
  hideOnSinglePage: true, // 只有一页时是否隐藏
  pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
  responsive: true, // 当 size 未指定时，根据屏幕宽度自动调整尺寸
  onChange: (page: any) => handlerCurrentChange(page)
})

function getList() {
  loading.value = true
  companyDetailGroup({
    companyId: props.companyId,
    pageNo: paginationParams.value.current,
    pageSize: paginationParams.value.pageSize
  })
    .then(({ result }) => {
      const { members: records = [], total } = result

      // 集团相关信息
      groupData.value = {
        baseCompany: result.baseCompany || '', // 主体公司
        baseCompanyId: result.baseCompanyId || '', // 主体公司id
        groupName: result.groupName || '', // 集团名
        groupLogo: result.groupLogo || '' // 集团logo
        // members: result.members || '' // 集团成员
      }
      // 集团公司列表
      listData.value = records
      paginationParams.value.total = toNumber(total)
      loading.value = false

      // console.log('this.listData: ', this.listData);
    })
    .catch(err => {
      console.error(err)
      loading.value = false
    })
}
// 第几页改变时触发
function handlerCurrentChange(e: number) {
  paginationParams.value.current = e
  getList()
}
// 打开公司
function handlerCompanyClickHandler(companyId: any, companyName: any) {
  if (companyId) {
    router.push({ path: '/companyInfo/index', name: 'companyInfo-index', query: { companyId, companyName } }).finally(() => {
      console.log(12121)
      visible.value = false
    })
  } else {
    message.warning('该组织详情正在准备中')
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.groupCompany {
  display: flex;
  align-items: center;
  .ant-skeleton {
    width: 120px;
    display: inline-table;
  }
}
.groupCompanyDialog {
  .groupInfo {
    display: flex;
    align-items: center;
    padding: 0 20px 20px;
    // border-bottom: 1px solid #e7e7e7;
    // margin-bottom: 20px;

    .logo_box {
      width: 80px;
      margin: 0 12px;
      display: flex;
      flex-direction: column;
      line-height: 22px;

      .logo_text {
        height: 100%;
        display: flex;
        align-items: center;

        div {
          flex: 1;
          line-height: 30px;
          font-size: 28px;
        }
      }
    }

    .textBox {
      flex: 1;

      h3 {
        font-size: 18px;
        margin-bottom: 6px;
        font-weight: 550;
      }

      .groupName {
        display: flex;
        align-items: center;
      }
    }
  }

  .record {
    margin: 12px 0;
    .main_color {
      color: var(--g-primary-color, #6553ee);
    }
  }
}
</style>
(: any): number: any: any
