<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-18 15:07:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2022-11-24 10:52:07
 * @FilePath: /corp-elf-web/src/views/companyInfo/components/errorInfoModal/index.vue
 * @Description: 
-->
<template>
  <a-modal title="数据纠错" class="errorInfoDialog" v-model:open="visible" :width="500" @cancel="hide" @ok="submit">
    <a-form ref="formRef" :model="form" :rules="rules">
      <a-form-item label="纠错意见" name="errorText">
        <a-textarea :rows="4" placeholder="纠错意见" v-model:value="form.errorText"> </a-textarea>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { customerErrorInfo } from '@/api/api'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

const props = defineProps<{ companyId: string }>()

const visible = ref(false)
const loading = ref(false)
const form = ref({ errorText: '' })
const rules = { errorText: [{ required: true, message: '请输入' }] }
const formRef = ref()

const show = () => {
  // this.form.companyId = companyId
  visible.value = true
}
const hide = () => {
  visible.value = false
  formRef.value.resetFields()
}

const submit = async () => {
  try {
    const validRef = await formRef.value.validateFields()
    console.log('validRef: ', validRef)
    console.log('123123', form.value)

    customerErrorInfo({
      companyId: props.companyId,
      text: form.value.errorText
    })
      .then(({ message: msg }) => {
        message.success(msg)
        loading.value = false
        hide()
      })
      .catch(error => {
        console.error(error)
        loading.value = false
      })
  } catch (error) {
    console.error(error)
  }
  // this.$refs.form.validate(valid => {
  //   if (valid) {
  //     this.loading = true

  //   }
  // })
}

defineExpose({
  show
})
</script>

<style lang="less" scoped></style>
