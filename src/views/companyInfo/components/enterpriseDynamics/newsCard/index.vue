<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-19 22:01:57
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 11:01:34
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/enterpriseDynamics/newsCard/index.vue
 * @Description: 新闻卡片
-->
<template>
  <div class="newsCard" @click="openNewsDetail">
    <template v-if="haveTitle">
      <div class="newsTitle hoverPrimaryColor">{{ newsData.title }}</div>
    </template>
    <template v-else>
      <a-space>
        <a-avatar v-if="newsData.authorUrl && isShowAvatar" :size="32" :src="newsData.authorUrl" :loadError="handlerAvatarLoadError" />
        <span v-if="newsData.author" class="author">{{ newsData.author }}</span>
      </a-space>
    </template>

    <div class="other">
      <!-- 时间 -->
      <span>{{ newsData.publishDate }}</span>
      <!-- 发布人、来源 -->
      <template v-if="haveTitle">
        <span>
          <template v-if="newsData.author && newsData.siteName"> {{ newsData.author }} | {{ newsData.siteName }} </template>
          <template v-else> {{ newsData.author }} {{ newsData.siteName }} </template>
        </span>
      </template>
      <template v-else>
        <span> {{ newsData.siteName }}</span>
      </template>
      <!-- 事件类型 -->
      <span v-if="eventsTags.length">
        事件类型
        <!-- :max="props.size === 'mini' ? 1 : eventsTags.length" -->
        <EllipsisTag style="margin-left: 4px" :max="1" :tagList="eventsTags" />
      </span>
    </div>

    <div class="content">
      {{ newsData.summary }}
    </div>
    <!-- 短样式 -->
    <template v-if="props.size === 'mini'">
      <div class="relation relation-mini">
        <!-- 竞对品牌 -->
        <!-- <a-tag
          v-for="(item, index) in newsData.rivalBrands || []"
          :key="index"
          @click.stop="handlerRivalBrandsClick(item)"
          color="#f0e2e3"
          style="color: #df4152"
        >
          竞对:{{ item.tagLabel }}
        </a-tag> -->

        <!-- 潜客公司,客户公司,关联公司 -->
        <EllipsisTag style="margin-left: 4px" :max="1" :tagList="allCompanyTags" @tagClick="handlerCompanyTagClick" />
      </div>

      <div class="iconBox iconBox-mini">
        <a-space>
          <span class="flex items-center">
            <iconfontIcon style="margin-right: 4px" icon="icon-share" />{{ newsData.shareCnt || '-' }}
          </span>
          <span class="flex items-center">
            <iconfontIcon style="margin-right: 4px" icon="icon-chat" />{{ newsData.reviewCnt || '-' }}
          </span>
          <span class="flex items-center">
            <iconfontIcon style="margin-right: 4px" icon="icon-thumb-up" />{{ newsData.likeCnt || '-' }}
          </span>
          <span class="flex items-center">
            <iconfontIcon style="margin-right: 4px" icon="icon-star" />{{ newsData.collectionViedoCnt || '-' }}
          </span>
        </a-space>
      </div>
    </template>
    <!-- 宽样式 -->
    <template v-else>
      <div class="relation">
        <div>
          <!-- 竞对品牌 -->
          <!-- <a-tag
            v-for="(item, index) in newsData.rivalBrands || []"
            :key="index"
            @click.stop="handlerRivalBrandsClick(item)"
            color="#f0e2e3"
            style="color: #df4152"
            >竞对:{{ item.tagLabel }}</a-tag
          > -->

          <!-- 潜客公司,客户公司,关联公司 -->
          <EllipsisTag style="margin-left: 4px" :tagList="allCompanyTags" @tagClick="handlerCompanyTagClick" />
        </div>

        <div class="iconBox">
          <a-space>
            <span><iconfontIcon style="margin-right: 4px" icon="icon-share" />{{ newsData.shareCnt || '-' }}</span>
            <span><iconfontIcon style="margin-right: 4px" icon="icon-chat" />{{ newsData.reviewCnt || '-' }}</span>
            <span><iconfontIcon style="margin-right: 4px" icon="icon-thumb-up" />{{ newsData.likeCnt || '-' }}</span>
            <span> <iconfontIcon style="margin-right: 4px" icon="icon-star" />{{ newsData.collectionViedoCnt || '-' }} </span>
          </a-space>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts" name="newsCard">
import { computed, ref, toRefs } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { isEmpty } from 'lodash-es'
import EllipsisTag from '@/components/ellipsisTag/index.vue'
import { useRouter } from 'vue-router'
import { message, theme } from 'ant-design-vue'
import { useThemeStore } from '@/store'
import { newsBusinessResType } from '~/types/api/company/companyDetail/newsBusiness'
import { tagType } from '~/types/common/tagType'

const themeStore = useThemeStore()
const { useToken } = theme
const { token: themeToken } = useToken()
const props = withDefaults(defineProps<{ newsData: newsBusinessResType; size?: 'mini' | 'default' }>(), {
  size: 'default'
  // haveTitle: true
})

const haveTitle = computed(() => !isEmpty(newsData.value.title))

const { newsData } = toRefs(props)

// 事件tag
const eventsTags = computed(() => {
  if (isEmpty(newsData.value.events)) {
    return []
  }

  const tagList = newsData.value.events.map(item => ({
    tagLabel: item,
    tagValue: item
  }))
  return tagList
})

// mini模式用，所有公司标签
const allCompanyTags = computed(() => {
  const {
    // potentialCustomerCompanies, // 潜客公司
    // customerCompanies, // 客户公司
    relationCompanies, // 关联公司
    customerCollectCompanies
  } = newsData.value
  const tagList = [
    // ...potentialCustomerCompanies.map(item => ({ ...item, tagLabel: `潜客:${item.tagLabel}` })),
    // ...customerCompanies.map(item => ({ ...item, tagLabel: `客户:${item.tagLabel}` })),
    ...customerCollectCompanies.map(item => ({ ...item, tagLabel: `${item.tagLabel}` })),
    ...relationCompanies.map(item => ({ ...item, tagLabel: `${item.tagLabel}` }))
  ]
  return tagList
})

// 打开新闻原文连接
function openNewsDetail() {
  console.log('website: ', newsData.value.url)
  if (newsData.value.url) {
    let url = newsData.value.url
    if (!(url.includes('https://') || url.includes('http://'))) {
      url = `http://${url}`
    }
    window.open(url, '_blank')
  }
}

// 是否显示头像
const isShowAvatar = ref(true)
function handlerAvatarLoadError() {
  isShowAvatar.value = false
  return true
}

const router = useRouter()
function handlerCompanyTagClick(e:tagType) {
  if (e.tagValue) {
    router.push({
      path: '/companyInfo/index',
      name: 'companyInfo-index',
      query: {
        companyId: e.tagValue,
        companyName: e.tagLabel
      }
    })
  } else {
    message.warning('该组织详情正在准备中')
  }
}

// // 品牌点击
// function handlerRivalBrandsClick(item) {
//   console.log('item: ', item)
//   // const tab = ''
//   // 新闻动态 news
//   // 市场营销 market
//   // 客户分布 customer
//   // 招投标 bidding
//   router.push({
//     name: 'competitor-opponent-news',
//     path: '/competitor/opponent/news',
//     query: { type: item.tagLabel, tab: 'news' }
//   })
// }
</script>

<style lang="less" scoped>
.newsCard {
  padding: 16px;
  &:hover {
    cursor: pointer;
    background-color: #f4f0ff;
    .newsTitle {
      color: v-bind('themeStore.getColorPrimary');
    }
  }

  + .newsCard {
    border-top: 1px solid #e7e7e7;
  }

  .newsTitle {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #050505;
    font-size: 16px;
    color: v-bind('themeToken.colorText');
    &:hover {
      color: v-bind('themeToken.colorLinkHover');
    }
  }

  .author {
    color: #050505;
    font-size: 16px;
  }

  .other {
    margin-top: 8px;
    color: #8c8c8c;
    span {
      + span {
        margin-left: 4px;
      }
    }
  }
  .content {
    color: #595959;
    margin-top: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
  }
  .relation {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    &-mini {
      display: block;
    }
  }

  .iconBox {
    color: #8c8c8c;

    &-mini {
      margin-top: 8px;
    }
  }
}
</style>
