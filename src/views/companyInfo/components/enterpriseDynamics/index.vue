<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 16:52:36
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-10 10:29:20
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/enterpriseDynamics/index.vue
 * @Description: 
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0 16px' }" :bordered="false" title="主要动态" class="h100%">
    <a-spin :spinning="loading">
      <a-list item-layout="vertical" size="large" :data-source="dataList" :split="false">
        <template #renderItem="{ item }">
          <newsCard :newsData="item"></newsCard>
        </template>
        <template #loadMore>
          <div v-if="dataList.length > 0" class="loadMore py-8px" v-intersection-observer="handlerIntersectionObserver">
            <div class="dynamicsMask" v-if="!userStore.isVip && pageParams.total! > 2">
              <a-result>
                <template #icon> <img src="@/assets/images/vip.png" class="m-0 m-auto w96px" /> </template>
                <template #title>
                  <span class="color-#fea127"> 开通会员查看剩余{{ isEmpty(pageParams.total) ? '内容' : `${pageParams.total}条内容` }}</span>
                </template>
                <template #extra>
                  <a-button type="primary" @click="openVipModal">开通会员</a-button>
                </template>
              </a-result>
            </div>
            <p class="endText" v-else-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
            <p class="endText" v-else>没有更多了</p>
          </div>
        </template>
      </a-list>
      <!-- <div class="text-right px-16px pt-16px">
        <a-pagination v-bind="paginationParams" />
      </div> -->
    </a-spin>
    <template #extra></template>
  </a-card>
</template>

<script setup lang="ts" name="EnterpriseDynamics">
import { companyCompanyDetailNewsBusiness } from '@/api/api'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import newsCard from './newsCard/index.vue'
import { newsBusinessReqType } from '~/types/api/company/companyDetail/newsBusiness'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { vIntersectionObserver } from '@vueuse/components'
import { useUserStore } from '@/store'
import { useEventBus } from '@vueuse/core'
import { isEmpty } from 'lodash-es'

const userStore = useUserStore()
const { emit: openVipModal } = useEventBus('openVipModal')
const route = useRoute()
const companyId = route.query.companyId || ''
const type = ref('all')
const params = computed<newsBusinessReqType>(() => {
  const temp: newsBusinessReqType = {
    companyId: companyId as string,
    type: type.value === 'all' ? undefined : type.value
    // pageNo: paginationParams.value.current as number,
    // pageSize: paginationParams.value.pageSize as number
  }
  return temp
})
const { loading, dataList, noMore, onLoadMore, pageParams } = useInfiniteLoading(companyCompanyDetailNewsBusiness, params)

/**
 * @description: 滚动到界面底部回调方法
 * @param {*} intersectionObserverList
 * @return {*}
 */
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !loading.value && !noMore.value && userStore.isVip) {
    onLoadMore()
  }
}
</script>

<style lang="less" scoped>
.endText {
  text-align: center;
  height: 44px;
  line-height: 44px;
  color: #909399;
}

.dynamicsMask {
  background-image: url('@/assets/images/dynamics-mask.jpg');
  height: 390px;
  background-size: contain;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filterBox {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 16px;
  .filterTypeList {
    // flex: 1;
    span {
      cursor: pointer;
      white-space: nowrap;
      display: inline-block;
      color: #595959;
      + span {
        margin-left: 8px;
      }
    }
    .activationItem {
      color: var(--g-primary-color, #6553ee);
    }
    .disabledItem {
      color: #bfbfbf;
      cursor: no-drop !important;
    }
  }
}
</style>
