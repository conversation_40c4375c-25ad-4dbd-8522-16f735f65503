<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-04 18:35:22
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-19 16:32:14
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/executiveSaid/index.vue
 * @Description: 
-->

<template>
  <div class="executiveSaid">
    <a-row :gutter="16">
      <a-col :span="14"><Comments /> </a-col>
      <a-col :span="10">
        <div class="sticky t-80px">
          <CompanyExecutive />
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import Comments from './components/comments.vue'
import CompanyExecutive from './components/companyExecutive.vue'
</script>

<style scoped></style>
