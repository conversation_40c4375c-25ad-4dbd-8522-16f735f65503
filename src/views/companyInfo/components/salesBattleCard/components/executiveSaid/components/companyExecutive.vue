<template>
  <a-card class="companyExecutive" :bodyStyle="{ minHeight: executiveList.length === 0 ? '366px' : '0px' }">
    <template #title>
      <div class="flex items-center">
        <p :style="{ marginRight: '8px' }">企业高管</p>

        <div class="flex items-center">
          <iconfontIcon
            icon="icon-search"
            :extra-common-props="{ class: 'fs-18px hoverPrimaryColor mr-8px color-#7F7F7F' }"
            @click="handlerSearchIconBtnClick"
          />
          <a-input
            v-show="showKeyWordInput"
            ref="keywordRef"
            v-model:value="filterParams.keyword"
            style="width: 220px"
            class="!fs-14 fw-400 transition-all"
            placeholder="关键词搜索"
            size="small"
            @change="handlerKeyWordChange"
            @blur="handlerKeyWordBlur"
            allowClear
          ></a-input>
        </div>
      </div>
    </template>
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px', margin: '16px 0 0' } } }">
      <a-table
        size="small"
        :dataSource="executiveList"
        :columns="columns"
        :loading="loading"
        :pagination="{
          ...pageParams,
          onChange: handlerSizeChange
        }"
      >
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.dataIndex === 'executiveName'">
            <router-link
              class="color-#000000E0 ellipsis"
              :to="{
                name: 'executiveComments-detail',
                path: '/executiveComments/detail',
                query: { executiveId: record.executiveId, executiveName: record.executiveName }
              }"
              >{{ text }}</router-link
            >
            <a-tag v-if="record.postList.filter((item:postList) => item.status === 1).length === 0" class="ml4px"> 失效</a-tag>
          </template>
          <template v-if="column.dataIndex === 'postList'">
            <span class="color-#7F7F7F" :title="text.map((item: postList) => item.post).join('、')">
              {{ text.map((item: postList) => item.post).join('、') }}
            </span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { executiveSaidListCompanyExecutive } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import useListLoading from '@/hooks/useListLoading'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import { debounce, isEmpty } from 'lodash-es'
import { computed, nextTick, ref } from 'vue'
import { useRoute } from 'vue-router'
import { postList } from '~/types/api/executiveSaid/executiveDetail'

const { query } = useRoute()
const { companyId } = query

const filterParams = ref({ keyword: undefined })

const columns = [
  { title: '姓名', dataIndex: 'executiveName', width: 140 },
  { title: '所属岗位', dataIndex: 'postList', ellipsis: true }
]
const params = computed(() => ({ keyword: filterParams.value.keyword, companyUniId: companyId as string }))
const {
  loading,
  dataList: executiveList,
  pageParams,
  refresh,
  getData
} = useListLoading(
  executiveSaidListCompanyExecutive,
  params
  //  { pageParams: { pageSize: 30 } }
)

/**
 * @description: 分页改变
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}

const keywordRef = ref<InputRef>()
const showKeyWordInput = ref(false)
/** 关键字输入框icon点击事件 */
function handlerSearchIconBtnClick() {
  showKeyWordInput.value = true
  nextTick(() => keywordRef.value?.focus())
}
/** 关键字变动 */
const handlerKeyWordChange = debounce(refresh, 300)
/** 关键字失焦处理 */
function handlerKeyWordBlur() {
  console.log('arguments: ', arguments)
  if (isEmpty(filterParams.value.keyword)) {
    showKeyWordInput.value = false
  }
}

// async function getData() {
//   try {
//     loading.value = true
//     const { result } = await executiveSaidListCompanyExecutive({
//       keyword: form.value.keyword,
//       companyUniId: companyId as string,
//       pageNo: 1,
//       pageSize: 20
//     })
//     const { records } = result
//     console.log('records: ', records)
//     loading.value = false
//   } catch (error) {
//     console.error(error)
//     loading.value = false
//   }
// }

// onMounted(() => {
//   getData()
// })
</script>

<style lang="less" scoped>
.companyExecutive {
}
</style>
