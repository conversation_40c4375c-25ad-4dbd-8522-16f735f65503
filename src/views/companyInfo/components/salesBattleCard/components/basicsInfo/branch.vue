<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 15:44:48
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/branch.vue
 * @Description: 公司详情-分支机构
-->
<template>
  <div>
    <p class="title">分支机构（正常状态）</p>

    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.slotName === 'number'">
          <span class="rank">
            {{ pagination.current * pagination.pageSize + index + 1 - pagination.pageSize }}
          </span>
        </template>
        <!-- 公司名称 -->
        <template v-if="column.slotName === 'entName'">
          <companyAvatar :companyData="record"></companyAvatar>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailBranch } from '@/api/api'
import companyAvatar from '@/components/companyAvatar/index.vue'
import { defineComponent } from 'vue'

export default defineComponent({
  components: { companyAvatar },
  data() {
    return {
      companyId: '',
      loading: false,
      columns: [
        { slotName: 'number', title: '序号', width: '60px' },
        { dataIndex: 'entName', title: '企业名称', slotName: 'entName' },
        { dataIndex: 'legalPerson', title: '法人', width: '150px' }
        // { dataIndex: 'openStatus', title: '状态', props: { width: '150px' } },
      ],
      dataSource: [],
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      }
    }
  },
  mounted() {
    // const { companyId: id } =
    this.companyId = this.$route.query.companyId as string
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyCompanyDetailBranch({
        companyId: this.companyId,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records = [], total = 0 } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'branch',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
})
</script>

<style lang="less" scoped></style>
