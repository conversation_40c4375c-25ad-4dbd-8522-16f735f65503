<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 15:36:08
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 15:45:05
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/financing.vue
 * @Description: 融资历程
-->
<template>
  <div>
    <p class="title">融资历程</p>

    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.slotName === 'newsSources'">
          <template v-if="!(text.includes('https://') || text.includes('http://'))">
            {{ text }}
          </template>
          <a v-else @click="openWebsite(text)">原文链接</a>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyDetailFinancing } from '@/api/api'
import { defineComponent } from 'vue'

export default defineComponent({
  data() {
    return {
      columns: [
        {
          dataIndex: 'disclosureDate',
          title: '披露日期',
          width: '20%'
        },
        // {
        //   dataIndex: 'newsSources',
        //   slotName: 'newsSources',
        //   title: '新闻来源',
        // },
        {
          dataIndex: 'investor',
          title: '投资方',
          width: '20%'
        },
        // {
        //   dataIndex: 'valuation',
        //   title: '估值',
        // },
        // {
        //   dataIndex: 'proportion',
        //   title: '比例',
        // },
        {
          dataIndex: 'eventDate',
          title: '事件日期',
          width: '20%'
        },
        {
          dataIndex: 'transactionAmount',
          title: '交易金额',
          width: '20%'
        },
        {
          dataIndex: 'financingRounds',
          title: '融资轮次',
          width: '20%'
        }
      ],
      loading: false,
      dataSource: [],
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      },
      queryParams: {
        cId: ''
      }
    }
  },
  mounted() {
    this.queryParams.cId = this.$route.query.companyId as string
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyDetailFinancing({
        ...this.queryParams,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          this.loading = false
          const { records, total } = result

          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'financing',
              isHide: true
            })
          }
        })
        .catch(err => {
          this.loading = false
          console.error(err)
        })
    },

    // // pageSize 改变时触发
    // handlerSizeChange(e) {
    //   this.pagination.pageSize = e
    //   this.getList()
    // },
    // // 第几页改变时触发
    // handlerCurrentChange(e) {
    //   this.pagination.current = e
    //   this.getList()
    // },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    },
    // 点击打开原文链接
    openWebsite(website) {
      console.log('website: ', website)
      if (website) {
        let url = website
        if (!(url.includes('https://') || url.includes('http://'))) {
          url = `http://${url}`
        }
        window.open(url, '_blank')
      }
    }
  }
})
</script>

<style lang="less" scoped>
// .title {
//   position: relative;
//   line-height: 20px;
//   font-size: 16px;
//   display: flex;
//   align-items: center;
//   margin-bottom: 10px;
//   &::before {
//     width: 5px;
//     height: 16px;
//     background-color: #409eff;
//     content: '';
//     display: inline-block;
//     margin-right: 6px;
//   }
// }
</style>
