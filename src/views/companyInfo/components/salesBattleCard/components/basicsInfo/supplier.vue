<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 16:15:46
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/supplier.vue
 * @Description: 供应商
-->
<template>
  <div>
    <p class="title">供应商</p>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.dataIndex === 'supplier'"> <companyAvatar :companyData="record"></companyAvatar> </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailSupplier } from '@/api/api'
import companyAvatar from '@/components/companyAvatar/index.vue'

export default {
  components: { companyAvatar },
  data() {
    return {
      companyId: '',
      loading: false,
      columns: [
        { title: '供应商', dataIndex: 'supplier', slotName: 'entName' },
        { title: '采购占比', dataIndex: 'purchaseProportion', width: 150 },
        { title: '采购金额(万元)', dataIndex: 'purchaseAmount', width: 150 },
        { title: '报告期/公开时间', dataIndex: 'reportingPeriod', width: 150 },
        { title: '数据来源', dataIndex: 'dataSources', width: 150 }
        // {
        //   dataIndex: 'relationship',
        //   title: '关联关系',
        //   props: { width: '150px' },
        // },
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: true // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      }
    }
  },
  mounted() {
    const { companyId: id } = this.$route.query
    this.companyId = id
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyCompanyDetailSupplier({
        companyId: this.companyId,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records = [], total = 0 } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records.map(item => ({
              entName: item.supplier,
              ...item
            }))
            console.log('this.dataSource: ', this.dataSource)
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'supplier',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },

    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped></style>
