<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 15:20:03
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/job.vue
 * @Description: 招聘信息
-->
<template>
  <div>
    <p class="title">招聘信息</p>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.slotName === 'number'">
          <span class="rank">
            {{ pagination.current * pagination.pageSize + index + 1 - pagination.pageSize }}
          </span>
        </template>
        <!-- 公司名称 -->
        <template v-if="column.slotName === 'action'">
          <a @click="openDetail(record)">详情</a>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailJob } from '@/api/api'
import { defineComponent } from 'vue'

export default defineComponent({
  data() {
    return {
      companyId: '',
      loading: false,
      columns: [
        {
          slotName: 'number',
          title: '序号',
          width: '60px'
        },
        { dataIndex: 'publishDate', title: '发布日期' },
        { dataIndex: 'jobName', title: '招聘职位' },
        { dataIndex: 'salary', title: '月薪' },
        { dataIndex: 'education', title: '学历' },
        { dataIndex: 'year', title: '经验' },
        { dataIndex: 'location', title: '办公地点' }
        // {
        //   title: '操作',
        //   slotName: 'action',
        //   props: { width: '100px' },
        // },
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: true // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      }
    }
  },
  mounted() {
    // const { companyId: id } =
    this.companyId = this.$route.query.companyId as string
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyCompanyDetailJob({
        companyId: this.companyId,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records = [], total = 0 } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'job',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    },
    // 打开详情
    openDetail(item) {
      console.log('item', item)
      const { detail } = item
      if (detail) {
        window.open(detail, '_blank')
      }
    }
  }
})
</script>

<style lang="less" scoped></style>
