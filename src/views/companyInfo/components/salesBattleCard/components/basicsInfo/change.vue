<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-18 15:07:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 16:23:43
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/change.vue
 * @Description: 工商变更
-->
<template>
  <div>
    <p class="title">工商变更</p>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    ></a-table>
  </div>
</template>

<script lang="ts">
import { companyDetailChange } from '@/api/api'

export default {
  data() {
    return {
      loading: false,
      columns: [
        { dataIndex: 'changeDate', title: '变更日期', ellipsis: true, width: 120 },
        { dataIndex: 'changeName', title: '变更项目', ellipsis: true, width: 200 },
        { dataIndex: 'oldValue', title: '变更后', ellipsis: true },
        { dataIndex: 'newValue', title: '变更前', ellipsis: true }
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: false // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      },
      queryParams: {
        cId: ''
      }
    }
  },
  mounted() {
    const { companyId: id } = this.$route.query

    this.queryParams.cId = id
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyDetailChange({
        ...this.queryParams,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records, total } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'change',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },

    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
// .title {
//   position: relative;
//   line-height: 20px;
//   font-size: 16px;
//   display: flex;
//   align-items: center;
//   margin-bottom: 10px;
//   &::before {
//     width: 5px;
//     height: 16px;
//     background-color: #409eff;
//     content: '';
//     display: inline-block;
//     margin-right: 6px;
//   }
// }
</style>
