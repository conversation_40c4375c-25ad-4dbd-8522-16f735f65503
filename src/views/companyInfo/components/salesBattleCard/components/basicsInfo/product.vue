<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-18 15:07:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2022-06-08 14:56:08
 * @FilePath: /b2b-market-web/src/globalModal/companyModal/src/components/basicsInfo/product.vue
 * @Description: 产品信息
-->
<template>
  <div>
    <p class="title">产品信息</p>

    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    />
  </div>
</template>

<script lang="ts">
import { companyDetailProduct } from '@/api/api'

export default {
  data() {
    return {
      loading: false,
      tableData: [],
      columns: [
        { dataIndex: 'productName', title: '产品名称' },
        { dataIndex: 'productAbbreviation', title: '产品简称' },
        { dataIndex: 'classification', title: '产品分类' },
        { dataIndex: 'domain', title: '领域' }
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: false // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      },
      queryParams: {
        cId: ''
      }
    }
  },
  mounted() {
    const { companyId: id } = this.$route.query
    this.queryParams.cId = id
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true

      companyDetailProduct({
        ...this.queryParams,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          this.loading = false
          const { records = [], total = 0 } = result
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'product',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },

    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped></style>
