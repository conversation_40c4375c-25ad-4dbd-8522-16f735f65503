<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 15:36:08
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-08-30 18:43:31
 * @FilePath: /corp-elf-web/src/views/companyInfo/components/basicsInfo/businessInformation.vue
 * @Description: 工商信息
-->
<template>
  <div>
    <p class="title">工商信息</p>

    <a-spin :spinning="loading">
      <a-descriptions :column="2" bordered :labelStyle="{ width: '180px' }">
        <a-descriptions-item label="法定代表人"> {{ companyInfo.legalPerson || '-' }} </a-descriptions-item>
        <a-descriptions-item label="经营状态"> {{ companyInfo.openStatus || '-' }} </a-descriptions-item>
        <a-descriptions-item label="注册资本"> {{ companyInfo.regCapital || '-' }} </a-descriptions-item>
        <a-descriptions-item label="实缴资本"> {{ companyInfo.realCapital || '-' }} </a-descriptions-item>
        <a-descriptions-item label="纳税人资质"> {{ companyInfo.taxpayerQualification || '-' }} </a-descriptions-item>
        <a-descriptions-item label="所属行业"> {{ companyInfo.industry || '-' }} </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码"> {{ companyInfo.unifiedCode || '-' }} </a-descriptions-item>
        <a-descriptions-item label="纳税人识别号"> {{ companyInfo.taxNo || '-' }} </a-descriptions-item>
        <a-descriptions-item label="工商注册号"> {{ companyInfo.regNo || '-' }} </a-descriptions-item>
        <a-descriptions-item label="组织机构代码"> {{ companyInfo.orgNo || '-' }} </a-descriptions-item>
        <a-descriptions-item label="登记机关"> {{ companyInfo.authority || '-' }} </a-descriptions-item>
        <a-descriptions-item label="成立日期"> {{ companyInfo.startDate || '-' }} </a-descriptions-item>
        <a-descriptions-item label="企业类型"> {{ companyInfo.entType || '-' }} </a-descriptions-item>
        <a-descriptions-item label="营业期限"> {{ companyInfo.openTime || '-' }} </a-descriptions-item>
        <a-descriptions-item label="所属地区">
          <template v-if="(companyInfo.province || companyInfo.city) && companyInfo.province !== companyInfo.city">
            {{ companyInfo.province || '' }} {{ companyInfo.city || '' }}
          </template>
          <template v-else-if="companyInfo.province === companyInfo.city">
            {{ companyInfo.province }}
          </template>
          <template v-else> - </template>
        </a-descriptions-item>
        <a-descriptions-item label="核准日期"> {{ companyInfo.checkDate || '-' }} </a-descriptions-item>
        <a-descriptions-item label="人员规模"> {{ companyInfo.staffSize || '-' }} </a-descriptions-item>
        <a-descriptions-item label="参保人数"> {{ companyInfo.numberOfInsuredPersons || '-' }} </a-descriptions-item>
        <a-descriptions-item label="曾用名" :span="2">
          {{ companyInfo.oldEntName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="注册地址" :span="2"> {{ companyInfo.regAddr || '-' }} </a-descriptions-item>
        <a-descriptions-item label="经营范围" :span="2"> {{ companyInfo.scope || '-' }} </a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { companyDetailBaseInfo } from '@/api/api'
import { isEmpty } from 'lodash-es'
import { defineComponent } from 'vue'

export default defineComponent({
  data() {
    return {
      loading: false,
      companyInfo: {
        officeAddr: '', // 地址
        realCapital: '', // 实缴资本
        regNo: '', // 工商注册号
        city: '', // 城市
        openTime: '', // 营业期限
        taxpayerQualification: '', // 纳税人资质
        industry: '', // 所属行业
        oldEntName: '', // 曾用名
        staffSize: '', // 人员规模
        openStatus: '', // 经营状态
        englishName: '', // 英文名称
        taxNo: '', // 纳税人识别号
        province: '', // 省份
        checkDate: '', // 核准日期
        scope: '', // 经营范围
        orgNo: '', // 组织机构代码
        createDate: '', // 成立日期
        email: '', // 邮箱
        startDate: '', // 成立时间
        area: '', // 区
        legalPerson: '', // 法定代表人
        website: '', // 网址
        regCapital: '', // 注册资本
        entType: '', // 公司类型
        unifiedCode: '', // 统一社会信用
        licenseNumber: '', // 工商注册号
        phone: '', // 电话
        districtCode: '', // 省份code
        authority: '', // 登记机关
        annualDate: '', // 审核/年检日期
        district: '', // 行政区划
        numberOfInsuredPersons: '', // 参保人数
        regAddr: '', // 注册地址
        entName: '', // 名称
        desc: '' // 简介
      },
      queryParams: {
        cId: ''
      }
    }
  },
  mounted() {
    this.queryParams.cId = this.$route.query.companyId as string
    this.getBaseInfo()
  },
  methods: {
    async getBaseInfo() {
      this.loading = true

      companyDetailBaseInfo({ ...this.queryParams })
        .then(({ result = {} }) => {
          if (!isEmpty(result)) {
            this.companyInfo = {
              ...result,
              // regCapital: _.ceil(result.regCapital, 2),
              province: result.province.replace(/-/g, ''),
              city: result.city.replace(/-/g, ''),
              area: result.area.replace(/-/g, '')
            }
            if (!isEmpty(this.companyInfo.email)) {
              const str = this.companyInfo.email.split('[')[0]
              this.companyInfo.email = str
            }
          } else {
            this.$emit('hideComponents', {
              componentName: 'businessInformation',
              isHide: true
            })
          }

          this.loading = false
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    }
  }
})
</script>

<style lang="less" scoped>
:deep(.ant-descriptions-row) {
  display: flex;
}
</style>
