<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-18 15:07:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 15:45:20
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/investInfo.vue
 * @Description: 对外投资
-->
<template>
  <div>
    <p class="title">对外投资</p>

    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.slotName === 'entName'">
          <router-link
            :to="{
              path: '/companyInfo/index',
              name: 'companyInfo-index',
              query: { companyId: record.companyId || record.cid, companyName: text }
            }"
            v-if="record.companyId || record.cid"
            >{{ text }}</router-link
          >
          <template v-else>{{ text }}</template>
        </template>
        <template v-if="column.slotName === 'legalPerson'">
          <router-link
            :to="{
              path: '/companyInfo/index',
              name: 'companyInfo-index',
              query: { companyId: record.legalId, companyName: text }
            }"
            v-if="record.legalId"
            >{{ text }}</router-link
          >
          <template v-else>{{ text }}</template>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyDetailOutInvest } from '@/api/api'
import { defineComponent } from 'vue'

export default defineComponent({
  data() {
    return {
      columns: [
        {
          dataIndex: 'entName',
          slotName: 'entName',
          title: '被投资企业名称'
          // props: { width: '150px' },
        },
        {
          dataIndex: 'legalPerson',
          slotName: 'legalPerson',
          title: '被投资法定代表人'
        },
        {
          dataIndex: 'startDate',
          title: '成立日期',
          width: '150px'
        },
        {
          dataIndex: 'regRate',
          title: '投资占比',
          width: '150px'
        },
        {
          dataIndex: 'regCapital',
          title: '注册资本',
          width: '150px'
        },
        {
          dataIndex: 'openStatus',
          title: '经营状态',
          width: '80px'
        }
      ],
      dataSource: [],
      loading: false,
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      },
      queryParams: {
        cId: ''
      }
    }
  },
  mounted() {
    this.queryParams.cId = this.$route.query.companyId as string
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyDetailOutInvest({
        ...this.queryParams,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records, total } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'investInfo',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
})
</script>

<style lang="less" scoped></style>
