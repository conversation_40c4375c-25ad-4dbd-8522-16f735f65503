<template>
  <a-card class="basics_info">
    <div class="content">
      <Anchor :anchorList="activeComponentsList" v-model:value="activeAnchor" class="navigation" />

      <div class="basics_list">
        <component
          v-for="(componentItem, index) in activeComponentsList"
          :key="componentItem.key"
          :id="componentItem.componentName"
          :class="['components', componentItem.componentName]"
          :is="componentItem.componentName"
          @hideComponents="handlerHideComponents"
        ></component>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts">
import app from './app.vue'
import bidding from './bidding.vue'
import branch from './branch.vue'
import business from './business.vue'
import businessInformation from './businessInformation.vue'
import certificate from './certificate.vue'
import change from './change.vue'
import client from './client.vue'
import filingWebsite from './filingWebsite.vue'
import financing from './financing.vue'
import investInfo from './investInfo.vue'
import ipo from './ipo.vue'
import job from './job.vue'
// import litigation from './litigation.vue'
import parentCompany from './parentCompany.vue'
import patent from './patent.vue'
import product from './product.vue'
import shareholder from './shareholder.vue'
import Anchor from './anchor.vue'
import softwareBook from './softwareBook.vue'
import supplier from './supplier.vue'
import trademark from './trademark.vue'
import wechat from './wechat.vue'
import { isEmpty, toNumber } from 'lodash-es'
import { companyCompanyDetailPagePreview } from '@/api/api'
import { useRoute } from 'vue-router'
import { computed, defineComponent, onMounted, ref } from 'vue'

export default defineComponent({
  name: 'BasicsInfo',
  components: {
    Anchor,
    businessInformationComp: businessInformation,
    financingComp: financing,
    shareholderComp: shareholder,
    investInfoComp: investInfo,
    changeComp: change,
    parentCompanyComp: parentCompany,
    branchComp: branch,
    supplierComp: supplier,
    clientComp: client,
    biddingComp: bidding,
    ipoComp: ipo,
    certificateComp: certificate,
    trademarkComp: trademark,
    patentComp: patent,
    softwareBookComp: softwareBook,
    productComp: product,
    businessComp: business,
    appComp: app,
    wechatComp: wechat,
    jobComp: job,
    filingWebsiteComp: filingWebsite
    // litigationComp: litigation
  },
  setup() {
    const componentsList = ref([
      { title: '工商信息', componentName: 'businessInformationComp', isHide: false, key: 'businessInformation' },
      { title: '融资历程', componentName: 'financingComp', isHide: false, key: 'financing' },
      { title: '股东信息', componentName: 'shareholderComp', isHide: false, key: 'shareholder' },
      { title: '对外投资', componentName: 'investInfoComp', isHide: false, key: 'investInfo' },
      { title: '工商变更', componentName: 'changeComp', isHide: false, key: 'change' },
      { title: '总公司', componentName: 'parentCompanyComp', isHide: false, key: 'parentCompany' },
      { title: '分支机构', componentName: 'branchComp', isHide: false, key: 'branch' },
      { title: '供应商', componentName: 'supplierComp', isHide: false, key: 'supplier' },
      { title: '客户', componentName: 'clientComp', isHide: false, key: 'client' },
      { title: '招投标', componentName: 'biddingComp', isHide: false, key: 'bidding' },
      { title: '上市信息', componentName: 'ipoComp', isHide: false, key: 'ipo' },
      { title: '资质证书', componentName: 'certificateComp', isHide: false, key: 'certificate' },
      { title: '商标', componentName: 'trademarkComp', isHide: false, key: 'trademark' },
      { title: '专利', componentName: 'patentComp', isHide: false, key: 'patent' },
      { title: '软著', componentName: 'softwareBookComp', isHide: false, key: 'softwareBook' },
      { title: '产品信息', componentName: 'productComp', isHide: false, key: 'product' },
      { title: '企业业务', componentName: 'businessComp', isHide: false, key: 'business' },
      // { title: 'app信息', componentName: 'appComp', isHide: false, key: 'app' },
      { title: '微信公众号', componentName: 'wechatComp', isHide: false, key: 'wechat' },
      { title: '招聘信息', componentName: 'jobComp', isHide: false, key: 'job' },
      { title: '备案网站', componentName: 'filingWebsiteComp', isHide: false, key: 'filingWebsite' }
      // { title: '风险', componentName: 'litigationComp', isHide: false, key: 'litigation' }
    ])

    const activeComponentsList = computed(() => componentsList.value.filter(item => !item.isHide))
    const activeAnchor = ref('businessInformation')

    function handlerHideComponents(e: { componentName: string; isHide: boolean }) {
      console.log('handlerHideComponents:', e)
      const index = componentsList.value.findIndex(item => item.key === e.componentName)
      componentsList.value[index].isHide = e.isHide
    }

    // 提前隐藏一些数据为空的模块
    async function getCompanyDetailPreview() {
      try {
        const route = useRoute()
        const companyId = route.query.companyId as string
        const { result } = await companyCompanyDetailPagePreview({ companyId })
        console.log('getCompanyDetailPreview result: ', result)
        Object.keys(result).forEach((key, index) => {
          const value = toNumber(result[key])
          const componentsItem = componentsList.value.find(item => item.key === key)
          if (!isEmpty(componentsItem)) {
            componentsItem.isHide = value === 0
          }
        })
        console.log('componentsList.value: ', componentsList.value)
      } catch (error) {
        console.error(error)
      }
    }

    onMounted(() => {
      getCompanyDetailPreview()
    })

    return { activeComponentsList, activeAnchor, handlerHideComponents }
  }
})
</script>

<style lang="less" scoped>
@primary-color: var(--g-primary-color, #6553ee);

.basics_info {
  // display: flex;
  // align-items: flex-start;
  width: 100%;

  .content {
    position: relative;
    display: flex;
    align-items: flex-start;
    width: 100%;
    .navigation {
      width: 120px;
      // sticky top-80px
      position: sticky;
      // position: absolute;
      top: 100px;
    }

    .basics_list {
      flex: 1;
      // overflow: hidden;
      // margin-left: 16px;

      .components {
        margin-bottom: 40px;

        :deep(.title) {
          position: relative;
          line-height: 20px;
          font-size: 16px;
          font-weight: 500;
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          &::before {
            width: 2px;
            height: 12px;
            border-radius: 9px;
            background-color: @primary-color;
            content: '';
            display: inline-block;
            margin-right: 6px;
          }
        }
      }
    }
  }
}
</style>
