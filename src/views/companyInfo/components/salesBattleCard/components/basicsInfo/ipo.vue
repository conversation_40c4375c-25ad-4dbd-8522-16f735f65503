<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2022-08-19 17:37:40
 * @FilePath: /corp-elf-web/src/views/companyInfo/components/basicsInfo/ipo.vue
 * @Description: 上市信息
-->
<template>
  <div>
    <p class="title">上市信息</p>

    <a-spin :spinning="loading">
      <a-descriptions :column="3" bordered :labelStyle="{ width: '180px' }">
        <a-descriptions-item label="上市类型"> {{ info.sharesType || '' }} </a-descriptions-item>
        <a-descriptions-item label="总市值"> {{ info.totalMarketValue || '' }} </a-descriptions-item>
        <a-descriptions-item label="更新时间"> {{ info.visitTime || '' }} </a-descriptions-item>
        <a-descriptions-item label="成交额"> {{ info.turnover || '' }} </a-descriptions-item>
        <a-descriptions-item label="成交量"> {{ info.volume || '' }} </a-descriptions-item>
        <a-descriptions-item label="上市日期"> {{ info.marketDate || '' }} </a-descriptions-item>
        <a-descriptions-item label="涨停"> {{ info.increase || '' }} </a-descriptions-item>
        <a-descriptions-item label="跌停"> {{ info.decrease || '' }} </a-descriptions-item>
        <a-descriptions-item label="网上发行日期"> {{ info.internetReleasesDate || '' }} </a-descriptions-item>
        <a-descriptions-item label="市净率"> {{ info.netRate || '' }} </a-descriptions-item>
        <a-descriptions-item label="市盈率"> {{ info.marketProfit || '' }} </a-descriptions-item>
        <a-descriptions-item label="流通市值"> {{ info.marketCapitalization || '' }} </a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailIpo } from '@/api/api'
import { isEmpty } from 'lodash-es'

export default {
  data() {
    return {
      loading: false,
      companyId: '',
      info: {}
    }
  },
  mounted() {
    this.companyId = this.$route.query.companyId
    this.getData()
  },
  methods: {
    getData() {
      this.loading = true
      companyCompanyDetailIpo({ companyId: this.companyId })
        .then(({ result }) => {
          console.log('result: ', result)
          this.loading = false
          if (!isEmpty(result)) {
            this.info = result
          } else {
            this.$emit('hideComponents', {
              componentName: 'ipo',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    }
  }
}
</script>

<style lang="less" scoped></style>
