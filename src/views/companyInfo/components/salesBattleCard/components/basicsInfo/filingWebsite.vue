<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-18 15:07:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-06-09 18:03:49
 * @FilePath: /corp-elf-web/src/views/companyInfo/components/basicsInfo/filingWebsite.vue
 * @Description: 公司详情-备案网站
-->
<template>
  <div>
    <p class="title">备案网站</p>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.slotName === 'number'">
          <span class="rank">
            {{ pagination.current * pagination.pageSize + index + 1 - pagination.pageSize }}
          </span>
        </template>
        <!-- 公司名称 -->
        <template v-if="column.slotName === 'homeSite'">
          <a-space>
            <a :href="transformWebsite(item)" v-for="(item, index) in text" :key="index" target="_blank">
              {{ item }}
            </a>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailWebsite } from '@/api/api'
import { transformWebsite } from '@/utils/util'
import { defineComponent } from 'vue'

export default defineComponent({
  data() {
    return {
      companyId: '',
      loading: false,
      columns: [
        {
          slotName: 'number',
          title: '序号',
          width: '60px'
        },
        { dataIndex: 'siteName', title: '网站名称' },
        { dataIndex: 'homeSite', title: '网址', slotName: 'homeSite' },
        { dataIndex: 'domain', title: '域名', width: '180px' },
        { dataIndex: 'icpNo', title: '网站备案/许可证号' },
        { dataIndex: 'checkDate', title: '审核日期', width: '150px' }
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: true // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      }
    }
  },
  mounted() {
    // const { companyId: id } =
    this.companyId = this.$route.query.companyId as string
    this.getList()
  },
  methods: {
    transformWebsite,
    getList() {
      this.loading = true
      companyCompanyDetailWebsite({
        companyId: this.companyId,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records = [], total = 0 } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records.map(item => ({
              ...item,
              homeSite: item.homeSite.includes(',') ? item.homeSite.split(',') : item.homeSite.split(' ')
            }))
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'filingWebsite',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
})
</script>

<style lang="less" scoped></style>
