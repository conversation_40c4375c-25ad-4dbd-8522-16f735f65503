<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-14 21:14:02
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 16:37:16
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/anchor.vue
 * @Description: 
-->
<template>
  <div class="anchor">
    <!-- :style="{ top: anchorOffsetTop }" -->
    <!-- <ul class="ant-anchor">
      <li class="ant-anchor-ink">
        <span class="ant-anchor-ink-ball visible" ref="inkNodeRef"></span>
      </li>
      <li
        v-for="(anchorItem, key) in anchorList"
        :key="key"
        @click="handlerLinkClick($event, anchorItem)"
        :class="[
          'ant-anchor-link',
          value === anchorItem.componentName ? 'ant-anchor-link-active' : '',
          `ant-anchor-link-${anchorItem.componentName}`
        ]"
      >
        <span
          :class="[
            'ant-anchor-link-title',
            'hoverPrimaryColor',
            value === anchorItem.componentName ? 'ant-anchor-link-title-active' : ''
          ]"
        >
          {{ anchorItem.title }}
        </span>
      </li>
    </ul> -->

    <a-anchor :items="anchorList" :affix="false" :offsetTop="100" :targetOffset="100"></a-anchor>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const emit = defineEmits(['update:value'])
const props = defineProps<{
  anchorList: Array<{
    title: string
    componentName: string
    key: string
    isHide: boolean
  }>
  value?: string
}>()

const anchorList = computed(() => {
  return props.anchorList.map(item => ({
    key: item.key,
    href: `#${item.componentName}`,
    title: item.title
  }))
})

// const { value } = toRefs(props)

// const inkNodeRef = ref()
// async function handlerLinkClick(e, linkItem) {
//   const target = $(`#${linkItem.componentName}`)
//   console.log('target: ', target.offset()?.top)
//   $('html, body').animate({ scrollTop: `${target.offset()?.top - 100}px` }, { duration: 300 })

//   await nextTick()
//   emit('update:value', linkItem.componentName)
//   setActiveLink(e.target)
// }

// watch(
//   () => value?.value,
//   newVal => {
//     if (!isEmpty(newVal)) {
//       const target = document.querySelector(`.ant-anchor-link-${newVal}`)
//       setActiveLink(target)
//     }
//   }
// )

// function setActiveLink(target) {
//   let linkNode
//   if (isElement(target)) {
//     linkNode = target
//   } else {
//     linkNode = document.querySelector(target)
//   }
//   inkNodeRef.value.style.top = `${linkNode.offsetTop + linkNode.clientHeight / 2 - 4.5}px`
// }

// onMounted(() => {
//   nextTick(() => {
//     if (isEmpty(value?.value)) {
//       emit('update:value', props.anchorList[0].componentName)
//     }
//     setActiveLink('.anchor .ant-anchor-link-active')
//   })
// })
</script>

<style scoped lang="less">
.anchor {
}
</style>
