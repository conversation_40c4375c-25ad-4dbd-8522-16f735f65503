<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 16:18:05
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/client.vue
 * @Description: 公司详情-客户
-->
<template>
  <div>
    <p class="title">客户</p>

    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.dataIndex === 'customer'"> <companyAvatar :companyData="record"></companyAvatar></template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailClient } from '@/api/api'
import companyAvatar from '@/components/companyAvatar/index.vue'

export default {
  components: { companyAvatar },
  data() {
    return {
      companyId: '',
      loading: false,
      columns: [
        { dataIndex: 'customer', title: '客户', slotName: 'entName' },
        { dataIndex: 'salesShare', title: '销售占比', width: 150 },
        { dataIndex: 'salesAmount', title: '销售金额(万元)', width: 150 },
        { dataIndex: 'reportingPeriod', title: '报告期/公开时间', width: 150 },
        { dataIndex: 'dataSources', title: '数据来源', width: 150 }
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: true // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      }
    }
  },
  mounted() {
    const { companyId: id } = this.$route.query

    this.companyId = id
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyCompanyDetailClient({
        companyId: this.companyId,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records = [], total = 0 } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records.map(item => ({
              entName: item.customer,
              ...item
            }))
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'client',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },

    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped></style>
