<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 18:01:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 15:44:34
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/parentCompany.vue
 * @Description: 总公司
-->
<template>
  <div>
    <p class="title">总公司</p>
    <a-table :loading="loading" :columns="columns" :dataSource="dataSource">
      <template #bodyCell="{ column, text, record, index }">
        <!-- 公司名称 -->
        <template v-if="column.slotName === 'entName'">
          <companyAvatar :companyData="record"></companyAvatar>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailHead } from '@/api/api'
import companyAvatar from '@/components/companyAvatar/index.vue'
import { isEmpty } from 'lodash-es'
import { defineComponent } from 'vue'

export default defineComponent({
  components: { companyAvatar },
  data() {
    return {
      loading: false,
      companyId: '',
      columns: [
        { dataIndex: 'entName', title: '企业名称', slotName: 'entName', width: '20%' },
        { dataIndex: 'legalPerson', title: '法定代表人', width: '20%' },
        { dataIndex: 'regCap', title: '注册资本', width: '20%' },
        { dataIndex: 'startDate', title: '成立日期', width: '20%' },
        { dataIndex: 'openStatus', title: '状态', width: '20%' }
      ],
      dataSource: [],
      config: {}
    }
  },
  mounted() {
    // const { companyId: id } =
    this.companyId = this.$route.query.companyId as string
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyCompanyDetailHead({ companyId: this.companyId })
        .then(({ result = {} }) => {
          if (!isEmpty(result)) {
            this.dataSource = [result]
          } else {
            this.$emit('hideComponents', {
              componentName: 'parentCompany',
              isHide: true
            })
          }
          this.loading = false
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    }
  }
})
</script>

<style lang="less" scoped></style>
