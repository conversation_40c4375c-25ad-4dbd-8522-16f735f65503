// 公司详情-微信公众号
<template>
  <div>
    <p class="title">微信公众号</p>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyCompanyDetailWechat } from '@/api/api'

export default {
  data() {
    return {
      companyId: '',
      loading: false,
      columns: [
        { dataIndex: 'wechatName', title: '微信公众号', width: 150 },
        { dataIndex: 'wechatId', title: '微信号', width: '150px' },
        { dataIndex: 'wechatDesc', title: '简介' }
      ],
      dataSource: [],
      config: {
        rowKey: 'id',
        height: null,
        pagination: true, // 是否需要分页
        selection: false, // 是否需要多选
        index: true // 是否需要序号
      },
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      }
    }
  },
  mounted() {
    const { companyId: id } = this.$route.query
    this.companyId = id
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyCompanyDetailWechat({
        companyId: this.companyId,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records = [], total = 0 } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'wechat',
              isHide: true
            })
          }
        })
        .catch(err => {
          console.error(err)
          this.loading = false
        })
    },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
.logo {
  width: 50px;
  height: 50px;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
