<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-05-09 15:36:08
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 15:45:13
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/basicsInfo/shareholder.vue
 * @Description: 股东信息
-->
<template>
  <div>
    <p class="title">股东信息</p>

    <a-table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="pagination"
      :loading="loading"
      size="small"
      @change="handlerSizeChange"
    >
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.slotName === 'shareholder'">
          <router-link
            :to="{
              path: '/companyInfo/index',
              name: 'companyInfo-index',
              query: { companyId: record.companyId || record.cid, companyName: text }
            }"
            v-if="record.companyId || record.cid"
            >{{ text }}</router-link
          >
          <template v-else>{{ text }}</template>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { companyDetailShareholder } from '@/api/api'
import { defineComponent } from 'vue'

export default defineComponent({
  data() {
    return {
      columns: [
        {
          dataIndex: 'shareholder',
          slotName: 'shareholder',
          title: '股东',
          width: '33%'
        },
        {
          dataIndex: 'contributionRatio',
          title: '出资比例',
          width: '33%'
        },
        {
          dataIndex: 'regCapital',
          title: '认缴出资额',
          width: '33%'
        }
        // {
        //   dataIndex: 'regDate',
        //   title: '认缴出资日期',
        // },
      ],
      loading: false,
      dataSource: [],
      pagination: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'] // 每页显示个数选择器的选项设置
      },
      queryParams: {
        cId: ''
      }
    }
  },
  mounted() {
    // const { companyId: id } = this.$route.query
    this.queryParams.cId = this.$route.query.companyId as string

    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      companyDetailShareholder({
        ...this.queryParams,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      })
        .then(({ result }) => {
          const { records, total } = result
          this.loading = false
          if (records.length) {
            this.dataSource = records
            this.pagination.total = total
          } else {
            this.$emit('hideComponents', {
              componentName: 'shareholder',
              isHide: true
            })
          }
        })
        .catch(err => {
          this.loading = false
          console.error(err)
        })
    },
    // pageSize 改变时触发
    handlerSizeChange(pagination) {
      console.log('pagination: ', pagination)
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.getList()
    },
    // 第几页改变时触发
    handlerCurrentChange(e) {
      this.pagination.current = e
      this.getList()
    }
  }
})
</script>

<style lang="less" scoped></style>
