<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-16 14:03:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 14:37:03
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/modelScoring.vue
 * @Description: 企业详情-潜客推荐模型评分
-->
<template>
  <a-card class="modelScoring" style="margin-bottom: 16px">
    <a-spin :spinning="loading">
      <a-tabs v-model:activeKey="activeTabKey">
        <template #leftExtra>
          <p class="title">评分</p>
        </template>

        <a-tab-pane v-for="item in modelScoringList" :key="item.title">
          <template #tab>
            <div class="tabTitle flexCenter">
              <span>{{ item.score }}</span>
              <p>{{ item.title }}</p>
            </div>
          </template>

          <div :gutter="[24, 8]" class="flexCenter modelScoringItem">
            <div :span="4" class="totalScore">
              <span>{{ item.score }}</span>
              <p>推荐指数</p>
            </div>

            <div :span="5" v-for="(scoringItem, index) in item.popupDtos" :key="index" class="modelDetail flexCenter">
              <div class="progressBox">
                <!-- :class="scoringItem.score === 0 ? '' : 'mainColor'" -->
                <a-progress
                  type="circle"
                  :percent="scoringItem.percent"
                  :format="() => scoringItem.score"
                  strokeColor="#6553ee"
                  :strokeWidth="8"
                  :width="110"
                />
                <p>{{ scoringItem.title }}</p>
              </div>
              <ul class="conditionBox">
                <li
                  v-for="(item, index) in scoringItem.scoringItems"
                  :key="index"
                  scoringItem
                  :class="[item.isActive ? '' : 'disabledScoreItem', 'flexCenter']"
                >
                  <iconfontIcon
                    :icon="item.isActive ? 'icon-check-circle' : 'icon-close-circle'"
                    :style="{ marginRight: '4px' }"
                    :class="item.isActive ? 'color-#6553ee' : ''"
                  />

                  <span class="active">{{ item.description }}</span>
                </li>
              </ul>
            </div>
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="1">
        <template #tab>
          <div class="tabTitle flexCenter">121212</div>
        </template>
        1212
      </a-tab-pane> -->
      </a-tabs>

      <a-empty
        v-if="modelScoringList.length === 0"
        :image="empty"
        :image-style="{
          height: '164px'
        }"
      >
        <template #description>
          <span class="emptyDescription">无数据</span>
        </template>
      </a-empty>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts" name="modelScoring">
import { onMounted, ref } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useRoute } from 'vue-router'
import { companyCompanyDetailRecommendScore } from '@/api/api'
import { round } from 'lodash-es'
import empty from '@/assets/empty.svg'
import { recommendScoreResType } from '~/types/api/company/companyDetail/recommendScore'

const route = useRoute()

const modelScoringList = ref<recommendScoreResType[]>([])
const activeTabKey = ref('1')
const loading = ref(false)

async function getList() {
  const { companyId } = route.query
  try {
    loading.value = true
    const { result } = await companyCompanyDetailRecommendScore({ companyId: companyId as string })
    modelScoringList.value = result.map(item => {
      return {
        ...item,
        popupDtos: item.popupDtos.map(popupItem => {
          const { totalScore } = popupItem
          let percent
          if (popupItem.score !== 0) {
            percent = round((popupItem.score / totalScore) * 100)
          }
          return { ...popupItem, percent }
        })
      }
    })
    console.log('modelScoringList.value: ', modelScoringList.value)
    activeTabKey.value = modelScoringList.value[0].title
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.modelScoring {
  .title {
    margin-right: 8px;
    width: 36px;
  }

  .tabTitle {
    span {
      font-size: 12px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      border-radius: 50%;
      // background-color: @tag-default-bg;
      text-align: center;
      margin-right: 4px;
    }
  }

  .modelScoringItem {
    // justify-content: space-between;
    flex-wrap: wrap;
    .totalScore {
      width: 20%;
      text-align: center;
      p {
        font-size: 16px;
        color: #a6a6a6;
        font-weight: 550;
      }
      span {
        font-size: 26px;
        font-weight: 550;
        color: #fed55c;
      }
    }

    .modelDetail {
      width: 20%;
      margin-bottom: 8px;
      padding-top: 16px;
      .progressBox {
        margin: 0 auto;
        text-align: center;

        p {
          margin-top: 16px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .conditionBox {
        margin-left: 8px;
        max-height: 80px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
        }

        .disabledScoreItem {
          color: #828282;
        }
      }
    }
  }

  :deep(.ant-tabs-nav) {
    justify-content: flex-end;

    .ant-tabs-extra-content {
      flex: 1;
    }
    .ant-tabs-nav-wrap {
      flex: initial;
    }
  }
}
</style>
