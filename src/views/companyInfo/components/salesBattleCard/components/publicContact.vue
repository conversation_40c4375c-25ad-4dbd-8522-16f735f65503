<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-02-21 14:43:21
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 14:26:40
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/publicContact.vue
 * @Description: 企业详情-公开联系方式
-->
<template>
  <a-card style="margin-bottom: 16px" v-if="loading || contactsList.length !== 0" :bordered="false">
    <div class="publicContact">
      <p class="title">公开联系方式</p>

      <div class="filterBox">
        <p>类型：</p>
        <div class="filterTypeList">
          <a-space>
            <span
              v-for="item in contactTypeList"
              :key="item.value"
              :class="[item.disabled ? 'disabledItem' : '', item.value === contactType ? 'activationItem' : '']"
              @click="handelContactTypeClick(item)"
            >
              {{ item.label }} ({{ item.num }})
            </span>
          </a-space>
        </div>
      </div>

      <a-table
        :loading="loading"
        :columns="columns"
        :dataSource="contactsList"
        :pagination="pagination"
        @change="handlerSizeChange"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.slotName === 'value'">
            <iconfontIcon v-if="record.type === 'LINKED'" icon="icon-lingying" />
            <iconfontIcon v-else-if="record.type === 'MAIMAI'" icon="icon-maimai" />
            <iconfontIcon v-else-if="record.type === 'FIXED'" icon="icon-guhua" />
            <iconfontIcon v-else-if="record.type === 'MAIL'" icon="icon-mail" />
            <iconfontIcon v-else-if="record.type === 'PHONE' || record.type === 'TELEPHONE'" icon="icon-mobile" />
            <span>{{ text }}</span>
          </template>

          <template v-if="column.slotName === 'contactFrom'">
            <a-tag v-for="(item, index) in record.dataFrom" :key="index">{{ item }}</a-tag>
          </template>
        </template>
      </a-table>
    </div>
  </a-card>
</template>

<script setup lang="ts" name="PublicContact">
import { contactV2Type, contactV2CompanyInfo, contactV2CompanyList } from '@/api/api'
import { has, toNumber } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { contactTypeResType } from '~/types/api/contact/type'
import { ContactTypeEnum, contactListResType } from '~/types/api/contact/list'
import { Item } from 'ant-design-vue/es/menu'

interface contactTypeList extends contactTypeResType {
  num: number
}

const route = useRoute()
const { companyId = '' } = route.query

const loading = ref(false)
const contactType = ref<ContactTypeEnum>('ALL')
const contactTypeList = ref<Array<contactTypeList>>([])
const columns = [
  { title: '联系方式', dataIndex: 'value', slotName: 'value' },
  { title: '联系人员', dataIndex: 'name' },
  { title: '数据来源', dataIndex: 'dataFrom', slotName: 'contactFrom' }
]
const contactsList = ref<contactListResType[]>([])
const pagination = ref({
  current: 1, // 当前页数
  pageSize: 10, // 每页显示条目个数
  total: 0, // 总条目数
  showSizeChanger: true, // 显示pagesize修改
  showQuickJumper: false, // 显示快速跳转
  hideOnSinglePage: true, // 只有一页时是否隐藏
  pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
  responsive: true, // 当 size 未指定时，根据屏幕宽度自动调整尺寸
  showTotal: (total: string | number) => `共 ${total} 条`,
  size: 'small'
})

async function getContactType() {
  try {
    const { result: typeList } = await contactV2Type({})
    const { result: typeNum } = await contactV2CompanyInfo({ companyId: companyId as string })

    contactTypeList.value = typeList
      .filter(({ value }) => has(typeNum, value))
      .map(({ value }) => {
        const tempItem: contactTypeList = {
          label: Item.label,
          value: Item.value,
          num: toNumber(typeNum[value]),
          disabled: toNumber(typeNum[value]) === 0
        }
        return tempItem
      })
    console.log('contactTypeList.value: ', contactTypeList.value)
  } catch (error) {
    console.error(error)
  }
}

// 获取联系人列表
function getContactsList() {
  const queryParams = {
    companyId: companyId as string,
    type: contactType.value,
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize
  }

  loading.value = true
  // companyDetailContact
  contactV2CompanyList(queryParams)
    .then(({ result }) => {
      const { records = [], total } = result
      contactsList.value = records.map(item => ({ ...item, dataFromDefaultValue: item.dataFrom[0] }))
      pagination.value.total = total
      loading.value = false
      console.log('contactsList.value: ', contactsList.value)
    })
    .catch(err => {
      console.error(err)
      loading.value = false
    })
}
// 处理联系人类型点击
function handelContactTypeClick(item: contactTypeList) {
  console.log('item: ', item)
  if (item.disabled) {
    return
  }
  contactType.value = item.value
  // this.queryParams.type = item.value
  pagination.value.current = 1
  getContactsList()
}

// pageSize 改变时触发
function handlerSizeChange(pageInfo: { current: number; pageSize: number }) {
  console.log('pagination: ', pagination)
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
  getContactsList()
}

onMounted(async () => {
  await getContactType()
  await getContactsList()
})
</script>

<style lang="less" scoped>
.publicContact {
  position: relative;

  .filterBox {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    border-bottom: 1px solid #f9f9f9;
    padding-bottom: 16px;
    .filterTypeList {
      span {
        white-space: nowrap;
        display: inline-block;
        cursor: pointer;
        color: #595959;
      }
      .activationItem {
        color: var(--g-primary-color, #6553ee);
      }
      .disabledItem {
        color: #bfbfbf;
        cursor: no-drop !important;
      }
    }
  }
}
</style>
