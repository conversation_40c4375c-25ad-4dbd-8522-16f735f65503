<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-27 11:31:42
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-27 13:26:57
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/tags/rankTags.vue
 * @Description: 
-->
<template>
  <div ref="tagContainerRef" class="tagContainer">
    <template v-for="(tagItem, index) in visibleTags" :key="index">
      <a-tag v-if="tagItem.tagLabel !== '...'" :style="{ backgroundColor: tagItem.backgroundColor, color: tagItem.fontColor }">
        {{ tagItem.tagLabel }}
      </a-tag>

      <a-popover v-else trigger="click" title="机构榜单" :getPopupContainer="getPopupContainer">
        <template #content>
          <div class="w700px max-h600px overflow-auto lh-2em">
            <a-tag
              v-for="(tagItem, index) in hideTags"
              :key="index"
              :style="{
                backgroundColor: tagItem.backgroundColor,
                color: tagItem.fontColor
              }"
            >
              {{ tagItem.tagLabel }}
            </a-tag>
          </div>
        </template>
        <span class="hoverPrimaryColor" title="查看更多">...</span>
      </a-popover>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { tagType } from '~/types/common/tagType'
const getPopupContainer = () => document.body

const tagContainerRef = ref<HTMLElement>()
const props = defineProps<{
  tagList: tagType[]
}>()

const hideTags = ref<tagType[]>([])
const visibleTags = computed<tagType[]>(() => {
  // 计算tagContainer的宽度
  const containerWidth = tagContainerRef.value?.offsetWidth || 0
  if (containerWidth === 0) return []
  const tagList = props.tagList
  const tempTags = []
  const maxLine = 5
  let wrapIndex = 1 // 换行下标
  let lineWidth = 0 // 当前行的宽度

  for (let index = 0; index < tagList.length; index++) {
    const element = tagList[index]

    // 计算当前元素的宽度
    const eleWidth = calculateTextWidth(element.tagLabel, {
      fontSize: '12px',
      fontFamily:
        "-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'",
      paddingLeft: 7,
      paddingRight: 7,
      marginRight: 8,
      borderLeft: 1,
      borderRight: 1
    })
    // 在计算第5行的时候要预留省略号的空间 20px
    const _containerWidth = wrapIndex === 4 ? containerWidth - 20 : containerWidth
    // 判断tag的宽度够不够塞入当前行，不够就是要换行
    if (lineWidth + eleWidth >= _containerWidth) {
      wrapIndex += 1
      lineWidth = 0
    } else {
      // 累加当前行的宽度
      lineWidth += eleWidth
    }

    // 如果超过5行，不再计算，跳出循环
    if (wrapIndex >= maxLine) {
      tempTags.push({ tagLabel: '...', tagValue: '...' })
      hideTags.value = tagList.slice(index + 1) // 获取后面的所有数据
      break
    }

    tempTags.push(element)
  }

  return tempTags
})

/**
 * 计算文本总宽度（含边距、边框、内边距）
 * @param {string} text - 待测量的文本内容
 * @param {Object} options - 配置项
 * @param {string} options.fontSize - 字体大小（如 '16px'）
 * @param {string} options.fontFamily - 字体系列（如 'Arial'）
 * @param {number} [options.paddingLeft=0] - 左内边距（像素）
 * @param {number} [options.paddingRight=0] - 右内边距（像素）
 * @param {number} [options.borderLeft=0] - 左边框宽度（像素）
 * @param {number} [options.borderRight=0] - 右边框宽度（像素）
 * @param {number} [options.marginLeft=0] - 左外边距（像素）
 * @param {number} [options.marginRight=0] - 右外边距（像素）
 * @param {string} [options.fontStyle='normal'] - 字体样式（如 'italic'）
 * @param {string} [options.fontWeight='normal'] - 字体粗细（如 'bold'）
 * @returns {number} 总宽度（像素）
 */
function calculateTextWidth(
  text: string,
  {
    fontSize = '12px',
    fontFamily = 'Arial',
    paddingLeft = 0,
    paddingRight = 0,
    borderLeft = 0,
    borderRight = 0,
    marginLeft = 0,
    marginRight = 0
  }
) {
  // 创建离屏Canvas
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!

  // 构建字体字符串（格式: 'italic bold 16px Arial'）
  const font = [fontSize, fontFamily].join(' ')

  ctx.font = font

  // 测量文本宽度
  const textMetrics = ctx.measureText(text)
  const textWidth = textMetrics.width

  // 计算总宽度
  return Math.ceil(textWidth + paddingLeft + paddingRight + borderLeft + borderRight + marginLeft + marginRight)
}
</script>

<style scoped>
:deep(.ant-popover) {
  max-width: 900px;
}
/* .tagContainer {
  flex: 1;
  width: 100%;
} */
</style>
