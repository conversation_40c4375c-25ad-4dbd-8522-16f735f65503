<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-18 15:35:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-02-21 15:19:23
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/tags/clueDynamics.vue
 * @Description: 
-->
<template>
  <a-card style="margin-bottom: 16px" v-if="props.loading || isShow" :bordered="false">
    <p class="title">线索动态</p>
    <a-spin :spinning="props.loading">
      <div class="tagList">
        <div>
          <template v-for="(item, index) in tagInfo">
            <div class="tagItem" v-if="!isEmpty(tagList) && !isEmpty(tagList[item.key])" :key="index">
              <p>{{ item.label }}：</p>
              <div>
                <a-tag
                  v-for="(tagItem, index) in tagList[item.key]"
                  :key="index"
                  :style="{
                    backgroundColor: tagItem.backgroundColor,
                    color: tagItem.fontColor
                  }"
                >
                  {{ tagItem.tagLabel }}
                </a-tag>
              </div>
            </div>
          </template>
        </div>
      </div>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts" name="clueDynamics">
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'
import { companyCompanyDetailPortraitShowTagsResType } from '~/types/api/company/companyDetail/showTags'

const props = defineProps<{ tagList: companyCompanyDetailPortraitShowTagsResType; loading: boolean }>()
const isShow = computed(() => {
  return (
    !isEmpty(props.tagList?.newlyAcquiredFundTags) ||
    !isEmpty(props.tagList?.digitizationTags) ||
    !isEmpty(props.tagList?.biddingChangeTags) ||
    !isEmpty(props.tagList?.jobChangeTags)
  )
})

// 画像-基本信息
const tagInfo = [
  { label: '新获资金', key: 'newlyAcquiredFundTags' },
  { label: '数字化', key: 'digitizationTags' },
  { label: '招中标', key: 'biddingChangeTags' },
  { label: '岗位招聘', key: 'jobChangeTags' }
]
</script>

<style lang="less" scoped></style>
