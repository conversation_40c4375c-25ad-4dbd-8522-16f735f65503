<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-18 15:35:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-26 17:53:25
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/tags/businessDevelopment.vue
 * @Description: 
-->
<template>
  <div v-if="isShow">
    <p class="subTitle">经营信息</p>
    <div class="useProductMask" v-if="!userStore.isVip">
      <div class="maskModal">
        <div class="mainContent">
          <div class="tipsContent">
            <p class="tipsText">成为会员查看企业经营信息</p>
            <a-button type="primary" @click="openVipModal">开通会员</a-button>
          </div>
          <div class="sampleContent">
            <p class="sampleText">样例</p>
            <div class="sampleImg"></div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <template v-for="(item, index) in tagInfo">
        <div class="tagItem" v-if="!isEmpty(tagList) && !isEmpty(tagList[item.key])" :key="index">
          <p>{{ item.label }}：</p>
          <div>
            <a-tag
              v-for="(tagItem, index) in tagList[item.key]"
              :key="index"
              :style="{
                backgroundColor: tagItem.backgroundColor,
                color: tagItem.fontColor
              }"
            >
              {{ tagItem.tagLabel }}
            </a-tag>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts" name="businessDevelopment">
import { useUserStore } from '@/store'
import { useEventBus } from '@vueuse/core'
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'
import { companyCompanyDetailPortraitShowTagsResType } from '~/types/api/company/companyDetail/showTags'

// const props = props
const props = defineProps<{ tagList: companyCompanyDetailPortraitShowTagsResType }>()
const isShow = computed(() => {
  return (
    !isEmpty(props.tagList?.biddingChangeTags) ||
    !isEmpty(props.tagList?.businessInformationTags) ||
    !isEmpty(props.tagList?.financingTags) ||
    !isEmpty(props.tagList?.memberTags) ||
    !isEmpty(props.tagList?.intellectualPropertyTags)
  )
})

// 画像-基本信息
const tagInfo = [
  { label: '招投标', key: 'biddingTags' },
  { label: '关联机构', key: 'businessInformationTags' },
  { label: '投融资', key: 'financingTags' },
  { label: '岗位招聘', key: 'memberTags' },
  { label: '知识产权', key: 'intellectualPropertyTags' }
]

const userStore = useUserStore()
const { emit: openVipModal } = useEventBus('openVipModal')
</script>

<style lang="less" scoped>
.useProductMask {
  @borderRadius: 8px;
  background-image: url('@/assets/images/tagMask.jpg');
  background-repeat: no-repeat;
  background-size: auto;
  @apply flex-center-start;
  height: 500px;
  .maskModal {
    background-image: url('//www.bengine.com.cn/images/footer/footerBg.png');
    background-size: 110%;
    overflow: hidden;
    height: 90%;
    width: 80%;
    border-radius: @borderRadius;
    box-shadow: 0px 2px 10px #00000050;
    padding-top: 48px;
    .mainContent {
      background: #f1f2f3;
      border-radius: @borderRadius;
      height: 100%;
      display: flex;
      flex-direction: column;

      .tipsContent {
        margin-bottom: 16px;
        padding: 16px;
        background: #fff;
        border-radius: @borderRadius;
        box-shadow: 0px -2px 5px #00000025;
        text-align: center;
        .tipsText {
          color: #6553ee;
          font-weight: bold;
          font-size: 20px;
        }
      }
      .sampleContent {
        padding: 32px 32px 16px;
        background: #fff;
        border-radius: @borderRadius;
        flex: 1;
        position: relative;
        .sampleText {
          position: absolute;
          left: 0;
          top: 0;
          background: #fe5370;
          color: #fff;
          padding: 2px 8px;
          overflow: hidden;
          border-radius: @borderRadius 0 @borderRadius 0;
          font-size: 14px;
        }
        .sampleImg {
          border: 1px solid #ccc;
          border-radius: @borderRadius;
          height: 100%;
          background-image: url('@/assets/images/businessDevelopmentSample.jpg');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
        }
      }
    }
  }
}
</style>
