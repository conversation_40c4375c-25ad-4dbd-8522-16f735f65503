<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-18 15:35:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 15:11:05
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/tags/touchdownIntelligence.vue
 * @Description: 
-->
<template>
  <a-card style="margin-bottom: 16px" v-if="props.loading || isShow" :bordered="false">
    <p class="title">触达情报</p>
    <a-spin :spinning="props.loading">
      <div class="tagList">
        <div class="tagItem" v-if="!isEmpty(tagList) && (!isEmpty(tagList.memberLayerTags) || !isEmpty(tagList.covertGenderLayerTags))">
          <p>与关注圈层：</p>
          <div>
            <div v-for="circleLayerItem in circleLayerAssociation" :key="circleLayerItem.key" class="circleLayerItem">
              <a-tag
                v-for="(tagItem, index) in tagList[circleLayerItem.key]"
                :key="index"
                :style="{
                  backgroundColor: tagItem.backgroundColor,
                  color: tagItem.fontColor
                }"
                @click="handlerTagClick(tagItem)"
                class="hoverPrimaryColor"
              >
                {{ tagItem.tagLabel }}
              </a-tag>

              <template v-if="tagList[circleLayerItem.key].length !== 0">{{ circleLayerItem.text }}</template>
            </div>
          </div>
        </div>

        <div
          class="tagItem"
          v-if="
            !isEmpty(tagList) &&
            (!isEmpty(tagList.shareholderCompanyTags) ||
              !isEmpty(tagList.controllingCompanyTags) ||
              !isEmpty(tagList.groupMainCompanyTags) ||
              !isEmpty(tagList.groupMemberCompanyTags) ||
              !isEmpty(tagList.parentCompanyTags) ||
              !isEmpty(tagList.subsidiaryCompanyTags) ||
              !isEmpty(tagList.supplierCompanyTags))
          "
        >
          <p>与关注客户：</p>
          <div>
            <template v-for="circleLayerItem in customerAssociation">
              <div v-if="tagList[circleLayerItem.key].length" :key="circleLayerItem.key" class="circleLayerItem">
                <a-tag
                  v-for="(tagItem, index) in tagList[circleLayerItem.key]"
                  :key="index"
                  :style="{
                    backgroundColor: tagItem.backgroundColor,
                    color: tagItem.fontColor
                  }"
                  @click="handlerTagClick(tagItem)"
                  class="hoverPrimaryColor"
                >
                  {{ tagItem.tagLabel }}
                </a-tag>

                {{ circleLayerItem.text }}
              </div>
            </template>
          </div>
        </div>
      </div>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts" name="touchdownIntelligence">
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { companyCompanyDetailPortraitShowTagsResType } from '~/types/api/company/companyDetail/showTags'
import { tagType } from '~/types/common/tagType'

// const props = props
const props = defineProps<{ tagList: companyCompanyDetailPortraitShowTagsResType; loading: boolean }>()
const isShow = computed(() => {
  return (
    !isEmpty(props.tagList?.memberLayerTags) ||
    !isEmpty(props.tagList?.covertGenderLayerTags) ||
    !isEmpty(props.tagList?.shareholderCompanyTags) ||
    !isEmpty(props.tagList?.controllingCompanyTags) ||
    !isEmpty(props.tagList?.groupMainCompanyTags) ||
    !isEmpty(props.tagList?.groupMemberCompanyTags) ||
    !isEmpty(props.tagList?.parentCompanyTags) ||
    !isEmpty(props.tagList?.subsidiaryCompanyTags) ||
    !isEmpty(props.tagList?.supplierCompanyTags)
  )
})

// 与已领取圈层关联
const circleLayerAssociation = [
  { text: '的成员企业；', key: 'memberLayerTags' },
  { text: '的隐性关联；', key: 'covertGenderLayerTags' }
]
// memberLayerTags?: tagType[] // 触达情报-与已领取圈层关联-成员企业
// covertGenderLayerTags?: tagType[] // 触达情报-与已领取圈层关联-隐性关联
// 与已领取客户关联：
const customerAssociation = [
  { text: '的股东企业；', key: 'controllingCompanyTags' },
  { text: '的控股企业；', key: 'shareholderCompanyTags' },

  { text: '的集团成员企业；', key: 'groupMainCompanyTags' },
  { text: '的同集团成员企业；', key: 'groupMemberCompanyTags' },

  { text: '的母公司；', key: 'subsidiaryCompanyTags' },
  { text: '的分支机构；', key: 'parentCompanyTags' },

  { text: '的供应商；', key: 'clientCompanyTags' },
  { text: '的客户；', key: 'supplierCompanyTags' }

  // parentCompanyTags?: tagType[] // 触达情报-与已领取客户关联-母企业
  // subsidiaryCompanyTags?: tagType[] // 触达情报-与已领取客户关联-分支机构
  // shareholderCompanyTags?: tagType[] // 触达情报-与已领取客户关联-股东企业
  // controllingCompanyTags?: tagType[] // 触达情报-与已领取客户关联-控股企业
  // groupMainCompanyTags?: tagType[] // 触达情报-与已领取客户关联-集团主企业
  // groupMemberCompanyTags?: tagType[] // 触达情报-与已领取客户关联-同集团成员企业
  // supplierCompanyTags?: tagType[] // 触达情报-与已领取客户关联-供应商
  // clientCompanyTags?: tagType[] // 触达情报-与已领取客户关联-客户
]
// shareholderCompanyTags 股东企业?: tagType[] // 触达情报-与已领取客户关联-股东企业
// controllingCompanyTags 控股企业?: tagType[] // 触达情报-与已领取客户关联-控股企业
// groupMainCompanyTags 集团主企业?: tagType[] // 触达情报-与已领取客户关联-集团主企业
// groupMemberCompanyTags 同集团成员企业?: tagType[] // 触达情报-与已领取客户关联-同集团成员企业
// parentCompanyTags 母企业?: tagType[] // 触达情报-与已领取客户关联-母企业
// subsidiaryCompanyTags 分支机构?: tagType[] // 触达情报-与已领取客户关联-分支机构
// supplierCompanyTags 供应商?: tagType[] // 触达情报-与已领取客户关联-供应商
const router = useRouter()
function handlerTagClick(tagItem: tagType) {
  console.log('tagItem: ', tagItem)
  router.push({
    path: '/companyInfo/index',
    name: 'companyInfo-index',
    query: {
      companyId: tagItem.tagValue,
      companyName: tagItem.tagLabel
    }
  })
}
</script>

<style lang="less" scoped>
.circleLayerItem {
  display: inline-block;
  font-size: 12px;
  color: #595959;
  // + .circleLayerItem {
  //   margin-left: 4px;
  // }
}
</style>
