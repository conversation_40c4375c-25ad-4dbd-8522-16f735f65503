<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-08 15:33:41
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-08 15:33:27
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/tags/index.vue
 * @Description: 企业详情-用户画像
-->
<template>
  <div class="tags h100% flex flex-direction-column">
    <touchdownIntelligence :tagList="tagList" :loading="loading" />

    <clueDynamics :tagList="tagList" :loading="loading" />

    <a-card :bordered="false" class="h100%">
      <p class="title">画像</p>
      <a-spin :spinning="loading">
        <div class="tagList">
          <baseInfo :tagList="tagList" />
          <useProduct :tagList="tagList" />
          <businessDevelopment :tagList="tagList" />
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { companyCompanyDetailPortraitShowTags } from '@/api/api'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import touchdownIntelligence from './touchdownIntelligence.vue'
import clueDynamics from './clueDynamics.vue'
import baseInfo from './baseInfo.vue'
import useProduct from './useProduct.vue'
import businessDevelopment from './businessDevelopment.vue'
import { companyCompanyDetailPortraitShowTagsResType } from '~/types/api/company/companyDetail/showTags'

const route = useRoute()
const { companyId = '' } = route.query

const loading = ref(false)
const tagList = ref<companyCompanyDetailPortraitShowTagsResType>({})

async function getTagList() {
  try {
    loading.value = true
    const { result } = await companyCompanyDetailPortraitShowTags({ companyId: companyId as string })
    tagList.value = {
      ...result,
      organizationTypeTags: result.organizationTypeTags?.filter(item => !['ICT代理商', 'ICT集成商'].includes(item.tagLabel))
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

onMounted(() => {
  getTagList()
})
</script>

<style lang="less" scoped>
.tags {
  :deep(.tagList) {
    // padding-left: 10px;
    .subTitle {
      position: relative;
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;

      color: var(--g-primary-color, #6553ee);
    }
    p {
      margin-bottom: 10px;
    }
    .ant-tag {
      margin-bottom: 8px;
    }

    .tagItem {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      + .tagItem {
        margin-top: 8px;
      }

      p {
        min-width: 100px;
        color: #595959;
      }
      div {
        flex: 1;
      }
    }
  }
}
</style>
