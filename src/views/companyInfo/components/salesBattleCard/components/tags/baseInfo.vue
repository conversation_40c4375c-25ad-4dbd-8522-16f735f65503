<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-18 15:35:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-27 12:47:39
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/components/tags/baseInfo.vue
 * @Description: 
-->
<template>
  <div v-if="isShow">
    <p class="subTitle">基本信息</p>
    <div>
      <template v-for="(item, index) in tagInfo">
        <div class="tagItem" v-if="!isEmpty(tagList) && !isEmpty(tagList[item.key])" :key="index">
          <p>{{ item.label }}：</p>
          <template v-if="item.key !== 'rankTags'">
            <div>
              <a-tag
                v-for="(tagItem, index) in tagList[item.key]"
                :key="index"
                :style="{
                  backgroundColor: tagItem.backgroundColor,
                  color: tagItem.fontColor
                }"
              >
                {{ tagItem.tagLabel }}
              </a-tag>
            </div>
          </template>
          <RankTags v-else :tagList="tagList[item.key]" />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts" name="baseInfo">
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'
import { companyCompanyDetailPortraitShowTagsResType } from '~/types/api/company/companyDetail/showTags'
import RankTags from './rankTags.vue'

// const props = props
const props = defineProps<{ tagList: companyCompanyDetailPortraitShowTagsResType }>()
const isShow = computed(() => {
  return (
    !isEmpty(props.tagList?.organizationTypeTags) ||
    !isEmpty(props.tagList?.qualificationTags) ||
    !isEmpty(props.tagList?.rankTags) ||
    !isEmpty(props.tagList?.industryTags) ||
    !isEmpty(props.tagList?.govCategoryTags)
  )
})

// 画像-基本信息
const tagInfo = [
  { label: '机构类型', key: 'organizationTypeTags' },
  { label: '政府认定', key: 'qualificationTags' },
  { label: '机构榜单', key: 'rankTags' },
  { label: '商瞳行业', key: 'industryTags' },
  { label: '工商行业', key: 'govCategoryTags' }
  // { label: '商瞳行业', key: 'industryTags' },
  // { label: '产品服务', key: 'productTags' },
  // { label: '资质认证', key: 'qualificationTags' }
]
</script>

<style lang="less" scoped></style>
