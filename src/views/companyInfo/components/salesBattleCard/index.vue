<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-08 15:31:37
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 16:39:52
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/salesBattleCard/index.vue
 * @Description: 
-->
<template>
  <div class="salesBattleCard h100%">
    <!-- 评分 -->
    <!-- <modelScoring /> -->
    <a-tabs v-model:activeKey="activeKey" class="h100%">
      <!-- salesBattleCard ant-tabs ant-tabs-content tags ant-card -->
      <template #renderTabBar>
        <a-card
          style="margin-bottom: 16px"
          :bordered="false"
          :bodyStyle="{ padding: '0', display: 'flex', alignItems: 'center' }"
          class="overflow-hidden"
        >
          <div
            v-for="item in tabsOptions"
            :key="item.value"
            :class="[item.value === activeKey ? 'primaryColor customTabItem_active' : '', 'hoverPrimaryColor customTabItem transition-all']"
            @click="activeKey = item.value"
          >
            {{ item.label }}
          </div>
          <template #customTab></template>
        </a-card>
      </template>
      <a-tab-pane v-for="item in tabsOptions" :key="item.value" :tab="item.label" class="h100%" :forceRender="item.forceRender">
        <component :is="item.component"></component>
      </a-tab-pane>
    </a-tabs>

    <!--  -->
    <!-- <relevantInformation style="margin-top: 16px" /> -->
    <!--  -->
    <!-- <leadDynamics style="margin-top: 16px" /> -->
    <!-- 触达情报、线索动态、用户画像 -->
    <!-- <corporatePortrait /> -->
    <!-- 商情 -->
    <!-- <EnterpriseDynamics /> -->

    <!-- 公开联系方式 -->
    <!-- <PublicContact /> -->
  </div>
</template>

<script setup lang="ts">
// import modelScoring from './components/modelScoring.vue'
// import relevantInformation from './components/relevantInformation.vue'
// import leadDynamics from './components/leadDynamics.vue'
import { ref } from 'vue'
import { useThemeStore } from '@/store'
import corporatePortrait from './components/tags/index.vue'
import EnterpriseDynamics from '../enterpriseDynamics/index.vue'
import executiveSaid from './components/executiveSaid/index.vue'
import basicsInfo from './components/basicsInfo/index.vue'
// import PublicContact from './components/publicContact.vue'

const { getColorPrimary } = useThemeStore()
const activeKey = ref('1')
const tabsOptions = [
  { label: '画像', value: '1', component: corporatePortrait, forceRender: false },
  { label: '主要动态', value: '2', component: EnterpriseDynamics, forceRender: false },
  { label: '高管说', value: '3', component: executiveSaid, forceRender: false },
  { label: '更多信息', value: '4', component: basicsInfo, forceRender: true }
]
</script>

<style lang="less">
.salesBattleCard {
  .ant-tabs-content {
    height: 100%;
  }

  .title {
    position: relative;
    line-height: 20px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    // &::before {
    //   width: 2px;
    //   height: 12px;
    //   border-radius: 9px;
    //   background-color: var(--g-primary-color, #6553ee);
    //   content: '';
    //   display: inline-block;
    //   margin-right: 6px;
    // }
  }

  .customTabItem {
    width: 100px;
    padding: 12px 0px;
    text-align: center;
    position: relative;
    &_active {
      &::before {
        content: '';
        position: absolute;
        width: 100px;
        height: 2px;
        left: 0;
        bottom: 0px;
        border-radius: 50px;
        background: v-bind(getColorPrimary);
      }
    }
  }
}
</style>
