<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-03-09 16:42:20
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 18:21:11
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/leftCard/components/progressBar.vue
 * @Description: 
-->

<template>
  <div ref="progressBarRef" class="progressBar">
    <p>{{ title }}</p>

    <div class="content">
      <!-- 进度条上方的提示框 -->
      <div
        ref="popperEl"
        class="popper colorBarTip"
        :style="{
          left: popperPosition.left
        }"
        v-show="!isHideFinancing"
      >
        <div class="progressLabel">{{ barData.label }}</div>
        <div
          ref="arrow"
          class="arrow"
          :style="{
            left: arrowPosition.left,
            right: arrowPosition.right
          }"
        ></div>
      </div>
      <!-- 战略投资提示框 -->
      <div
        v-if="barData.isHaveStrategicFinancing"
        class="popper purpleBarTip strategicInvestment"
        ref="strategicFinancingPopper"
        :style="{
          left: strategicFinancing.popper.left,
          right: strategicFinancing.popper.right
        }"
      >
        <div class="progressLabel">{{ strategicFinancing.text }}</div>
        <div
          class="arrow"
          :style="{
            left: strategicFinancing.arrow.left,
            right: strategicFinancing.arrow.right
          }"
        ></div>
      </div>
      <!-- 进度条 -->
      <div class="bar" ref="barEl">
        <div
          ref="reference"
          class="colorBar"
          :style="{
            width: `${progressValue}%`
          }"
        ></div>

        <div class="barLine"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="ProgressBar">
import { isEmpty } from 'lodash-es'
import { computed, nextTick, ref, toRefs, watch } from 'vue'
import { useElementSize } from '@vueuse/core'
import { overviewItem } from '../useParentCompanyAndProgress'

const progressBarRef = ref(null)
const { width, height } = useElementSize(progressBarRef)
watch([width, height], () => {
  initPopper()
})

const props = withDefaults(
  defineProps<{
    color: string
    title: string
    barData: overviewItem
    startSite: string
    endSite: string
  }>(),
  { color: '#9885ED' }
)
const { color, title, barData } = toRefs(props)

const popperPosition = ref({ left: '' })
const arrowPosition = ref({ left: '', right: '' })
const strategicFinancing = ref({ popper: { left: '', right: '' }, arrow: { left: '', right: '' }, text: '' })
const progressValue = ref(0)

const isHideFinancing = computed(() => !!barData.value.isHaveStrategicFinancing && barData.value.value === 0)

watch(
  () => barData,
  newVal => {
    if (!isEmpty(newVal) && !isEmpty(newVal.value)) {
      initPopper()
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const strategicFinancingPopper = ref()
const barEl = ref()
const popperEl = ref()

async function initPopper() {
  if (barData.value.isHaveStrategicFinancing) {
    // 设置显示文字
    if (barData.value.children && barData.value.children.length) {
      strategicFinancing.value.text = barData.value.children[0].label
    }
  }

  await nextTick(() => {
    // 设置正常提示框
    // const {  popper: popperEl, bar: barEl,  } = this.$refs
    const barElWidth = $(barEl.value).width() || 300 // 进度条总宽度
    const popperMoveDistance = Math.round((barElWidth / 100) * barData.value.value) // 移动的距离
    const popperElWidth = Math.round($(popperEl.value).width() || 24) // tips框宽度

    // 判断移动距离
    if (barElWidth - popperMoveDistance < popperElWidth / 2) {
      // 移动距离过大
      popperPosition.value.left = `${popperMoveDistance - popperElWidth}px`
      arrowPosition.value = {
        left: '',
        right: `0px`
      }
    } else if (popperMoveDistance < popperElWidth) {
      // 移动距离过小
      if (popperMoveDistance < popperElWidth / 2) {
        popperPosition.value.left = `${0}px`
        arrowPosition.value = {
          left: `${popperMoveDistance - 5}px`,
          right: ''
        }
      } else {
        popperPosition.value.left = `${popperMoveDistance - popperElWidth / 2}px`
        arrowPosition.value = {
          left: `${popperElWidth === 0 ? 0 : popperElWidth / 2 - 5}px`,
          right: ''
        }
      }
    } else {
      // 移动距离正常
      popperPosition.value.left = `${popperMoveDistance - popperElWidth / 2}px`
      arrowPosition.value = {
        left: `${popperElWidth === 0 ? 0 : popperElWidth / 2 - 5}px`,
        right: ''
      }
    }

    progressValue.value = barData.value.value

    // 战略投资
    if (barData.value.isHaveStrategicFinancing) {
      // // 设置显示文字
      // this.strategicFinancing.text = this.barData.children[0].label
      // 判断进度条位置
      progressValue.value = barData.value.label === '退市' ? barData.value.value + 3 : barData.value.value + 5

      const strategicFinancingPopperWidth = $(strategicFinancingPopper.value).width() || 48 // 战略投资tips框宽度
      const strategicFinancingPopperMoveDistance = Math.round((barElWidth / 100) * progressValue.value) // 战略投资移动的距离

      let popperLeft = 0
      let arrowLeft = 0
      // 判断移动距离
      if (barElWidth - strategicFinancingPopperMoveDistance <= strategicFinancingPopperWidth / 2) {
        strategicFinancing.value.popper.right = `${0}px`
        strategicFinancing.value.arrow.right = `${barElWidth - strategicFinancingPopperMoveDistance - 5}px`
        return
      } else if (strategicFinancingPopperMoveDistance < strategicFinancingPopperWidth) {
        // 移动距离过小 = 0
        if (strategicFinancingPopperMoveDistance < strategicFinancingPopperWidth / 2) {
          console.log(1)
          popperLeft = 0
          arrowLeft = strategicFinancingPopperMoveDistance - 5
        } else {
          popperLeft = strategicFinancingPopperMoveDistance - strategicFinancingPopperWidth / 2

          arrowLeft = strategicFinancingPopperWidth / 2 - 5
        }
      } else {
        // 移动距离正常 = 移动距离 - tips框宽度/2
        popperLeft = strategicFinancingPopperMoveDistance - strategicFinancingPopperWidth / 2
        arrowLeft = strategicFinancingPopperWidth / 2 - 5
      }

      // strategicFinancing.value.popper.left = popperLeft > 0 ? `${popperLeft}px` : ''

      if (popperLeft === 0) {
        strategicFinancing.value.popper.left = `${0}px`
      } else if (popperLeft > 0) {
        strategicFinancing.value.popper.left = `${popperLeft}px`
      } else {
        strategicFinancing.value.popper.left = ``
      }

      if (arrowLeft === 0) {
        strategicFinancing.value.arrow.left = `${0}px`
      } else if (arrowLeft > 0) {
        strategicFinancing.value.arrow.left = `${arrowLeft}px`
      } else {
        strategicFinancing.value.arrow.left = ``
      }
    }
  })
}
</script>

<style lang="less" scoped>
.progressBar {
  flex: 1;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  + .progressBar {
    margin-top: 32px;
  }

  p {
    color: #333333;
    text-align: left;
  }
  .content {
    flex: 1;
    position: relative;
    // display: flex;
    // flex-direction: column;
    // justify-content: flex-end;

    .popper {
      margin-top: 4px;
      position: absolute;
      display: inline-block;
      width: auto;
      .arrow {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border: 5px solid transparent;
        border-top: 5px solid green;
        z-index: 9;
        bottom: -10px;
      }

      .progressLabel {
        display: inline-block;
        word-break: keep-all;
      }
    }
    .colorBarTip {
      color: v-bind(color);
      .arrow {
        border-top: 5px solid v-bind(color);
        color: v-bind(color);
      }
    }
    // .blueBarTip {
    //   color: #5fb5f4;
    //   .arrow {
    //     border-top: 5px solid #5fb5f4;
    //     color: #5fb5f4;
    //   }
    // }
    // .purpleBarTip {
    //   color: #ddbdfd;
    //   .arrow {
    //     border-top: 5px solid #ddbdfd;
    //     color: #ddbdfd;
    //   }
    // }
    // 战略投资
    .strategicInvestment {
      top: 50px;
      color: v-bind(color);

      .arrow {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border: 5px solid transparent;
        border-bottom: 5px solid v-bind(color);
        z-index: 9;
        top: -10px;
      }
    }

    .bar {
      position: relative;
      margin-top: 32px;
      height: 12px;
      border-radius: 12px;
      width: 100%;
      background-color: #e9eeef;
      // &:before {
      //   position: absolute;
      //   left: 90%;
      //   width: 3px;
      //   height: 6px;
      //   background: #fff;
      //   visibility: visible;
      //   content: '';
      //   bottom: 0;
      // }
    }
    .colorBar {
      background-color: v-bind(color);
      border-radius: 12px;
      height: 100%;
    }
    // .blueBar {
    //   background-color: #5fb5f4;
    //   border-radius: 12px;
    //   height: 100%;
    // }
    // .purpleBar {
    //   background-color: #ddbdfd;
    //   border-radius: 12px;
    //   height: 100%;
    // }

    .coordinate {
      margin-top: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #999999;
      position: relative;
    }
  }
}
</style>
