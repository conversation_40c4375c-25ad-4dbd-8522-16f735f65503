/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-08 11:52:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-04 17:35:22
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/leftCard/useParentCompanyAndProgress.ts
 * @Description:
 */
import { companyDetailSurveyV2 } from '@/api/api'
import { isEmpty, toNumber } from 'lodash-es'
import { ref } from 'vue'

export interface overviewItem {
  label: string
  value: number
  isHaveStrategicFinancing: boolean
  children?: overviewChildren[]
}

export interface overviewChildren {
  label: string
  value: string | number
  disabled: boolean
  score?: number
}

const useParentCompany = () => {
  const parentCompany = ref<{ companyName: string; companyId: string }>() // 总公司
  const overviewData = ref<{
    round: overviewItem
    annualRevenue: overviewItem
    staffSize: overviewItem
    cloud: overviewItem
    totalSocre: overviewItem
  }>({
    round: { label: '', value: 0, isHaveStrategicFinancing: false }, // 发展阶段
    annualRevenue: { label: '', value: 0, isHaveStrategicFinancing: false }, // 营收规模
    staffSize: { label: '', value: 0, isHaveStrategicFinancing: false }, // 人员规模
    cloud: { label: '', value: 0, isHaveStrategicFinancing: false }, // 云应用量
    totalSocre: { label: '', value: 0, isHaveStrategicFinancing: false } // 实力指数
  })

  // 获取总公司信息
  async function getCompanyDetailSurvey(id: string) {
    try {
      const { result } = await companyDetailSurveyV2({ companyId: id })
      const { baseCompany, tag } = result
      // 母公司
      parentCompany.value = !isEmpty(baseCompany) ? baseCompany : { companyName: '', companyId: '' }
      // 进度条
      console.log('tag.round: ', tag.round)
      overviewData.value = {
        round: {
          ...tag.round,
          value: toNumber(tag.round.value) || 0,
          isHaveStrategicFinancing: !!(tag.round.children && tag.round.children.length !== 0)
        },
        annualRevenue: {
          ...tag.annualRevenue,
          value: toNumber(tag.annualRevenue.value) || 0,
          isHaveStrategicFinancing: false
        },
        staffSize: {
          ...tag.staffSize,
          value: toNumber(tag.staffSize.value) || 0,
          isHaveStrategicFinancing: false
        },
        cloud: {
          ...tag.cloud,
          value: toNumber(tag.cloud.value) || 0,
          isHaveStrategicFinancing: false
        },
        totalSocre: {
          // ...tag.totalSocre,
          label: tag.totalSocre,
          value: toNumber(tag.totalSocre) || 0,
          isHaveStrategicFinancing: false
        }
      }
      console.log('overviewData.value: ', overviewData.value)
    } catch (error) {
      console.error(error)
    }
  }

  return {
    parentCompany,
    overviewData,
    getCompanyDetailSurvey
  }
}

export default useParentCompany
