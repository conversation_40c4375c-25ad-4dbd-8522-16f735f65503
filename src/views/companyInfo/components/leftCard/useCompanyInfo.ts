/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-19 09:59:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-05-26 14:18:44
 * @FilePath: /corp-elf-web/src/views/companyInfo/components/leftCard/useCompanyInfo.ts
 * @Description:
 */
import { companyDetailBaseInfo } from '@/api/api'
import { transformLocation, transformWebsite } from '@/utils/util'
import { isEmpty } from 'lodash-es'
import { computed, ref } from 'vue'
import { companyBaseInfoResType } from '~/types/api/company/companyDetail/companyBaseInfo'

const useCompanyInfo = () => {
  const companyInfo = ref<companyBaseInfoResType>({
    id: '',
    officeAddr: '',
    realCapital: '',
    regNo: '',
    city: '',
    openTime: '',
    taxpayerQualification: '',
    industry: '',
    oldEntName: '',
    staffSize: '',
    openStatus: '',
    englishName: '',
    taxNo: '',
    province: '',
    checkDate: '',
    scope: '',
    orgNo: '',
    createDate: '',
    email: '',
    startDate: '',
    area: '',
    legalPerson: '',
    website: '',
    regCapital: '',
    entType: '',
    unifiedCode: '',
    licenseNumber: '',
    phone: '',
    districtCode: '',
    authority: '',
    annualDate: '',
    district: '',
    numberOfInsuredPersons: '',
    regAddr: '',
    entName: '',
    desc: '',
    showTags: [],
    shortName: '',
    entLogo: '',
    totalSocre: '',
    updateTime: '',
    cid: ''
  })

  // 公司地址
  const companyLocation = computed(() => transformLocation({ province: companyInfo.value.province, city: companyInfo.value.city }))
  // 公司官网
  const website = computed(() => transformWebsite(companyInfo.value.website))

  // 获取公司信息
  async function getCompanyInfo(id: string) {
    try {
      const { result } = await companyDetailBaseInfo({ cId: id })
      if (!isEmpty(result)) {
        companyInfo.value = result
      }
    } catch (error) {
      console.error(error)
    }
  }

  return {
    companyInfo,
    companyLocation,
    website,
    getCompanyInfo
  }
}

export default useCompanyInfo
