<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-07 20:18:19
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 18:37:07
 * @FilePath: /corp-elf-web-consumer/src/views/companyInfo/components/leftCard/index.vue
 * @Description: 
-->
<template>
  <div class="leftCard">
    <a-card :bordered="false">
      <a-spin :spinning="loading">
        <div class="survey">
          <a-space direction="vertical">
            <div class="companyLogo">
              <a-image
                :width="100"
                :height="100"
                :src="companyInfo.entLogo || companyDefaultLogo"
                :fallback="companyDefaultLogo"
                :preview="false"
              />
            </div>

            <div class="companyNameBox">
              <h1>
                {{ companyInfo.entName }}
                <iconfont-icon icon="icon-copy" class="copyBtn hoverPrimaryColor" @click.stop="copyCompanyName()" />
              </h1>
            </div>

            <div>
              <span> {{ companyLocation }} </span> | <span> {{ companyInfo.createDate }} </span>
              <span v-if="companyInfo.website && companyInfo.website !== '-'"> | <a target="_blank" :href="website">官网</a> </span>
            </div>
            <!-- 
            <div class="companyTag">
              <a-tag v-for="(item, index) in showTags" :key="index">{{ item }}</a-tag>
            </div> -->

            <!-- <div class="scoreTags">
              <a-space size="large">
                <div class="scoreBox">
                  <p>匹配度</p>
                  <h2 class="score">{{ scoreTags.matchingDegree }}</h2>
                </div>
                <div class="scoreBox">
                  <p>意向度</p>
                  <h2 class="score">{{ scoreTags.totalScore }}</h2>
                </div>
              </a-space>
            </div> -->

            <div class="progress">
              <progress-bar color="#696BE8" title="发展阶段" :barData="overviewData.round" startSite="未融资" endSite="上市" />
              <progress-bar color="#FED55C" title="实力指数" :barData="overviewData.totalSocre" startSite="0" endSite="100" />
              <progress-bar color="#8DDDEB" title="营收规模" :barData="overviewData.annualRevenue" startSite="0万元" endSite="1000亿以上" />
              <progress-bar color="#F2637B" title="人员规模" :barData="overviewData.staffSize" startSite="0人" endSite="10000人以上" />
              <!-- <progress-bar color="#EF9966" title="云应用量" :barData="overviewData.cloud" startSite="0万元" endSite="500万元以上" /> -->
            </div>
          </a-space>
        </div>
      </a-spin>
    </a-card>

    <a-card :bordered="false" style="margin-top: 16px">
      <a-spin :spinning="loading">
        <a-space direction="vertical">
          <div>
            <p>简介：</p>
            <p>
              <text-clamp autoResize :text="companyInfo?.desc || '-'" :max-lines="5">
                <template #after="{ clamped, expanded, toggle }">
                  <template v-if="clamped || expanded">
                    <a style="margin-left: 4px" @click="toggle">
                      {{ expanded ? '收起' : '展开' }}
                    </a>
                  </template>
                </template>
              </text-clamp>
            </p>
          </div>

          <div>
            <groupCompany ref="groupCompanyRef" :companyId="companyId" />
          </div>

          <div v-if="parentCompany?.companyName">
            <p>
              总公司：
              <a @click="handlerCompanyClickHandler(parentCompany.companyId, parentCompany.companyName)">
                {{ parentCompany.companyName }}
              </a>
            </p>
          </div>
        </a-space>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import companyDefaultLogo from '@/assets/icon/companyDefaultLogo.svg'
import useParentCompanyAndProgress from './useParentCompanyAndProgress'
import useCompanyInfo from './useCompanyInfo'
import progressBar from './components/progressBar.vue'
import groupCompany from '../groupCompany/index.vue'
import iconfontIcon from '@/components/tools/iconfontIcon'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const companyId = ref<string>('') // 公司id
const { companyId: queryCompanyId } = route.query
companyId.value = queryCompanyId as string

const { parentCompany, overviewData, getCompanyDetailSurvey } = useParentCompanyAndProgress()
const { companyInfo, companyLocation, website, getCompanyInfo } = useCompanyInfo()

// 复制公司名称
const { copy } = useClipboard({ legacy: true })
async function copyCompanyName() {
  copy(companyInfo.value.entName)
    .then(() => {
      message.success('复制成功')
    })
    .catch(err => {
      console.error(err)
      message.error('复制失败')
    })
}

// 打开总公司
function handlerCompanyClickHandler(companyId: string, companyName: string) {
  if (companyId) {
    router.push({
      path: '/companyInfo/index',
      name: 'companyInfo-index',
      query: { companyId, companyName }
    })
  } else {
    message.warning('该组织详情正在准备中')
  }
}

onMounted(async () => {
  try {
    loading.value = true
    await Promise.all([getCompanyInfo(companyId.value), getCompanyDetailSurvey(companyId.value)])
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
})
</script>

<style lang="less" scoped>
.leftCard {
  // position: sticky;
  // top: 80px;
  // overflow: auto;
  .survey {
    text-align: center;

    .companyLogo {
      overflow: hidden;
      :deep(.ant-image-img) {
        height: 100%;
        width: 100%;
        object-fit: contain;
      }
    }

    .ant-space {
      width: 100%;
    }

    .companyNameBox {
      h1 {
        font-size: 20px;
        font-weight: 550;
        a {
          font-size: 14px;
        }
      }
      .copyBtn {
        visibility: hidden;
        // display: none;
        color: #777777;
        font-size: 18px;
      }

      &:hover {
        .copyBtn {
          // display: inline-block;
          visibility: initial;
        }
      }
    }

    .companyTag {
      .ant-tag {
        // color: #595959;
        // background-color: #eae7fb;
        margin-bottom: 8px;
      }
    }

    .scoreTags {
      .ant-space {
        width: auto;
      }
      .scoreBox {
        display: inline-block;
        .score {
          color: var(--g-primary-color, #6553ee);
        }
      }
    }
  }
}
</style>
