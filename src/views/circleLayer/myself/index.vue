<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-16 10:39:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 15:16:29
 * @FilePath: /corp-elf-web-consumer/src/views/circleLayer/myself/index.vue
 * @Description: 
-->
<template>
  <div class="relative flex">
    <a-collapse ghost accordion class="navBox sticky top-80px pr-16px w100px min-w-100px">
      <a-collapse-panel :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <router-link to="/circleLayer/overview"> 圈子概况 </router-link>
          </div>
        </template>
      </a-collapse-panel>
      <a-collapse-panel :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <router-link to="/circleLayer/myself" class="active"> 关注的圈子 </router-link>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>

    <div class="flex-1">
      <FollowCircleTable></FollowCircleTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="circleLayerLibrary">
import FollowCircleTable from '@/views/followList/components/followCircleTable.vue'
</script>

<style lang="less" scoped>
.navBox {
  height: fit-content;
  max-height: calc(100vh - 40px);

  ::v-deep .ant-collapse-content-box {
    padding: 0;
    padding-block: 0 !important;
  }
  ::v-deep .ant-collapse-header {
    padding: 0;
    padding-inline-start: 0 !important;
  }

  .navItem {
    text-align: center;
    margin-bottom: 4px;
    .navTitle {
      a {
        color: #000;
        cursor: pointer;
        line-height: 40px;
        border-radius: 6px;
        font-weight: 500;
        display: block;
        &:hover {
          color: #fff;
          background-color: #6553ee;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }

      .active {
        color: #fff;
        background-color: #6553ee;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
      }
    }
  }
}
</style>
