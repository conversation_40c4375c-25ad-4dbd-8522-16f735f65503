<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-18 20:22:24
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-12 11:30:25
 * @FilePath: /corp-elf-web-consumer/src/views/circleLayer/survey/components/maintenanceCircle/index.vue
 * @Description: 
-->
<template>
  <a-card :border="false" title="已维护圈子" :bodyStyle="{ height: '850px' }">
    <template #extra>
      <a-select
        v-model:value="form.layerLevels"
        size="mini"
        placeholder="级别筛选"
        :options="[
          { label: '国家级', value: '国家级' },
          { label: '省级', value: '省级' },
          { label: '地市级', value: '地市级' },
          { label: '其它', value: '其它' }
        ]"
        style="width: 120px"
        allowClear
        :bordered="false"
        @change="getList"
      ></a-select>
    </template>

    <a-config-provider>
      <template #renderEmpty>
        <empty emptyText="无关注圈子" />
      </template>

      <a-table size="small" :loading="loading" :columns="columns" :dataSource="dataList" :pagination="false" :scroll="{ y: 780 }">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.slotName === 'title'">
            <span class="hoverPrimaryColor" @click="openCircleLayerDetail(record)">{{ text }}</span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { layerSummaryShowReceiveLayerChart } from '@/api/api'
import { randomUUID } from '@/utils/util'
import { isEmpty } from 'lodash-es'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import empty from '@/components/empty/index.vue'

const columns = [
  { title: '排名', dataIndex: 'indexNo', width: 50 },
  { title: '圈子', dataIndex: 'name', slotName: 'title', ellipsis: true },
  { title: '覆盖关注企业数量', dataIndex: 'num', width: 140, align: 'center' },
  { title: '关注企业覆盖率', dataIndex: 'coverage', width: 120, align: 'center' }
]
const dataList = ref([])
const loading = ref(false)

const form = ref<Record<string, any>>({ layerLevels: undefined })
const params = computed(() => {
  const formTemp = Object.keys(form.value)
    .filter(key => !isEmpty(form.value[key]))
    .map(key => {
      const value = form.value[key]
      return {
        fieldName: key,
        fieldValues: [value],
        filedType: 'STR' // STR, DATE
      }
    })
  return { filters: formTemp }
})

async function getList() {
  try {
    loading.value = true
    const { result } = await layerSummaryShowReceiveLayerChart(params.value)
    console.log('params.value: ', params.value)
    dataList.value = result.dataSet?.data.map((item, index) => ({
      id: randomUUID(),
      indexNo: index + 1,
      name: item[0],
      num: item[1],
      coverage: item[2]
    }))
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

const router = useRouter()
function openCircleLayerDetail(item) {
  router.push({
    name: 'circleLayer-detail',
    path: '/circleLayer/detail',
    query: { id: item.id, name: item.name }
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped></style>
