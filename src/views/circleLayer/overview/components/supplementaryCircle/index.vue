<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-18 20:22:24
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-12 11:10:28
 * @FilePath: /corp-elf-web-consumer/src/views/circleLayer/survey/components/supplementaryCircle/index.vue
 * @Description: 
-->
<template>
  <a-card :border="false" title="推荐补充的圈子" :bodyStyle="{ height: '850px' }">
    <template #extra>
      <a-select
        v-model:value="form.layerLevels"
        size="mini"
        placeholder="级别筛选"
        :options="[
          { label: '国家级', value: '国家级' },
          { label: '省级', value: '省级' },
          { label: '地市级', value: '地市级' },
          { label: '其它', value: '其它' }
        ]"
        style="width: 120px"
        allowClear
        :bordered="false"
        @change="getList"
      ></a-select>
    </template>

    <div class="supplementaryCircleMask" v-if="!userStore.isVip">
      <div class="maskModal">
        <div class="mainContent">
          <div class="tipsContent">
            <p class="tipsText">成为会员查看推荐补充的圈子</p>
            <a-button type="primary" @click="openVipModal">开通会员</a-button>
          </div>
          <div class="sampleContent">
            <p class="sampleText">样例</p>
            <div class="sampleImg"></div>
          </div>
        </div>
      </div>
    </div>
    <a-config-provider v-else>
      <template #renderEmpty>
        <empty emptyText="无关注企业或未发现关联关系" />
      </template>
      <a-table size="small" :loading="loading" :columns="columns" :dataSource="dataList" :pagination="false" :scroll="{ y: 780 }">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.slotName === 'title'">
            <span class="hoverPrimaryColor" @click="openCircleLayerDetail(record)">{{ text }}</span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { layerSummaryShowRecommendLayerChart } from '@/api/api'
import { randomUUID } from '@/utils/util'
import { isEmpty } from 'lodash-es'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import empty from '@/components/empty/index.vue'
import { useEventBus } from '@vueuse/core'
import { useUserStore } from '@/store'

const { emit: openVipModal } = useEventBus('openVipModal')
const userStore = useUserStore()

const columns = [
  { title: '排名', dataIndex: 'indexNo', width: 50 },
  { title: '圈子', dataIndex: 'name', slotName: 'title', ellipsis: true },
  { title: '覆盖关注企业数量', dataIndex: 'num', width: 140, align: 'center' },
  { title: '关注企业覆盖率', dataIndex: 'coverage', width: 120, align: 'center' }
]
const dataList = ref([])
const loading = ref(false)

const form = ref<Record<string, any>>({ layerLevels: undefined })
const params = computed(() => {
  const formTemp = Object.keys(form.value)
    .filter(key => !isEmpty(form.value[key]))
    .map(key => {
      const value = form.value[key]
      return {
        fieldName: key,
        fieldValues: [value],
        filedType: 'STR' // STR, DATE
      }
    })
  return { filters: formTemp }
})

async function getList() {
  try {
    loading.value = true
    const { result } = await layerSummaryShowRecommendLayerChart(params.value)
    dataList.value = result.dataSet.data.map((item, index) => ({
      id: randomUUID(),
      indexNo: index + 1,
      name: item[0],
      num: item[1],
      coverage: item[2]
    }))
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

const router = useRouter()
function openCircleLayerDetail(item) {
  router.push({
    name: 'circleLayer-detail',
    path: '/circleLayer/detail',
    query: { id: item.id, name: item.name }
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="less">
.supplementaryCircleMask {
  @borderRadius: 8px;
  background-image: url('@/assets/images/supplementaryCircleMask.jpg');
  background-repeat: no-repeat;
  background-size: contain;
  @apply flex-center-center;
  height: 500px;
  .maskModal {
    background-image: url('//www.bengine.com.cn/images/footer/footerBg.png');
    background-size: 110%;
    overflow: hidden;
    height: 90%;
    width: 80%;
    border-radius: @borderRadius;
    box-shadow: 0px 2px 10px #00000050;
    padding-top: 48px;
    .mainContent {
      background: #f1f2f3;
      border-radius: @borderRadius;
      height: 100%;
      display: flex;
      flex-direction: column;

      .tipsContent {
        margin-bottom: 16px;
        padding: 16px;
        background: #fff;
        border-radius: @borderRadius;
        box-shadow: 0px -2px 5px #00000025;
        text-align: center;
        .tipsText {
          color: #6553ee;
          font-weight: bold;
          font-size: 20px;
          margin-bottom: 8px;
        }
      }
      .sampleContent {
        padding: 32px 32px 16px;
        background: #fff;
        border-radius: @borderRadius;
        flex: 1;
        position: relative;
        .sampleText {
          position: absolute;
          left: 0;
          top: 0;
          background: #fe5370;
          color: #fff;
          padding: 2px 8px;
          overflow: hidden;
          border-radius: @borderRadius 0 @borderRadius 0;
          font-size: 14px;
        }
        .sampleImg {
          border: 1px solid #ccc;
          border-radius: @borderRadius;
          height: 100%;
          background-image: url('@/assets/images/supplementaryCircleSample.jpg');
          background-size: cover;
          background-repeat: no-repeat;
          background-position: top;
        }
      }
    }
  }
}
</style>
