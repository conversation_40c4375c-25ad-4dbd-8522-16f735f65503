<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-16 10:39:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 14:42:31
 * @FilePath: /corp-elf-web-consumer/src/views/circleLayer/overview/index.vue
 * @Description: 
-->
<template>
  <div class="circleLayerSurvey relative flex">
    <a-collapse ghost accordion class="navBox sticky top-80px pr-16px w100px min-w-100px">
      <a-collapse-panel :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <router-link to="/circleLayer/overview" class="active"> 圈子概况 </router-link>
          </div>
        </template>
      </a-collapse-panel>
      <a-collapse-panel :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <router-link to="/circleLayer/myself"> 关注的圈子 </router-link>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>

    <div class="flex-1">
      <div class="flex-center-center pt32px pb48px">
        <a-auto-complete
          ref="autoCompleteRef"
          style="width: 600px"
          size="large"
          :options="searchData"
          :fieldNames="{ label: 'layerName', value: 'id' }"
          v-model:value="searchContent"
          @search="handleSearch"
          @select="handleSelect"
          :open="dropdownVisible"
          @dropdownVisibleChange="dropdownVisibleChange"
        >
          <template #notFoundContent>
            <a-spin v-if="searchLoading" :spinning="searchLoading" />
            <empty v-else-if="!searchLoading && searchData.length === 0" empty-text="无数据" />
          </template>

          <a-input placeholder="圈子名称" size="large">
            <template #prefix>
              <iconfontIcon icon="icon-search" />
            </template>
          </a-input>
        </a-auto-complete>
      </div>

      <a-row :gutter="[16, 16]">
        <a-col :span="24">
          <a-spin :spinning="loading">
            <a-space class="w100%">
              <a-card :border="false"><a-statistic title="圈子总数量" :value="summaryShowData[4]?.[0]" /></a-card>
              <a-card :border="false"><a-statistic title="与圈子互动企业总数量" :value="summaryShowData[5]?.[0]" /></a-card>
              <a-card :border="false"><a-statistic title="关注圈子数量" :value="summaryShowData[0]?.[0]" /></a-card>
              <a-card :border="false"> <a-statistic title="覆盖关注企业数量" :value="summaryShowData[2]?.[0]" /> </a-card>
              <a-card :border="false"> <a-statistic title="关注企业覆盖率" :value="summaryShowData[3]?.[0]" /> </a-card>
            </a-space>
          </a-spin>
        </a-col>
        <a-col :span="12"> <maintenanceCircle /> </a-col>
        <a-col :span="12"> <supplementaryCircle /> </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts" name="circleLayerSurvey">
import supplementaryCircle from './components/supplementaryCircle/index.vue' //  推荐补充的圈层
import maintenanceCircle from './components/maintenanceCircle/index.vue' //  已维护圈层
import { computed, ref, useTemplateRef } from 'vue'
import { layerShowLayer, layerSummaryShowSummaryChart } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { ShowLayerResponse } from '~/types/api/layer/showLayer'
import { debounce, isEmpty, isUndefined } from 'lodash-es'
import empty from '@/components/empty/index.vue'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import { useRouter } from 'vue-router'

const router = useRouter()

const { dataList, loading } = useRequest(layerSummaryShowSummaryChart, { filters: [] })
const summaryShowData = computed<Array<string[] | number[]>>(() => dataList.value?.dataSet.data || [])

// 搜索
const autoCompleteRef = useTemplateRef<InputRef>('autoCompleteRef')
const searchLoading = ref(false)
const lastFetchId = ref(0)
const searchData = ref<ShowLayerResponse[]>([])
const searchContent = ref('')
let controller: AbortController | null = null

const searchReq = debounce((searchText: string) => {
  lastFetchId.value += 1
  controller = new AbortController()
  searchData.value = []
  searchLoading.value = true
  const fetchId = lastFetchId.value
  layerShowLayer(
    {
      filter: {
        isHidCustomer: true,
        layerName: searchText,
        orderColumns: [{ columnName: 'company_cnt', asc: false }]
      }
    },
    controller
  )
    .then(({ result }) => {
      if (fetchId !== lastFetchId.value) {
        return
      }
      searchData.value = result.records
      searchLoading.value = false
    })
    .catch(err => {
      searchLoading.value = false
      console.error(err)
    })
}, 300)

function handleSearch(searchText: string) {
  console.log('searchText: ', searchText)
  const tempText = searchText.trim()
  // 输入框为空处理
  if (isEmpty(tempText)) {
    return
  }
  // 取消之前的请求
  if (controller) {
    controller.abort()
  }
  controller = new AbortController()
  searchReq(tempText)
}

function handleSelect(value: string, options: ShowLayerResponse) {
  console.log('value, options: ', value, options)
  goCircleLayerDetail({ id: options.id, name: options.layerName })
}

const dropdownVisible = ref(false)
function dropdownVisibleChange(status: boolean) {
  dropdownVisible.value = !isEmpty(searchContent.value) && status === true
  console.log('dropdownVisible.value: ', dropdownVisible.value)
}

function goCircleLayerDetail(item: { id: string; name: string }) {
  console.log('item: ', item)

  router
    .push({
      name: 'circleLayer-detail',
      path: '/circleLayer/detail',
      query: { id: item.id, name: item.name }
    })
    .finally(() => {
      searchData.value = []
      searchContent.value = ''
    })
}
</script>

<style lang="less" scoped>
.circleLayerSurvey {
  .navBox {
    height: fit-content;
    max-height: calc(100vh - 40px);

    ::v-deep .ant-collapse-content-box {
      padding: 0;
      padding-block: 0 !important;
    }
    ::v-deep .ant-collapse-header {
      padding: 0;
      padding-inline-start: 0 !important;
    }

    .navItem {
      text-align: center;
      margin-bottom: 4px;
      .navTitle {
        a {
          color: #000;
          cursor: pointer;
          line-height: 40px;
          border-radius: 6px;
          font-weight: 500;
          display: block;
          &:hover {
            color: #fff;
            background-color: #6553ee;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
          }
        }

        .active {
          color: #fff;
          background-color: #6553ee;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }
    }
  }

  ::v-deep .ant-space-item {
    flex: 1;
  }
}
</style>
