<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-16 10:39:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 12:12:03
 * @FilePath: /corp-elf-web-consumer/src/views/circleLayer/detail/index.vue
 * @Description: 
-->
<template>
  <div class="circleLayerDetail">
    <div class="btnBox">
      <a @click="goBack" class="flexCenter"> <iconfontIcon icon="icon-chevron-left" /> 返回</a>

      <div class="btns">
        <a-button
          v-if="!circleLayerInfo?.isReceive"
          size="middle"
          type="primary"
          @click="handlerActionBtnClick('RECEIVE')"
          :loading="receiveLoading"
        >
          关注
        </a-button>
        <a-button v-else size="middle" @click="handlerActionBtnClick('CANCEL')" :loading="receiveLoading"> 取消关注 </a-button>
      </div>
    </div>

    <a-row :gutter="[16, 16]">
      <a-col :span="6">
        <a-card class="leftBox">
          <a-spin :spinning="detailInfoLoading">
            <!-- <div class="logoBox">
              <a-image
                :width="80"
                :height="80"
                :src="circleLayerInfo?.entLogo || companyDefaultLogo"
                :fallback="companyDefaultLogo"
                :preview="false"
              />
            </div> -->

            <div class="nameBox">
              <h1>
                {{ circleLayerInfo?.layerName }}
              </h1>
              <iconfont-icon
                v-if="circleLayerInfo?.layerName"
                icon="icon-copy"
                class="copyBtn hoverPrimaryColor"
                @click.stop="copyName()"
              />
            </div>

            <div>
              <span>
                {{ transformLocation({ province: circleLayerInfo?.province || '', city: circleLayerInfo?.city || '' }) }}
              </span>
              <span v-if="has(circleLayerInfo, 'layerLevel')"> | {{ circleLayerInfo?.layerLevel }} </span>
              <span>
                |
                <template v-if="circleLayerInfo?.website">
                  <a target="_blank" :href="transformWebsite(circleLayerInfo?.website)">官网</a>
                </template>
                <template v-else> - </template>
              </span>
            </div>
          </a-spin>
        </a-card>
      </a-col>

      <a-col :span="18">
        <a-card class="rightBox">
          <a-spin :spinning="detailInfoLoading">
            <a-row :gutter="[16, 16]">
              <a-col :span="24">
                <p>简介：</p>
                <text-clamp autoResize :text="circleLayerInfo?.briefIntroduction || ''" :max-lines="2">
                  <template #after="{ clamped, expanded, toggle }">
                    <template v-if="clamped || expanded">
                      <a @click="handlerOpenDetail">查看全部</a>
                      <!-- <a-popconfirm
                        :title="circleLayerInfo?.briefIntroduction"
                        :showCancel="false"
                        :overlayStyle="{ width: '800px' }"
                      >
                        <template #icon><iconfontIcon icon="icon-root-list" /></template>
                      </a-popconfirm> -->
                    </template>
                  </template>
                </text-clamp>
              </a-col>
              <a-col :span="12">
                <hoverTooltipText :text="`会长/理事长：${circleLayerInfo?.managerName || ''}`"> </hoverTooltipText>
              </a-col>
              <a-col :span="12">
                <hoverTooltipText :text="`秘书长：${circleLayerInfo?.secretaryGeneralName || ''}`"> </hoverTooltipText>
              </a-col>
              <a-col :span="12">
                <hoverTooltipText :text="`联系方式：${circleLayerInfo?.contactInformation || ''}`"> </hoverTooltipText>
              </a-col>
              <a-col :span="12">
                <hoverTooltipText :text="`联系地址：${circleLayerInfo?.contactAddress || ''}`"> </hoverTooltipText>
              </a-col>
            </a-row>
          </a-spin>
        </a-card>
      </a-col>

      <a-col :span="24">
        <Table
          title="关联企业"
          rowKey="id"
          :dataSource="companyList"
          :columns="tableColumns"
          :loading="loading"
          :pagination="pageParams"
          @selectedRowsChange="handlerSelectedRowsChange"
          v-model:sort="sortType"
          :sort-config="[
            { label: '营收规模', value: 'annual_revenue' },
            { label: '实力指数', value: 'powerful_rank_score' }
          ]"
        >
          <template #button>
            <a-button v-show="checkCompanyList.length !== 0" @click="batchAddFollow">关注</a-button>

            <!-- <receiveAssign
              v-show="checkCompanyList.length !== 0"
              ref="receiveAssignRef"
              :companyList="checkCompanyList"
              @refresh="handlerSizeChange(1, pageParams.pageSize)"
              v-model:isHaveCheckBox="isHaveCheckBox"
            /> -->
          </template>
          <template #extra>
            <div style="height: 32px; line-height: 32px">
              <filterForm
                v-model:value="filterFormState"
                :config="filterFormConfig"
                :label-col="6"
                @ok="handlerFilterFun"
                @reset="handlerFilterReset"
              />
            </div>
          </template>

          <template #associationType="{ text }">
            <template v-if="text === '显性'"> 成员企业 </template>
            <template v-if="text === '隐性'"> 隐性关联 </template>
          </template>
          <template #actions="rowItem">
            <moreIcon
              v-show="rowItem.visibility"
              :menuList="[{ title: '关注', key: 'addFollow' }]"
              @click="menuItem => handlerMoreIconClick(menuItem, rowItem)"
            />
          </template>
        </Table>
      </a-col>
    </a-row>

    <a-modal v-model:open="detailVisible" title="圈层简介" :footer="null" :width="800">
      <p>{{ circleLayerInfo?.briefIntroduction }}</p>
    </a-modal>

    <addFollowModal ref="addFollowModalRef" @refresh="getData" />
  </div>
</template>

<script setup lang="ts" name="circleLayerDetail">
import { layerDetailShowDetail, layerDetailShowDetailCompany, layerReceiveLayer, layerUnReceiveLayer } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import useListLoading from '@/hooks/useListLoading'
import { useKeepAliveCache, useSearchField, useUserStore } from '@/store'
import { CascaderProps, message, Modal } from 'ant-design-vue'
import { computed, h, onMounted, reactive, ref, toRefs, useTemplateRef, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import addFollowModal from '@/components/addFollowModal/index.vue'
import filterForm, { filterFromProps } from '@/components/filterForm/index.vue'
import provincesAndRegionsOptions from '@/assets/json/provincesAndRegions.json'
import industryClassificationOptions from '@/assets/json/industryClassification.json'
import { useTransformCascaderData } from '@/hooks/useTransformCascaderData'
import Table from '@comp/table/index'
import moreIcon, { MenuItem } from '@/components/actionIcon/more.vue'
import { transformLocation, transformWebsite } from '@/utils/util'
import { useClipboard, useEventBus } from '@vueuse/core'
import { cloneDeep, has } from 'lodash-es'
import annualRevenueMap from '@/assets/json/annualRevenues.json'
import hoverTooltipText from '@comp/hoverTooltipText/index.vue'
import { layerDetailShowDetailResType } from '~/types/api/layer/detail/showDetail'
import useRequest from '@/hooks/useRequest'
import { companyCardType } from '~/types/common/companyCardType'
import { MenuInfo } from 'ant-design-vue/es/menu/src/interface'

const searchFieldStore = useSearchField()

const { emit: openVipModal } = useEventBus('openVipModal')
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
// const provincesAndRegionsOptions = initAreaData(3)

// 圈层基本信息
const { name: circleLayerName, id: circleLayerId } = route.query
const detailInfoLoading = ref(false)
const circleLayerInfo = ref<layerDetailShowDetailResType['detailInfo']>()

// 获取的圈层详情信息
async function getLayerDetailShowDetail() {
  try {
    detailInfoLoading.value = true
    const { result } = await layerDetailShowDetail({
      layerName: circleLayerName,
      layerId: circleLayerId
    })
    circleLayerInfo.value = result.detailInfo
    detailInfoLoading.value = false
  } catch (error) {
    console.error(error)
    detailInfoLoading.value = false
  }
}

// 返回事件
async function goBack() {
  const keepAliveCache = useKeepAliveCache()
  router.back()
  keepAliveCache.delCachedView(route)
}

// 复制公司名称
const { copy } = useClipboard({ legacy: true })
async function copyName() {
  copy(circleLayerInfo.value?.layerName || '')
    .then(() => message.success('复制成功'))
    .catch(err => {
      console.error(err)
      message.error('复制失败')
    })
}

// 领取退回操作按钮
// const btnsLoading = ref(false)
// const actionBtns = ref<Array<{ label: string; type: string }>>([
//   { label: '领取', type: 'RECEIVE' },
//   { label: '退回', type: 'CANCEL' }
// ])isReceive
// 按钮点击事件
const receiveLoading = ref(false)
async function handlerActionBtnClick(type: 'RECEIVE' | 'CANCEL') {
  console.log('type: ', type)
  try {
    if (!userStore.isVip) {
      openVipModal()
      return
    }
    receiveLoading.value = true
    const params = { layerNames: [circleLayerInfo.value?.layerName] }
    if (type === 'RECEIVE') {
      const { message: msg } = await layerReceiveLayer(params)
      await getLayerDetailShowDetail()
      message.success(msg)
    } else {
      Modal.confirm({
        title: '提示',
        content: '是否取消关注该圈层?',
        icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
        autoFocusButton: null,
        onOk: async () => {
          const { message: msg } = await layerUnReceiveLayer(params)
          await getLayerDetailShowDetail()
          message.success(msg)
        }
      })
    }
    receiveLoading.value = false
  } catch (error) {
    receiveLoading.value = false
    message.success('操作失败')
    console.error(error)
  }
}
// ==============================

const filterFormConfig: filterFromProps['config'] = reactive([
  {
    label: '企业名称',
    key: 'searchContent',
    formItemType: 'input',
    placeholder: '工商名称'
  },
  {
    label: '商瞳行业',
    key: 'tags',
    formItemType: 'cascader',
    mode: 'multiple',
    optionsData: searchFieldStore.industryList
  },
  {
    label: '工商行业',
    key: 'industryClassification',
    formItemType: 'cascader',
    optionsData: industryClassificationOptions,
    mode: 'multiple'
  },
  {
    label: '所属地区',
    key: 'provincesAndRegions',
    formItemType: 'cascader',
    optionsData: provincesAndRegionsOptions,
    mode: 'multiple',
    placeholder: '省/市/区'
  },
  {
    label: '成立年限',
    key: 'establishmentTime',
    formItemType: 'rangeInputNumber',
    max: Infinity,
    placeholder: ['开始年限', '结束年限']
  },
  {
    label: '营收规模',
    key: 'annualRevenues',
    formItemType: 'select',
    mode: 'multiple',
    optionsData: annualRevenueMap
  },
  {
    label: '实力指数',
    key: 'rankPowerfulScore',
    formItemType: 'rangeInputNumber',
    max: Infinity,
    placeholder: ['最低分', '最高分']
  },
  // {
  //   label: '关联圈层数',
  //   key: 'layerCnt',
  //   formItemType: 'rangeInputNumber'
  // },
  {
    label: '关联类型',
    key: 'associationType',
    formItemType: 'select',
    optionsData: [
      { label: '成员企业', value: '显性' },
      { label: '隐性关联', value: '隐性' }
    ]
  },
  {
    label: '客户范围',
    key: 'companyRange',
    formItemType: 'radio',
    optionsData: [
      { label: '全部', value: 'ALL_COMPANY' },
      { label: '我的关注', value: 'MINE_CUSTOMER_COMPANY' }
    ]
  }
])
const filterFormState = ref<Record<string, any>>({ companyRange: 'ALL_COMPANY' }) // 筛选默认值
// 筛选确定回掉
function handlerFilterFun(e: any) {
  handlerSizeChange(1, pageParams.value.pageSize!)
}
// 筛选重置回掉
function handlerFilterReset() {
  filterFormState.value = { companyRange: 'ALL_COMPANY' }
  handlerSizeChange(1, pageParams.value.pageSize!)
}
// 准备筛选器的参数
const filterParams = computed(() => {
  const _filterFormState = cloneDeep(filterFormState.value)

  let returnData = {
    ..._filterFormState
    // ...establishmentTime,
    // ...rankPowerfulScore,
    // tags,
    // industryClassification,
    // provincesAndRegions
  }
  // 成立年限
  let establishmentTime = {}
  if (has(_filterFormState, 'establishmentTime')) {
    establishmentTime = {
      minEstablishmentTime: _filterFormState.establishmentTime[0] || null,
      maxEstablishmentTime: _filterFormState.establishmentTime[1] || null
    }
    delete returnData.establishmentTime
    returnData = {
      ...returnData,
      ...establishmentTime
    }
  }
  // 实力指数
  let rankPowerfulScore = {}
  if (has(_filterFormState, 'rankPowerfulScore')) {
    rankPowerfulScore = {
      minRankPowerfulScore: _filterFormState.rankPowerfulScore[0] || null,
      maxRankPowerfulScore: _filterFormState.rankPowerfulScore[1] || null
    }
    delete returnData.rankPowerfulScore
    returnData = {
      ...returnData,
      ...rankPowerfulScore
    }
  }
  // 关联圈层数
  let layerCnt = {}
  if (has(_filterFormState, 'layerCnt')) {
    layerCnt = {
      minLayerCnt: _filterFormState.layerCnt[0] || null,
      maxLayerCnt: _filterFormState.layerCnt[1] || null
    }
    delete returnData.layerCnt
    returnData = {
      ...returnData,
      ...layerCnt
    }
  }

  // 商瞳行业
  let tags
  if (has(_filterFormState, 'tags')) {
    tags = useTransformCascaderData(_filterFormState.tags, searchFieldStore.industryList as unknown as CascaderProps['options']).map(
      item => item.label
    )
    returnData = {
      ...returnData,
      tags
    }
  }
  // 工商行业
  let industryClassification
  if (has(_filterFormState, 'industryClassification')) {
    industryClassification = useTransformCascaderData(_filterFormState.industryClassification, industryClassificationOptions).map(
      item => item.value
    )
    returnData = {
      ...returnData,
      ...{ industryClassification }
    }
  }
  // 所属地区
  let provincesAndRegions
  console.log('_filterFormState.provincesAndRegions: ', cloneDeep(_filterFormState.provincesAndRegions))
  console.log('provincesAndRegionsOptions: ', provincesAndRegionsOptions)
  if (has(_filterFormState, 'provincesAndRegions')) {
    const n = useTransformCascaderData(_filterFormState.provincesAndRegions, provincesAndRegionsOptions)
    console.log('n: ', n)
    provincesAndRegions = n.map(item => item.value)
    returnData = {
      ...returnData,
      ...{ provincesAndRegions }
    }
  }

  console.log('returnData: ', returnData)
  return returnData
})
// ==============================

// 表格相关参数
const tableColumns = [
  { title: '企业', slotName: 'companyCard' },
  { title: '所属地区', dataIndex: 'address', slotName: 'address', width: '13%', ellipsis: true },
  { title: '成立日期', dataIndex: 'startDate', width: '13%', ellipsis: true },
  { title: '营收规模', dataIndex: 'annualRevenue', slotName: 'annualRevenue', width: '13%', ellipsis: true },
  { title: '实力指数', dataIndex: 'powerfulRankScore', width: '13%', ellipsis: true },
  { title: '关联类型', dataIndex: 'associationType', width: '13%', slotName: 'associationType', ellipsis: true },
  // { title: '关联圈层数', dataIndex: 'layerCnt', width: '13%', ellipsis: true },
  { title: '', slotName: 'actions', width: '5%' }
]

// 选中企业相关
const checkCompanyList = ref<companyCardType[]>([])
function handlerSelectedRowsChange(_keys: string, rows: companyCardType[]) {
  checkCompanyList.value = rows
}
const params = computed(() => ({
  layerName: circleLayerName,
  layerId: circleLayerId,
  filter: {
    ...filterParams.value,
    orderColumns: [{ columnName: sortType.value, asc: false }]
  }
}))

const { dataList, pageParams, loading, getData } = useListLoading(layerDetailShowDetailCompany, params, {
  pageParams: { pageSize: 30, onChange: handlerSizeChange }
})
const companyList = computed(() =>
  dataList.value.map(item => ({ ...item, companyLabels: item.companyLabels.includes('我的客户') ? ['我的关注'] : [] }))
)
// 分页改变
function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}

// 排序方法
const sortType = ref<'powerful_rank_score' | 'annual_revenue'>('powerful_rank_score')
watch(sortType, () => handlerSizeChange(1, pageParams.value.pageSize!))

// 领取指派
const addFollowModalRef = useTemplateRef('addFollowModalRef')
function handlerMoreIconClick(menuItem: MenuItem, rowItem: { record: any }) {
  switch (menuItem.key) {
    case 'addFollow':
      addFollowModalRef.value?.show({ companyId: rowItem.record.cid, companyName: rowItem.record.entName })
      break
    default:
      break
  }
}

// 批量关注
function batchAddFollow() {
  addFollowModalRef.value?.show(checkCompanyList.value.map(item => ({ companyId: item.cid, companyName: item.entName })))
}

// 圈层简介modal
const detailVisible = ref(false)
function handlerOpenDetail() {
  detailVisible.value = true
}

onMounted(() => {
  getLayerDetailShowDetail()
})
</script>

<style lang="less" scoped>
.circleLayerDetail {
  .btnBox {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .leftBox {
    text-align: center;
    height: 100%;
    .nameBox {
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;
      flex-wrap: wrap;
      // h1 {
      //   font-size: 28px;
      // }
    }

    :deep(.ant-card-body) {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .logoBox {
      overflow: hidden;
      margin-bottom: 16px;
      :deep(.ant-image-img) {
        height: 100%;
        width: 100%;
        object-fit: contain;
      }
    }
  }
  .rightBox {
    height: 100%;
    .desc {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
