<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-19 09:59:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 11:32:26
 * @FilePath: /corp-elf-web-consumer/src/views/followCompany/followCompanyList/components/groupCard/index.vue
 * @Description: 
-->
<template>
  <a-spin :spinning="loading">
    <sideNavMenu :navMenu="navMenu" :selectedKeys="activateIndex" @click="handlerNavMenuClick">
      <template #titleText="navItem">
        <div class="flex items-center" v-if="navItem.label === '关注的企业'">
          <p class="flex-1">
            {{ navItem.label }}
            <span class="color-#8c8c8c fs-14px fw400">（{{ countMap[navItem.label] }}）</span>
          </p>
          <a @click="addGroup">
            <iconfont-icon icon="icon-add-circle"></iconfont-icon>
          </a>
        </div>
        <div v-else>
          <p class="navItem hoverPrimaryColor">
            {{ navItem.label }}
            <span class="color-#8c8c8c fs-14px fw400 num">（{{ countMap[navItem.label] }}）</span>
          </p>
        </div>
      </template>
      <template #childrenText="navItem">
        <div class="flex items-center navItem">
          <p class="flex-1 ellipsis">
            {{ navItem.label }}
            <span class="color-#8c8c8c fs-14px fw400 num">（{{ navItem.rawData.companyNum }}）</span>
          </p>
          <template v-if="!['全部', '未分组'].includes(navItem.label)">
            <div class="extra">
              <a-space>
                <a @click.stop="showGroupModal('编辑分组', navItem.rawData)" class="btns">
                  <iconfont-icon icon="icon-edit-1" />
                </a>
                <a @click.stop="deleteGroup(navItem.rawData)" class="btns">
                  <iconfont-icon icon="icon-delete" />
                </a>
              </a-space>
            </div>
          </template>
        </div>
      </template>
    </sideNavMenu>
  </a-spin>
  <!-- <a-card :bordered="false" class="groupCard">
    <a-spin :spinning="loading">
      <div class="myselfGroup">
        <div class="titleBox">
          <p class="title">关注分组</p>
        </div>

        <ul class="groupList">
          <li
            v-for="(item, index) in myselfGroupList"
            :key="index"
            :class="['groupItem', 'transition-all', activateIndex === item.collectId ? 'activeGroupItem' : '']"
            @click="handlerGroupClick(item.collectId)"
          >
            <div class="groupItem_content">
              <div class="flex items-center overflow-hidden">
                <template v-if="item.collectName !== '全部'">
                  <iconfontIcon
                    icon="icon-caret-right-small"
                    :extraCommonProps="{
                      class: ['color-#ccc']
                    }"
                  />
                </template>
                <span class="text"> {{ item.collectName === '全部' ? '关注企业' : item.collectName }} </span>
              </div>
              <span class="num">（{{ item.companyNum || 0 }}）</span>
            </div>

            <div class="groupItem_extra">
              <a-space class="">
                <template v-if="item.collectName === '全部'">
                  <a @click="addGroup">
                    <iconfont-icon icon="icon-add-circle"></iconfont-icon>
                  </a>
                </template>
                <template v-if="!['全部', '未分组'].includes(item.collectName)">
                  <a @click.stop="showGroupModal('编辑分组', item)" class="btns">
                    <iconfont-icon icon="icon-edit-1" />
                  </a>
                  <a @click.stop="deleteGroup(item)" class="btns">
                    <iconfont-icon icon="icon-delete" />
                  </a>
                </template>
              </a-space>
            </div>
          </li>
          <li
            :class="['groupItem', 'transition-all', activateIndex === 'followExecutives' ? 'activeGroupItem' : '']"
            @click="handlerGroupClick('followExecutives')"
          >
            <div class="groupItem_content">
              <div class="flex items-center overflow-hidden">
                <span class="text"> 关注高管 </span>
              </div>
              <span class="num">（{{ executiveCount }}）</span>
            </div>
          </li>
        </ul>
      </div>
    </a-spin> -->

  <!-- </a-card> -->
  <myselfGroupModal ref="myselfGroupModalRef" @refresh="getGroupList" />
</template>

<script lang="ts" setup name="groupList">
import { getCustomerCollectGroup, customerCollectDelete, executiveRelationCount, layerShowLayer } from '@/api/api'
import { message, Modal } from 'ant-design-vue'
import { computed, h, nextTick, onMounted, ref } from 'vue'
import myselfGroupModal from './components/myselfGroupModal.vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useVModel } from '@vueuse/core'
import { getCustomerCollectGroupResponseType } from '~/types/api/customer/getCustomerCollectGroup'
import useRequest from '@/hooks/useRequest'
import sideNavMenu, { BaseNavItem } from '@/components/tools/sideNavMenu'
import useListLoading from '@/hooks/useListLoading'
import { LocationQuery, onBeforeRouteUpdate, RouteLocationNormalized, useRoute } from 'vue-router'

const route = useRoute()
const navMenu = computed(() => [
  {
    label: '关注的企业',
    key: '关注的企业',
    icon: 'icon-chart-bubble',
    type: 'followCompany',
    children: (myselfGroupList.value || []).map(item => ({
      label: item.collectName,
      key: item.collectId,
      icon: 'icon-point',
      rawData: item,
      type: 'followCompany'
    }))
  },
  { label: '关注的高管', key: '关注的高管', icon: 'icon-chart-bubble', type: 'followExecutives' },
  { label: '关注的圈子', key: '关注的圈子', icon: 'icon-chart-bubble', type: 'followCircle' }
])

// const emits = defineEmits(['update:collectId'])
// const props = defineProps<{ collectId: string }>()
// // 当前高亮的分组
// const activateIndex = useVModel(props, 'collectId', emits)

// 分组数据获取
const { dataList: myselfGroupList, loading, getData: getGroupList } = useRequest(getCustomerCollectGroup, { appType: 'LITE' })

// 删除分组
async function deleteGroup(item: getCustomerCollectGroupResponseType) {
  if (item.companyNum !== 0) {
    message.error('删除失败！在执行删除操作前，请确保将该分组中的企业重新分配到其他已存在的分组中')
    return
  }
  Modal.confirm({
    title: '提示',
    content: '是否删除该分组？',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    onOk: async () => {
      const { message: msg } = await customerCollectDelete({ appType: 'LITE', collectId: item.collectId })
      message.success(msg)
      // 判断删除的分组是否当前高亮显示的分组
      if (activateIndex.value === item.collectId) {
        activateIndex.value = '-1'
      }
      getGroupList()
    }
  })
}

// 新增、修改个人分组
const myselfGroupModalRef = ref()
function showGroupModal(title: '新增分组' | '编辑分组', editItem?: getCustomerCollectGroupResponseType) {
  const nameList = myselfGroupList
    .value!.map(item => item.collectName)
    .filter(item => (title === '编辑分组' ? item !== editItem?.collectName : true))
  console.log('nameList: ', nameList)

  myselfGroupModalRef.value.show(title, nameList, editItem)
}
function addGroup() {
  if (myselfGroupList.value!.filter(item => item.collectName !== '全部').length >= 6) {
    message.warning('最多可添加6个分组')
    return
  }
  showGroupModal('新增分组')
}

// // 分组点击事件
// function handlerGroupClick(groupItemId: string) {
//   console.log('groupItemId: ', groupItemId)
//   activateIndex.value = groupItemId
// }

const { dataList: executiveCount } = useRequest(executiveRelationCount)
const { pageParams: circlePageParams } = useListLoading(layerShowLayer, {
  filter: {
    isOwner: true,
    isOnlyOwner: true,
    orderColumns: [{ columnName: 'company_cnt', asc: false }]
  }
})
const countMap = computed<Record<string, number>>(() => ({
  关注的企业: myselfGroupList.value?.[0].companyNum || 0,
  关注的高管: executiveCount.value || 0,
  关注的圈子: circlePageParams.value.total || 0
}))
console.log('countMap.value: ', countMap.value)

function handlerNavMenuClick(e: BaseNavItem) {}

const activateIndex = ref('-1')
function setActivateIndex(route: RouteLocationNormalized) {
  //   关注的企业
  // 关注的高管
  // 关注的圈子
  const { query, fullPath } = route
  switch (fullPath) {
    case 'search':
      activateIndex.value = '高级搜索'
      break
    case 'model':
      activateIndex.value = id as string
      break
    case 'followCompany':
      activateIndex.value = collectId as string
      break
    case 'ignore':
      activateIndex.value = '忽略企业'
      break

    default:
      break
  }
}

onMounted(() => {
  setActivateIndex(route)
})

onBeforeRouteUpdate(to => {
  setActivateIndex(to)
})

// defineExpose({ refresh: getGroupList })
</script>

<style lang="less" scoped>
.extra {
  height: 30px;
  display: none;
  text-align: right;
}
.navItem {
  &:hover {
    .extra {
      display: block;
    }
    .num {
      color: #baabff;
    }
  }
}
</style>
