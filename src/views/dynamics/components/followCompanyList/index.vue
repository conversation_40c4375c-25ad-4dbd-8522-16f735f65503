<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 13:56:55
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 14:34:51
 * @FilePath: /corp-elf-web-consumer/src/views/dynamics/components/followCompanyList/index.vue
 * @Description: 
-->
<template>
  <a-card
    :bordered="false"
    class="followCompanyList"
    :bodyStyle="{
      minHeight: '490px',
      display: 'flex',
      flexDirection: 'column'
    }"
  >
    <template #title>
      <div class="flex">
        <p :style="{ color: themeStore.getColorPrimary }">关注企业</p>
        <a-input
          v-model:value="companyName"
          class="ml-8px fs-14px fw-400"
          allowClear
          size="small"
          placeholder="搜索已关注企业"
          style="width: 180px"
          @change="handlerCompanyChange"
        ></a-input>
      </div>
    </template>
    <template #extra>
      <router-link to="/followCompany/list">管理关注企业</router-link>
    </template>

    <a-space wrap>
      <span
        v-for="(item, index) in groupList"
        :key="index"
        @click="handlerGroupClick(item)"
        :class="[activateCollectId === item.collectId ? 'color-#000' : 'color-#999', 'cursor-pointer hover-color-#000']"
      >
        {{ item.collectName }}({{ item.companyNum }})
      </span>
    </a-space>

    <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
      <a-table :columns="columns" :data-source="dataList" :pagination="false" :loading="loading" size="small" class="mt24px flex-1">
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'entName'">
            <div class="flex">
              <a-tag>{{ record.collectName }}</a-tag>
              <p
                class="flex-1 ellipsis hoverPrimaryColor"
                @click="openCompanyInfo({ companyId: record.companyId, companyName: record.entName })"
              >
                {{ text }}
              </p>
            </div>
          </template>
          <!-- <template v-if="column.dataIndex === 'events'">
            <template v-if="text.split('、').length > 1">
              <a-tooltip :getPopupContainer="getPopupContainer">
                <template #title>{{ text }}</template>
                <span class="fs-14 secondaryText">{{ text.split('、')[0] }}...</span>
              </a-tooltip>
            </template>
            <template v-else>
              <span class="fs-14 secondaryText">{{ text }}</span>
            </template>
          </template> -->
          <template v-if="column.dataIndex === 'address'">
            {{ transformLocation(record) }}
          </template>
        </template>
      </a-table>
    </a-config-provider>
    <div class="flex items-center justify-between mt16px">
      <!-- <a class="fs-12px" @click="addFollowCompanyRef.show()"><PlusOutlined class="mr-8px" />关注</a> -->
      <a class="fs-14px" @click="addFollowCompanyRef?.show()"><PlusOutlined class="mr-8px" />关注</a>
      <a-pagination
        size="small"
        :current="pageParams.current"
        :pageSize="pageParams.pageSize"
        :total="pageParams.total"
        :showSizeChanger="false"
        :showQuickJumper="false"
        @change="handlerSizeChange"
      />
    </div>
    <addFollowCompany ref="addFollowCompanyRef" @refresh="refresh" modal-type="1" />
  </a-card>
</template>

<script setup lang="ts">
import { getCustomerCollectGroup, customerList } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { PlusOutlined } from '@ant-design/icons-vue'
import { computed, onActivated, onMounted, ref } from 'vue'
import addFollowCompany from '@comp/addFollowModal/index.vue'
import { useThemeStore } from '@/store'
import { debounce } from 'lodash-es'
import { useRouter } from 'vue-router'
import { transformLocation } from '@/utils/util'
import { getCustomerCollectGroupResponseType } from '~/types/api/customer/getCustomerCollectGroup'
import { customerListRequestType } from '~/types/api/customer/list'

const themeStore = useThemeStore()
const companyName = ref()
const activateCollectId = ref('-1')
const addFollowCompanyRef = ref<InstanceType<typeof addFollowCompany>>() // 添加关注企业弹窗
const columns = [
  { title: '企业名称', dataIndex: 'entName', ellipsis: true },
  // { title: '近期动态', dataIndex: 'events', width: '120px', ellipsis: true }
  { title: '地区', dataIndex: 'address', slotName: 'address', width: '120px', ellipsis: true }
]
const params = computed<customerListRequestType>(() => ({
  collectId: activateCollectId.value,
  companyName: companyName.value,
  isFirst: false,
  customerType: 'PERSONAL',
  isAsc: false,
  orderColumns: [
    {
      columnName: 'powerful_rank_score',
      asc: false
    }
  ]
}))
const {
  dataList,
  pageParams,
  loading,
  getData,
  refresh: refreshTableData
} = useListLoading(customerList, params, { immediateReqData: false })

/**
 * @description: 分页改变
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}

// 获取分组列表
const groupList = ref<getCustomerCollectGroupResponseType[]>([])
function getCustomerCollectList() {
  getCustomerCollectGroup({ appType: 'LITE' })
    .then(({ result }) => {
      groupList.value = result
    })
    .catch(err => {
      console.error(err)
    })
}

function handlerGroupClick(item: getCustomerCollectGroupResponseType) {
  activateCollectId.value = item.collectId
  refreshTableData()
}

const handlerCompanyChange = debounce(refreshTableData, 300)

const router = useRouter()
function openCompanyInfo(item: { companyId: string; companyName: string }) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.companyId,
      companyName: item.companyName
    }
  })
}
// const getPopupContainer = (_triggerNode, _dialogContext) => document.body

// 刷新方法
function refresh() {
  getCustomerCollectList()
  refreshTableData()
}

onMounted(() => {
  refresh()
  // getCustomerCollectList()
})

onActivated(() => {
  // console.log(12122)
})
</script>

<style lang="less" scoped>
.followCompanyList {
  .secondaryText {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
