<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 17:17:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 17:30:01
 * @FilePath: /corp-elf-web-consumer/src/views/dynamics/components/circle/index.vue
 * @Description: 行业圈子
-->
<template>
  <!-- :where(.css-4fkknx).ant-table-wrapper .ant-table.ant-table-small -->
  <a-card :bordered="false" class="customCircle">
    <template #title>
      <p>
        <span>客户圈子</span>
        <a-tooltip :getPopupContainer="getPopupContainer" :overlayStyle="{ maxWidth: '438px' }" class="ml8px">
          <template #title>
            展示关联已关注企业最多的高价值圈子，通过与圈子共办活动可以批量对企业施加影响。关联来自圈子成员企业或高频活动互动。
          </template>
          <iconfontIcon
            icon="icon-info-circle"
            :extra-common-props="{
              style: {
                color: '#666',
                fontSize: '16px'
              }
            }"
          />
        </a-tooltip>
      </p>
    </template>
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
      <a-table size="small" :dataSource="dataList" :columns="columns" :loading="loading" :pagination="false">
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.dataIndex === 'layerName'">
            <router-link
              :to="{
                path: '/circleLayer/detail',
                query: { name: text, id: record.id }
              }"
              class="color-#000000E0"
              >{{ text }}</router-link
            >
          </template>
          <template v-if="column.dataIndex === 'clientCnt'">
            <span :style="{ color: theme.getColorPrimary }" class="pr16px">
              {{ text ? text : '-' }}
            </span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script lang="ts">
export default {
  name: 'circleList'
}
</script>

<script setup lang="ts">
import { layerShowLayer } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { useThemeStore } from '@/store'
import { computed, onActivated, onMounted } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { ShowLayerRequest } from '~/types/api/layer/showLayer'

const getPopupContainer = (_triggerNode: HTMLElement) => document.body
const theme = useThemeStore()

const columns = [
  { title: '圈层', dataIndex: 'layerName', ellipsis: true },
  { title: '关联关注企业', dataIndex: 'clientCnt', width: 140, align: 'center' }
  // { title: '活跃度', dataIndex: 'activityLevel', width: 80 }
]

const params = computed<ShowLayerRequest>(() => ({
  pageNo: 1,
  pageSize: 10,
  filter: {
    // industryId: props.industryId!,
    isOnlyOwner: true,
    isHidCustomer: false,
    orderColumns: [
      {
        columnName: 'client_cnt2',
        asc: false
      }
    ]
  }
}))
const { getData, dataList, loading } = useListLoading(layerShowLayer, params, { immediateReqData: false })

onMounted(() => {
  getData()
})
</script>

<style lang="less" scoped></style>
