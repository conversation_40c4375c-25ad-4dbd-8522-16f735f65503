<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 17:17:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-19 12:10:44
 * @FilePath: /corp-elf-web-consumer/src/views/followCompany/overview/components/dynamicNews/index.vue
 * @Description: 行业热点
-->
<template>
  <a-card :bordered="false" :bodyStyle="{ padding: 0 }" class="dynamicNews">
    <template #title>
      <div class="flex justify-between items-center">
        <p>动态</p>
        <a-divider type="vertical" />
        <a-space class="flex-1">
          <a-segmented v-model:value="activeDynamicNewsKey" :options="dynamicNewsTypeList" @change="refresh" />
        </a-space>
      </div>
    </template>
    <template #extra>
      <a-select
        size="small"
        placeholder="请选择"
        v-model:value="selectTimeType"
        @change="refresh"
        :options="timeTypeData"
        :bordered="false"
      >
      </a-select>
    </template>

    <a-config-provider>
      <template #renderEmpty>
        <div class="text-center">
          <empty>
            <template #description>
              <p class="fs-16px my16px">当前分组下无企业数据</p>
              <p class="block mb16px color-#ccc">开始添加您的目标企业</p>
              <a-button type="primary" shape="round" class="inline-flex items-center justify-center" @click="addFollowCompanyRef?.show">
                <template #icon><PlusOutlined /></template>
                关注企业
              </a-button>
            </template>
          </empty>
        </div>
      </template>

      <a-list class="list" item-layout="horizontal" :data-source="dataList" :loading="{ spinning: infiniteLoading, tip: '加载中...' }">
        <template #loadMore>
          <div v-if="dataList.length > 0" class="loadMore py-8px">
            <div
              v-if="isEmpty(userStore.getToken)"
              class="inline-flex items-center justify-center hoverPrimaryColor endText"
              @click="showLoginModal"
            >
              <iconfontIcon icon="icon-lock-on" />
              <p class="mx4px">登录即可获取更多数据</p>
              <iconfontIcon icon="icon-chevron-down" />
            </div>
            <div v-else v-intersection-observer="handlerIntersectionObserver" class="endText">
              <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
              <p v-else>没有更多了</p>
            </div>
          </div>
        </template>
        <template #renderItem="{ item }">
          <newsCard :newsData="item" @click="newsCardClick(item)" :collectId="activeDynamicNewsKey" />
        </template>
      </a-list>
    </a-config-provider>
  </a-card>

  <addFollowCompany ref="addFollowCompanyRef" @refresh="refresh" :collectId="activeDynamicNewsKey" modal-type="1" />
  <!-- <newsDetailModal ref="newsDetailModalRef" :collectId="activeDynamicNewsKey" /> -->
</template>

<script setup lang="ts">
import { industryHotNews, getCustomerCollectGroup } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { useThemeStore, useUserStore } from '@/store'
import { computed, ref, onActivated, onMounted } from 'vue'
import { vIntersectionObserver } from '@vueuse/components'
import newsCard from '@/components/newsCard/index.vue'
import { HotNewsRequest, HotNewsResponse } from '~/types/api/industry/hotNews'
import { SegmentedOption } from 'ant-design-vue/es/segmented/src/segmented'
// import newsDetailModal from '@/components/newsCard/newsCardDetailModal.vue'
import empty from '@/components/empty/index.vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import addFollowCompany from '@comp/addFollowModal/index.vue'
import { findIndex, isEmpty } from 'lodash-es'
import showLoginModal from '@/components/loginModal'
import iconfontIcon from '@/components/tools/iconfontIcon'

const theme = useThemeStore()
const userStore = useUserStore()
// const newsDetailModalRef = ref()

const addFollowCompanyRef = ref<InstanceType<typeof addFollowCompany>>() // 添加关注企业弹窗
const activeDynamicNewsKey = ref<string>('-1') // 当前选中的分组类型
const dynamicNewsTypeList = ref<SegmentedOption[]>()
const dynamicNewsTypeLoading = ref(false)
// 获取关注企业类型列表
async function getCompanyTypeList() {
  try {
    dynamicNewsTypeLoading.value = true
    const { result } = await getCustomerCollectGroup({ appType: 'LITE' })
    console.log('result: ', result)
    dynamicNewsTypeList.value = result.map(item => ({ label: item.collectName, value: item.collectId, total: item.companyNum }))

    // 判断当前类型是否在列表中
    const index = findIndex(dynamicNewsTypeList.value, { value: activeDynamicNewsKey.value })
    if (index === -1) {
      activeDynamicNewsKey.value = dynamicNewsTypeList.value[0].value as string
    }
    dynamicNewsTypeLoading.value = false
  } catch (error) {
    dynamicNewsTypeLoading.value = false
    console.error(error)
  }
}

const selectTimeType = ref<string>('ALL') // 选中新闻时间范围
const timeTypeData = [
  { label: '所有时间', value: 'ALL' },
  { label: '最近3天', value: 'DAY' },
  { label: '最近1周', value: 'WEEK' },
  { label: '近1个月', value: 'MONTH' }
]
const selectNewsType = ref<string>('全部事件') // 选中新闻类型
// const newsTypeData = [
//   '全部事件',
//   'IPO上市',
//   '对外投资',
//   '企业融资',
//   '企业破产',
//   '亏损',
//   '高管变动',
//   '企业裁员',
//   '新品发布',
//   '战略合作',
//   '办会参会',
//   '新机构成立',
//   '考察拜访',
//   '高管发声',
//   '出海',
//   '奖励',
//   '行业研究',
//   '政策',
//   '其它'
// ]

// 获取关注企业动态的参数
const params = computed<HotNewsRequest>(() => ({
  collectId: activeDynamicNewsKey.value as string,
  timeRange: selectTimeType.value,
  eventTypes: selectNewsType.value === '全部事件' ? undefined : [selectNewsType.value]
}))
const {
  dataList,
  noMore,
  loading: infiniteLoading,
  refresh,
  onLoadMore
} = useInfiniteLoading(industryHotNews, params, { immediateReqData: false })

// 滚动到界面底部回调方法
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !infiniteLoading.value && !noMore.value) {
    onLoadMore()
  }
}

function newsCardClick(item: HotNewsResponse) {
  window.open(item.oriUrl, '_blank')
}
// onMounted(async () => {
//   await getCompanyTypeList()
//   getData()
// })

onMounted(async () => {
  // console.log(12122)
  await getCompanyTypeList()
  refresh()
})
</script>

<style lang="less" scoped>
.dynamicNews {
  :deep(.ant-select-selector) {
    color: #999;
  }
}
</style>
