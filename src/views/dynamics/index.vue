<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 13:56:55
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-02 15:13:46
 * @FilePath: /corp-elf-web-consumer/src/views/followCompany/overview/index.vue
 * @Description: 
-->
<template>
  <div class="followCompanyOverview">
    <a-row :gutter="[16, 16]">
      <a-col span="16">
        <dynamicNews />
      </a-col>
      <a-col span="8">
        <div ref="rightBoxRef">
          <followCompanyList class="mb16px" />
          <circleComp class="mb16px" />
          <companyWrapper />
        </div>

        <div v-show="!targetIsVisible" class="stickyBox sticky top-80px">
          <followCompanyList />
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import dynamicNews from './components/dynamicNews/index.vue'
import followCompanyList from './components/followCompanyList/index.vue'
import circleComp from './components/circle/index.vue'
import companyWrapper from '@comp/companyWrapper/index.vue'
import { useElementVisibility } from '@vueuse/core'
import { ref } from 'vue'

const rightBoxRef = ref()
const targetIsVisible = useElementVisibility(rightBoxRef)
</script>

<style lang="less" scoped>
.stickyBox {
  height: fit-content;
  max-height: calc(100vh - 64px);
}

.followCompanyOverview {
  min-height: calc(100vh - 64px - 32px);
}
</style>
