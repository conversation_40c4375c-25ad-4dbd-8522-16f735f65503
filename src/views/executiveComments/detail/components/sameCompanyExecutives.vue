<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-02 10:40:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-15 11:18:16
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/detail/components/sameCompanyExecutives.vue
 * @Description: 高管详情-同公司高管
-->
<template>
  <a-card :bordered="false" class="sameCompanyExecutives" title="同企业高管">
    <a-spin :spinning="loading">
      <a-config-provider :theme="{ components: { Table: { fontSize: '16px', margin: '16px 0 0' } } }">
        <template #renderEmpty>
          <empty empty-text="无同企业高管信息" />
        </template>

        <a-table
          size="small"
          :dataSource="dataList"
          :columns="columns"
          :loading="loading"
          :pagination="{
            ...pageParams,
            onChange: handlerSizeChange
          }"
        >
          <template
            #bodyCell="{ text, column, record }: { text: string, column: TableColumnProps, record: listSameCompanyExecutiveResType }"
          >
            <template v-if="column.dataIndex === 'executiveName'">
              <router-link
                class="color-#000000E0"
                :to="{
                  name: 'executiveComments-detail',
                  path: '/executiveComments/detail',
                  query: { executiveId: record.executiveId, executiveName: record.executiveName }
                }"
                >{{ text }}</router-link
              >
            </template>
            <template v-if="column.dataIndex === 'postList'">
              <div class="flex-1 flex flex-direction-column justify-center fs-14px">
                <p class="ellipsis">{{ record.postList.map(item => item.post).join('、') }}</p>
                <router-link
                  class="color-#7F7F7F ellipsis"
                  :to="{
                    path: '/companyInfo/index',
                    name: 'companyInfo-index',
                    query: { companyId: record.companyUniId, companyName: record.companyName }
                  }"
                >
                  {{ record.companyName }}
                </router-link>
              </div>
            </template>
          </template>
        </a-table>
      </a-config-provider>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
import { executiveSaidListSameCompanyExecutive } from '@/api/api'
import { useThemeStore } from '@/store'
import { TableColumnProps } from 'ant-design-vue'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { listSameCompanyExecutiveResType } from '~/types/api/executiveSaid/listSameCompanyExecutive'
import empty from '@/components/empty/index.vue'
import useListLoading from '@/hooks/useListLoading'

const route = useRoute()
const { executiveId } = route.query

const theme = useThemeStore()

const columns = [
  { title: '姓名', dataIndex: 'executiveName', ellipsis: true, width: 140 },
  { title: '企业岗位', dataIndex: 'postList', ellipsis: true }
]

const params = ref({ executiveId: executiveId as string })
const { dataList, loading, pageParams, getData } = useListLoading(executiveSaidListSameCompanyExecutive, params)

/**
 * @description: 分页改变
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}
</script>

<style lang="less" scoped>
.sameCompanyExecutives {
}
</style>
