<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-01-17 16:06:22
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 16:44:19
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/detail/components/Interpret.vue
 * @Description: 
-->
<template>
  <a-card class="interpret" title="商瞳解读" :loading="loading">
    <!-- 提示词 -->
    <div class="my16px">
      <a-space>
        <span class="promptsItem min-h32px" v-for="(item, index) in promptData" :key="index" @click="openInterPret(item)">
          {{ item }}
        </span>
      </a-space>
    </div>

    <div class="py-16px">
      <Sender v-model:problem="problem" :sendLoading="false" @send-msg="openInterPret" />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { h, ref } from 'vue'
import Sender from '../../interpret/components/chat/components/sender.vue'
import { executeSaidExplainTips } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import { useRoute, useRouter } from 'vue-router'
import { isUndefined } from 'lodash-es'
import { Modal } from 'ant-design-vue'
import iconfontIcon from '@/components/tools/iconfontIcon'

const props = withDefaults(defineProps<{ isRelation?: boolean }>(), {
  isRelation: false
})
const route = useRoute()
const router = useRouter()

const { executiveId, executiveName } = route.query
const problem = ref('')

// 获取提示词
const { dataList: promptData, loading } = useRequest(executeSaidExplainTips, { executiveId: executiveId, executiveName: executiveName })

function openInterPret(_problem?: string) {
  if (!props.isRelation) {
    // 请先关注
    Modal.info({
      title: '提示',
      content: '请先关注该高管',
      icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
      autoFocusButton: 'ok',
      onCancel() {},
      onOk: () => {}
    })
    return
  }
  router.push({
    path: '/executiveComments/interpret',
    query: {
      problem: isUndefined(_problem) ? problem.value : _problem,
      executiveId: executiveId,
      executiveName: executiveName
    }
  })
}
</script>

<style lang="less" scoped>
.promptsItem {
  @apply transition-all p8px bg-#fff border-solid border-1px border-c-#e8eaf2 br-12px fs-16px cursor-pointer hover:(color-#6553ee bg-#eaeafd border-c-#baabff);

  box-shadow: 0 2px 4px -10px rgba(54, 54, 73, 0.04), 0 2px 5px 0 rgba(51, 51, 71, 0.08), 0 0 1px 0 rgba(44, 44, 54, 0.02);
}
</style>
