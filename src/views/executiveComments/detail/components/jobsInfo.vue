<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-02 10:39:44
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 17:27:13
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/detail/components/jobsInfo.vue
 * @Description: 高管详情-高管任职信息
-->
<template>
  <a-card :bordered="false">
    <a-spin :spinning="loading">
      <template v-if="executiveInfo.length !== 0">
        <div class="mb20px text-center">
          <span class="fs-20px fw500">{{ executiveInfo?.[0]?.executiveName }}</span>
          <!-- <span class="border-1px border-solid border-#fccb3a color-#fccb3a w44px display-inline-block border-radius-22px ml8px">高管</span> -->
        </div>
        <a-timeline>
          <a-timeline-item v-for="(item, index) in executiveInfo" :key="index">
            <a-tooltip>
              <template #title>最新摘录岗位时间：{{ getPublishDate(item.latestExcerptTime) }}</template>
              <div class="content fs-16px ellipsis">
                {{ item.postList.map(item => item.post).join('、') }}
              </div>
            </a-tooltip>
            <a-typography-text
              type="secondary"
              class="color-#7F7F7F hoverPrimaryColor"
              @click="openCompanyInfo({ companyId: item.companyUniId, companyName: item.companyName })"
            >
              {{ item.companyName }}
            </a-typography-text>
          </a-timeline-item>
        </a-timeline>
      </template>
      <template v-else><empty /></template>
    </a-spin>
    <template #tabBarExtraContent></template>
  </a-card>
</template>

<script setup lang="ts">
import { executiveSaidExecutiveDetail } from '@/api/api'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { executiveDetailResType } from '~/types/api/executiveSaid/executiveDetail'
import empty from '@/components/empty/index.vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const { executiveId } = route.query

const loading = ref(false)
const executiveInfo = ref<executiveDetailResType[]>([])

// 获取高管任职信息
async function getData() {
  try {
    loading.value = true
    const { result } = await executiveSaidExecutiveDetail({ executiveId: executiveId as string })
    executiveInfo.value = result
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}
function openCompanyInfo(item: { companyId: string; companyName: string }) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.companyId,
      companyName: item.companyName
    }
  })
}

/** 解析日期 */
const getPublishDate = (date: number) => {
  const diffDay = dayjs(date).set('h', 0).set('m', 0).set('s', 0)
  const nowDay = dayjs().set('h', 0).set('m', 0).set('s', 0)
  const diffRes = nowDay.diff(diffDay, 'd')
  if (diffRes < 1) {
    return '今日'
  } else if (diffRes === 1) {
    return '昨日'
  } else {
    return dayjs(date).format('M月D日')
  }
}

onMounted(() => {
  getData()
})
</script>

<style lang="less" scoped>
:deep(.ant-timeline-item-last) {
  padding-bottom: 0px !important;
}
</style>
