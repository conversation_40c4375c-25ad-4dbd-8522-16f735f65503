<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-02 10:39:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-10 11:36:21
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/detail/components/comments.vue
 * @Description: 高管详情-发表言论
-->
<template>
  <div class="h100%">
    <a-card :bordered="false" :bodyStyle="{ padding: '0' }" class="h100%">
      <!-- : '0px' -->
      <template #title>
        <div class="flex items-center">
          <p :style="{ color: theme.getColorPrimary, marginRight: '8px' }">发表言论</p>

          <div class="flex items-center">
            <iconfontIcon
              icon="icon-search"
              :extra-common-props="{ class: 'fs-18px hoverPrimaryColor mr-8px color-#7F7F7F' }"
              @click="handlerSearchIconBtnClick"
            />
            <a-input
              v-show="showKeyWordInput"
              ref="keywordRef"
              v-model:value="filterParams.keyword"
              style="width: 220px"
              class="!fs-14 fw-400 transition-all"
              placeholder="关键词搜索"
              size="small"
              @change="handlerKeyWordChange"
              @blur="handlerKeyWordBlur"
              allowClear
            ></a-input>
          </div>
        </div>
      </template>

      <div class="listContent">
        <a-config-provider :theme="{ components: { List: { paddingContentVertical: 16, paddingContentHorizontalLG: 16 } } }">
          <a-list :loading="infiniteLoading" item-layout="horizontal" :data-source="dataList" split>
            <template #renderItem="{ item }: { item: executiveSaidListResType }">
              <a-list-item>
                <executiveSaidCard :item="item" :show-field="['publicDate', 'content', 'sourceUrl']" class="p0! w100%" />
              </a-list-item>
            </template>

            <template #loadMore>
              <div v-if="dataList.length > 0" class="loadMore py-8px" v-intersection-observer="handlerIntersectionObserver">
                <div class="commentsMask" v-if="!userStore.isVip && pageParams.total! > 2">
                  <a-result>
                    <template #icon> <img src="@/assets/images/vip.png" class="m-0 m-auto w96px" /> </template>
                    <template #title>
                      <span class="color-#fea127">
                        开通会员查看剩余{{ isEmpty(pageParams.total) ? '内容' : `${pageParams.total}条内容` }}
                      </span>
                    </template>
                    <template #extra> <a-button type="primary" @click="openVipModal">开通会员</a-button> </template>
                  </a-result>
                </div>
                <p class="endText" v-else-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                <p class="endText" v-else>没有更多了</p>
              </div>
            </template>
          </a-list>
        </a-config-provider>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { executiveSaidList } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { useThemeStore, useUserStore } from '@/store'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import { debounce, isEmpty } from 'lodash-es'
import { nextTick, ref } from 'vue'
import { vIntersectionObserver } from '@vueuse/components'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { executiveSaidListReqType, executiveSaidListResType } from '~/types/api/executiveSaid/executiveSaidList'
import { useRoute } from 'vue-router'
import executiveSaidCard from '@/components/executiveSaidCard/index.vue'
import { useEventBus } from '@vueuse/core'

const userStore = useUserStore()
const { emit: openVipModal } = useEventBus('openVipModal')
const theme = useThemeStore()
const route = useRoute()
const { executiveId } = route.query

const filterParams = ref<executiveSaidListReqType>({ keyword: undefined, executiveId: executiveId as string, showPostType: 3 })
const { dataList, loading: infiniteLoading, noMore, onLoadMore, refresh, pageParams } = useInfiniteLoading(executiveSaidList, filterParams)

/**
 * @description: 滚动到界面底部回调方法
 * @param {*} intersectionObserverList
 * @return {*}
 */
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !infiniteLoading.value && !noMore.value && userStore.isVip) {
    onLoadMore()
  }
}

const keywordRef = ref<InputRef>()
const showKeyWordInput = ref(false)
/** 关键字输入框icon点击事件 */
function handlerSearchIconBtnClick() {
  showKeyWordInput.value = true
  nextTick(() => keywordRef.value?.focus())
}
/** 关键字变动 */
const handlerKeyWordChange = debounce(refresh, 300)
/** 关键字失焦处理 */
function handlerKeyWordBlur() {
  console.log('arguments: ', arguments)
  if (isEmpty(filterParams.value.keyword)) {
    showKeyWordInput.value = false
  }
}
</script>

<style lang="less" scoped>
.listContent {
  .titleContent {
    margin-bottom: 4px;
    .companyName {
      margin-bottom: 0px;
    }
  }

  .commentsMask {
    background-image: url('@/assets/images/executiveDetail-mask.jpg');
    height: 486px;
    background-size: contain;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mainContent {
    .content {
      margin-bottom: 4px;
      white-space: pre-wrap;
    }
  }
}
</style>
