<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 15:30:26
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 16:28:45
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/detail/index.vue
 * @Description: 
-->
<template>
  <div class="executiveCommentsDetail">
    <div class="mb16px relative flex items-center justify-between">
      <a @click="goBack" class="flexCenter"> <iconfontIcon icon="icon-chevron-left" /> 返回</a>

      <a-button :loading="followLoading || cancelFollowLoading" @click="!isRelation ? handleFollow() : handleCancelFollow()">
        {{ !isRelation ? '关注' : '取消关注' }}
      </a-button>
    </div>

    <a-row :gutter="16">
      <a-col :span="16">
        <Interpret :isRelation="isRelation" class="mb-16px" />
        <Comments />
      </a-col>
      <a-col :span="8">
        <JobsInfo class="mb-16px" />
        <SameCompanyExecutives />
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useKeepAliveCache } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import JobsInfo from './components/jobsInfo.vue'
import SameCompanyExecutives from './components/sameCompanyExecutives.vue'
import Comments from './components/comments.vue'
import useRequest from '@/hooks/useRequest'
import { executiveIsRelation, executiveRelationAdd, executiveRelationRemove, indexSaveRecentBrowsing } from '@/api/api'
import { message } from 'ant-design-vue'
import Interpret from './components/interpret.vue'

const keepAliveCache = useKeepAliveCache()
const route = useRoute()
const router = useRouter()

const { executiveId, executiveName } = route.query

// 返回事件
async function goBack() {
  router.back()
  keepAliveCache.delCachedView(route) // 消除当前路由的缓存
}

// 保存浏览记录
const { dataList: saveRes } = useRequest(indexSaveRecentBrowsing, { executiveId, executiveName })
// 关注状态
const { dataList: isRelation, getData: getRelationStatus } = useRequest(executiveIsRelation, { executiveId, executiveName })
// 添加关注
const { loading: followLoading, getData: followExecutive } = useRequest(
  executiveRelationAdd,
  { executiveId, executiveName },
  { immediateReqData: false }
)
// 取消关注
const { loading: cancelFollowLoading, getData: cancelFollowExecutive } = useRequest(
  executiveRelationRemove,
  { executiveIds: [executiveId] },
  { immediateReqData: false }
)

function handleFollow() {
  followExecutive().then(() => {
    message.success('操作成功')
    getRelationStatus()
  })
}

function handleCancelFollow() {
  cancelFollowExecutive().then(() => {
    message.success('操作成功')
    getRelationStatus()
  })
}
</script>

<style lang="less" scoped></style>
