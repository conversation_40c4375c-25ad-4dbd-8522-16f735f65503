<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 11:23:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 17:18:40
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/list/index.vue
 * @Description: 
-->
<template>
  <div class="executiveCommentsOverview relative flex">
    <a-collapse :activeKey="activeFilterClassify" ghost accordion class="navBox sticky top-80px pr-16px w100px min-w-100px">
      <a-collapse-panel v-for="item in executiveCommentsFilterClassifyData" :key="item.value" :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <router-link :to="item.to" :class="[item.value === activeFilterClassify ? 'active' : '']">
              {{ item.label }}
            </router-link>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>

    <div class="flex-1">
      <a-row :gutter="[16, 16]">
        <a-col span="16">
          <Comments :type="activeFilterClassify" />
        </a-col>
        <a-col span="8">
          <RightSidebar :type="activeFilterClassify" />
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'executiveComments-index'
})
</script>

<script setup lang="ts">
import { computed, defineComponent } from 'vue'
import { useUserStore } from '@/store'
import { useRoute } from 'vue-router'
import RightSidebar from './components/RightSidebar.vue'
import Comments from './components/comments.vue'

// const props = withDefaults(
//   defineProps<{
//     type?: string // 1行业概况，2全部企业，3关注企业
//     industryId?: string // type1时传，行业id
//     industryName?: string // type1时传，行业名称
//   }>(),
//   { type: '3' }
// )
const route = useRoute()
const activeFilterClassify = computed(() => executiveCommentsFilterClassifyData.find(item => item.to.includes(route.path))?.value || '')
const executiveCommentsFilterClassifyData = [
  { label: '关注高管', value: '3', to: '/executiveComments/overview/follow' },
  { label: 'AI解读', value: 'AI解读', to: '/executiveComments/interpret' },
  { label: '更多高管', value: '2', to: '/executiveComments/overview/more' }
]
</script>

<style lang="less" scoped>
.navBox {
  height: fit-content;
  max-height: calc(100vh - 40px);

  ::v-deep .ant-collapse-content-box {
    padding: 0;
    padding-block: 0 !important;
  }
  ::v-deep .ant-collapse-header {
    padding: 0;
    padding-inline-start: 0 !important;
  }

  .navItem {
    text-align: center;
    margin-bottom: 4px;
    .navTitle {
      a {
        color: #000;
        cursor: pointer;
        line-height: 40px;
        border-radius: 6px;
        font-weight: 500;
        display: block;
        &:hover {
          color: #fff;
          background-color: #6553ee;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }

      .active {
        color: #fff;
        background-color: #6553ee;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
      }
    }
  }
}
</style>
