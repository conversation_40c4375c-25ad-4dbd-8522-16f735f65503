<template>
  <activeExecutives v-if="activeFilterClassify === '1' || activeFilterClassify === '2'" class="mb-16px" :type="activeFilterClassify" />

  <div class="sticky t-80px">
    <executivesEnterprises v-if="activeFilterClassify === '2'" class="mb-16px" :type="activeFilterClassify" />
    <followExecutives v-else-if="activeFilterClassify === '3'" class="mb-16px" />
    <companyWrapper />
  </div>
</template>

<script setup lang="ts">
import companyWrapper from '@/components/companyWrapper/index.vue'
import activeExecutives from './activeExecutives.vue'
import executivesEnterprises from './executivesEnterprises.vue'
import followExecutives from './followExecutives.vue'
import { toRef } from 'vue'
const props = defineProps<{ type: string }>()

const activeFilterClassify = toRef(props, 'type')
</script>

<style scoped></style>
