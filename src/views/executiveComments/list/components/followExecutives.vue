<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 11:27:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 17:23:57
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/list/components/followExecutives.vue
 * @Description: 活跃高管
-->
<template>
  <a-card :bordered="false" class="activity" title="关注高管">
    <template #extra>
      <router-link to="/followOverview/executives">关注列表</router-link>
    </template>
    <a-spin :spinning="loading">
      <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
        <template #renderEmpty>
          <empty empty-text="没有关注的高管" />
        </template>
        <a-table size="small" :dataSource="dataList" :columns="columns" :loading="loading" :pagination="false">
          <template #bodyCell="{ text, column, record }: { text: string, column: TableColumnProps, record: executiveRelationListResType }">
            <template v-if="column.dataIndex === 'executiveName'">
              <div class="executiveBox flex align-center">
                <div class="w-100px h-44px lh-44px ellipsis">
                  <router-link
                    class="color-#000000E0"
                    :to="{
                      name: 'executiveComments-detail',
                      path: '/executiveComments/detail',
                      query: { executiveId: record.executiveId, executiveName: record.executiveName }
                    }"
                    >{{ text }}</router-link
                  >
                </div>
                <div class="flex-1 flex flex-direction-column justify-center fs-14px ellipsis">
                  <p class="ellipsis" :title="record.companyExecutivePostList?.[0].post">
                    {{ record.companyExecutivePostList?.[0].post }}
                  </p>
                  <div class="flex align-center color-#7F7F7F">
                    <template v-if="record.companyExecutivePostList && record.companyExecutivePostList.length > 1">
                      <!-- 有多个公司的才有hover弹窗 -->
                      <a-popover :getPopupContainer="getPopupContainer">
                        <template #content>
                          <a-space direction="vertical">
                            <div v-for="(item, index) in drop(record.companyExecutivePostList)" :key="item.companyUniId">
                              {{ index + 1 }}. {{ item.companyName }}
                            </div>
                          </a-space>
                        </template>
                        <div class="inline-flex overflow-hidden align-center color-#7F7F7F">
                          <router-link
                            class="color-#7F7F7F ellipsis"
                            :to="{
                              path: '/companyInfo/index',
                              name: 'companyInfo-index',
                              query: {
                                companyId: record.companyExecutivePostList?.[0].companyUniId,
                                companyName: record.companyExecutivePostList?.[0].companyName
                              }
                            }"
                          >
                            {{ record.companyExecutivePostList?.[0].companyName }}
                          </router-link>
                          <span class="hoverPrimaryColor flex-1"> （{{ record.companyExecutivePostList.length - 1 }}） </span>
                        </div>
                      </a-popover>
                    </template>
                    <template v-else>
                      <div class="inline-flex overflow-hidden align-center color-#7F7F7F">
                        <router-link
                          class="color-#7F7F7F ellipsis"
                          :to="{
                            path: '/companyInfo/index',
                            name: 'companyInfo-index',
                            query: {
                              companyId: record.companyExecutivePostList?.[0].companyUniId,
                              companyName: record.companyExecutivePostList?.[0].companyName
                            }
                          }"
                        >
                          {{ record.companyExecutivePostList?.[0].companyName }}
                        </router-link>
                      </div>
                    </template>

                    <!-- <router-link
                      class="color-#7F7F7F ellipsis"
                      :to="{
                        path: '/companyInfo/index',
                        name: 'companyInfo-index',
                        query: {
                          companyId: record.companyExecutivePostList?.[0].companyUniId,
                          companyName: record.companyExecutivePostList?.[0].companyName
                        }
                      }"
                    >
                      {{ record.companyExecutivePostList?.[0].companyName }}
                    </router-link>

                    <a-popover :getPopupContainer="getPopupContainer">
                      <template #content>
                        <a-space direction="vertical">
                          <div v-for="(item, index) in drop(record.companyExecutivePostList)" :key="item.companyUniId">
                            {{ index + 1 }}. {{ item.companyName }}
                          </div>
                        </a-space>
                      </template>
                      <span
                        v-if="record.companyExecutivePostList && record.companyExecutivePostList.length > 1"
                        class="hoverPrimaryColor flex-1"
                      >
                        （{{ record.companyExecutivePostList.length - 1 }}）
                      </span>
                    </a-popover> -->
                  </div>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'num'">
              <span :style="{ color: theme.getColorPrimary }">
                {{ text ? text : '-' }}
              </span>
            </template>
          </template>
        </a-table>
      </a-config-provider>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
import { executiveRelationList } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { useThemeStore } from '@/store'
import { TableColumnProps } from 'ant-design-vue'
import { executiveRelationListResType } from '~/types/api/executiveSaid/relationList'
import empty from '@/components/empty/index.vue'
import { drop } from 'lodash-es'

const emits = defineEmits(['interpret'])
const props = withDefaults(
  defineProps<{
    pagination?: boolean
    interpret?: boolean
  }>(),
  {
    pagination: false,
    interpret: false
  }
)

const theme = useThemeStore()
const getPopupContainer = () => document.body

const columns = [
  { title: '高管', dataIndex: 'executiveName', ellipsis: true },
  { title: '近3月言论', dataIndex: 'num', width: 100, align: 'right' }
]

const { dataList, loading, pageParams, getData } = useListLoading(
  executiveRelationList,
  {},
  { pageParams: { hideOnSinglePage: false, onChange: handlerSizeChange } }
)

/**
 * @description: 分页改变
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}
</script>

<style lang="less" scoped></style>
