<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-01-15 15:57:07
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 17:19:02
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/interpret/index.vue
 * @Description:
-->
<template>
  <div class="executiveCommentsInterpret relative flex">
    <a-collapse :activeKey="activeFilterClassify" ghost accordion class="navBox sticky top-80px pr-16px w100px min-w-100px">
      <a-collapse-panel v-for="item in executiveCommentsFilterClassifyData" :key="item.value" :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <router-link :to="item.to" :class="[item.value === activeFilterClassify ? 'active' : '']">
              {{ item.label }}
            </router-link>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>

    <a-row :gutter="16">
      <a-col span="16">
        <chat />
      </a-col>
      <a-col span="8">
        <followExecutives @interpret="handleInterpret" />
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  beforeRouteEnter: async (to, from) => {
    if (!(has(to.query, 'executiveId') && has(to.query, 'executiveName'))) {
      try {
        // 判断是否有关注的高管
        let followTotal = 0
        const { result: followRes } = await executiveRelationList({})
        followTotal = followRes.total
        if (followTotal === 0) {
          // 关注数为0，不给进入，提示关注高管
          Modal.info({
            title: '提示',
            content: '请先关注高管',
            icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
            autoFocusButton: 'ok',
            onCancel() {},
            onOk: () => {}
          })
          return false
        }

        // 判断之前有没有历史对话记录
        const { result: lastInterpretRes } = await executeSaidExplainLast()
        console.log('result: ', lastInterpretRes)
        return {
          ...to,
          query: {
            executiveId: lastInterpretRes.executiveId,
            executiveName: lastInterpretRes.executiveName
          }
        }
      } catch (error) {
        console.log(error)
        return false
      }
    }
  }
})
</script>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import followExecutives from './components/followExecutives.vue'
import chat from './components/chat/index.vue'
import { computed, defineComponent, h } from 'vue'
import { executiveRelationList, executeSaidExplainLast } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { Modal } from 'ant-design-vue'
import { has } from 'lodash-es'

const route = useRoute()
const router = useRouter()

function handleInterpret(item: { executiveId: string; executiveName: string }) {
  router.replace({ path: route.path, query: { ...item } })
}

const activeFilterClassify = computed(() => executiveCommentsFilterClassifyData.find(item => item.to.includes(route.path))?.value || '')
const executiveCommentsFilterClassifyData = [
  { label: '关注高管', value: '3', to: '/executiveComments//overview/follow' },
  { label: 'AI解读', value: 'AI解读', to: '/executiveComments/interpret' },
  { label: '更多高管', value: '2', to: '/executiveComments//overview/more' }
]
</script>

<style lang="less" scoped>
.executiveCommentsInterpret {
  .navBox {
    height: fit-content;
    max-height: calc(100vh - 40px);

    ::v-deep .ant-collapse-content-box {
      padding: 0;
      padding-block: 0 !important;
    }
    ::v-deep .ant-collapse-header {
      padding: 0;
      padding-inline-start: 0 !important;
    }

    .navItem {
      text-align: center;
      margin-bottom: 4px;
      .navTitle {
        a {
          color: #000;
          cursor: pointer;
          line-height: 40px;
          border-radius: 6px;
          font-weight: 500;
          display: block;
          &:hover {
            color: #fff;
            background-color: #6553ee;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
          }
        }

        .active {
          color: #fff;
          background-color: #6553ee;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }
    }
  }
}
</style>
