<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-01-15 16:57:41
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-17 17:47:49
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/interpret/components/chat/components/sender.vue
 * @Description: 
-->
<template>
  <div class="sender">
    <a-textarea
      placeholder="请输入您的问题"
      :autoSize="{ minRows: 1, maxRows: 6 }"
      class="senderTextarea"
      :value="props.problem"
      @change="handleTextAreaChange"
      @keydown="handlePushKeyword($event)"
    ></a-textarea>
    <div class="flex-center-end h100%">
      <div
        :class="[
          'ml-16px w28px h28px  br-50px flex-center-center',
          sendLoading || props.problem ? 'bg-primaryColor cursor-pointer' : 'bg-#d0cfd9 cursor-no-drop'
        ]"
      >
        <iconfontIcon v-if="sendLoading" icon="icon-zhongzhi" class="fs-16px! color-red" @click="stopMsg" />
        <iconfontIcon v-else-if="props.problem" icon="icon-arrow-up" class="fs-20px! color-#fff" @click="sendMsg" />
        <iconfontIcon v-else icon="icon-arrow-up" class="fs-20px! color-#fff" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="sender">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { ChangeEvent } from 'ant-design-vue/es/_util/EventInterface'
import { isEmpty } from 'lodash-es'

const emits = defineEmits(['update:problem', 'sendMsg', 'stopMsg'])
const props = defineProps<{ problem: string; sendLoading: boolean }>()

function stopMsg() {
  emits('stopMsg')
}
function sendMsg() {
  emits('sendMsg')
}

function handleTextAreaChange(e: ChangeEvent) {
  emits('update:problem', e.target.value)
}

function handlePushKeyword(event: KeyboardEvent) {
  // 阻止输入内容为空的时候，回车会换行的情况
  if (isEmpty(props.problem) && event.key === 'Enter') {
    event.preventDefault()
    return
  }

  // 检查是否按下了Enter键
  if (event.key === 'Enter') {
    // 如果Shift键也被按下，则允许换行
    if (event.shiftKey) {
      return
    }
    // 否则，阻止默认行为（换行），并调用sendMessage函数
    event.preventDefault()
    sendMsg()
  }
}
</script>

<style lang="less" scoped>
.sender {
  @apply flex-inherit-end relative  p12px bg-#fff border-solid border-1px border-c-#e8eaf2 br-20px;
  width: 100%;
  box-shadow: 0 5px 5px -10px rgba(54, 54, 73, 0.04), 0 2px 5px 0 rgba(51, 51, 71, 0.08), 0 0 1px 0 rgba(44, 44, 54, 0.02);
  transition: border 0.4s ease;

  &:hover {
    border: 1px solid var(--g-primary-hover-color);
  }

  .senderTextarea {
    @apply flex-1 border-0 mb-4px fs-16px;
    width: 100%;
    background: transparent !important;
    box-shadow: none !important;
    transition: none !important;
    padding: 0 8px 0 0;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      transition: 0.25s;
    }
    &::-webkit-scrollbar-thumb {
      background: transparent;
      border-radius: 3px;
    }
    &:focus {
      border: 0;
    }
    &:hover {
      border: 0;
      &::-webkit-scrollbar-thumb {
        background: rgb(224, 226, 235) !important;
      }
    }

    // @keyframes fade-in {
    //   0% {
    //     opacity: 0;
    //     transform: translateY(20%);
    //   }

    //   to {
    //     opacity: 1;
    //     transform: translateY(0);
    //   }
    // }
  }
}
</style>
