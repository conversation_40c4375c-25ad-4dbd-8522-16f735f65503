<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-01-15 16:57:35
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 16:57:15
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/interpret/components/chat/components/msgItem.vue
 * @Description: 
-->
<template>
  <div class="w100%">
    <div :class="['msgItem', msgDirection === 'left' ? 'leftMsgItem' : 'rightMsgItem']">
      <div class="containerWrap containerMarkdown">
        <div v-html="contentText"></div>
        <div v-if="loading && contentText.length === 0" class="loading-dots min-h-48px">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>

    <div v-if="isStop !== 0" :class="['color-#c8cad9 fs-14px ml6px mt13px', msgDirection === 'left' ? 'text-left' : 'text-right']">
      <template v-if="isStop === 1"> 这条消息已停止 </template>
      <template v-else-if="isStop === 2"> 系统繁忙 </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import hljs from 'highlight.js'
import 'highlight.js/styles/atom-one-dark.min.css'
import MarkdownIt from 'markdown-it'
import { isEmpty } from 'lodash-es'

interface Props {
  /** 消息方向，可选值为 'left' 或 'right' */
  msgDirection?: 'left' | 'right'
  /** 消息文本内容 */
  msgText: string
  /** 是否处于加载状态 */
  loading?: boolean
  /**
   * 消息停止状态
   * @default 0
   * @value 0 默认状态
   * @value 1 已停止
   * @value 2 异常状态
   */
  isStop?: 0 | 1 | 2
}

const props = withDefaults(defineProps<Props>(), {
  msgDirection: 'right',
  loading: false,
  isStop: 0
})

const markdown = MarkdownIt({
  breaks: true,
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str: string, lang: string): string {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return (
          '<pre class="hljs br-8px p8px"><code>' + hljs.highlight(str, { language: lang, ignoreIllegals: true }).value + '</code></pre>'
        )
      } catch (__) {}
    }
    return '<pre class="hljs br-8px p8px"><code>' + markdown.utils.escapeHtml(str) + '</code></pre>'
  }
})

const contentText = computed(() => {
  // return props.msgText // .replace(/\n/g, '<br/>')
  if (isEmpty(props.msgText)) {
    return []
  }

  // console.log('this.msgText:', this.msgText)
  let value = props.msgText.replace(/\\n/g, '  \n')
  let htmlString = ''
  if (value.split('```').length % 2) {
    let mdText = value
    if (mdText[mdText.length - 1] != '\n') {
      mdText += '\n'
    }
    htmlString = markdown.render(mdText)
  } else {
    htmlString = markdown.render(value)
  }
  // 解决小程序表格边框型失效问题
  htmlString = htmlString.replace(/<table/g, `<table class="table"`)
  htmlString = htmlString.replace(/<tr/g, `<tr class="tr"`)
  htmlString = htmlString.replace(/<th>/g, `<th class="th">`)
  htmlString = htmlString.replace(/<td/g, `<td class="td"`)
  htmlString = htmlString.replace(/<hr>|<hr\/>|<hr \/>/g, `<hr class="hr">`)
  htmlString = htmlString.replace(/<hr>|<hr\/>|<hr \/>/g, `<hr class="hr">`)

  // 去除多余的空格，优化文本样式
  // htmlString = htmlParser(htmlString)
  // htmlString = this.filterWrap(htmlString)

  // return isUndefined(htmlString) ? [] : htmlString
  return htmlString
})
</script>

<style lang="less">
.msgItem {
  @apply lh-1.5em  flex;
  width: 100%;

  .loading-dots {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 4px;

    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: currentColor;
      opacity: 0.6;
      animation: dotFade 1s infinite;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  @keyframes dotFade {
    0%,
    100% {
      opacity: 0.6;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.containerWrap {
}

.leftMsgItem {
  justify-content: flex-start;
  .containerWrap {
    text-align: left;
    background-color: #f2f2f2;
    color: #303030;
  }
}
.rightMsgItem {
  justify-content: flex-end;
  .containerWrap {
    text-align: left;
    background-color: var(--g-primary-color);
    color: #fff;
  }
}

.containerMarkdown {
  @apply px-8px b-12px overflow-auto lh-2em inline-block br-12px fs-16px;
  box-shadow: 0 12px 14px rgba(54, 54, 73, 0.04);

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit;
  }
  h1,
  h2,
  h3 {
    margin: 8px 0;
  }
  h4,
  h5,
  h6 {
    margin: 8px 0;
  }
  .h1,
  h1 {
    font-size: 36px;
  }
  .h2,
  h2 {
    font-size: 30px;
  }
  .h3,
  h3 {
    font-size: 24px;
  }
  .h4,
  h4 {
    font-size: 18px;
  }
  .h5,
  h5 {
    font-size: 14px;
  }
  .h6,
  h6 {
    font-size: 12px;
  }
  a {
    background-color: transparent;
    color: #2196f3;
    text-decoration: none;
  }
  hr,
  ::v-deep .hr {
    margin: 8px 0;
    border: 0;
    border-top: 1px solid #e5e5e5;
  }
  img {
    max-width: 35%;
  }
  p {
    margin: 8px 0;
  }
  em {
    font-style: italic;
    font-weight: inherit;
  }
  ol,
  ul {
    margin: 8px 0;
    padding-left: 2em;
    list-style: revert;
  }
  ol ol,
  ol ul,
  ul ol,
  ul ul {
    margin-bottom: 0;
  }
  ol ol,
  ul ol {
    list-style-type: lower;
  }
  ol ol ol,
  ul ul ol {
    list-style-type: lower-alpha;
  }
  dl {
    margin: 8px 0;
  }
  dt {
    font-weight: 600;
  }
  dt,
  dd {
    line-height: 1.4;
  }
  .task-list-item {
    list-style-type: none;
  }
  .task-list-item input {
    margin: 0 0.2em 0.25em -1.6em;
    vertical-align: middle;
  }
  pre {
    position: relative;
    z-index: 11;
  }
  code,
  kbd,
  pre,
  samp {
    font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
  }
  code:not(.hljs) {
    padding: 2px 4px;
    font-size: 90%;
    // color: #c7254e;
    // background-color: #ffe7ee;
    border-radius: 4px;
  }
  code:empty {
    display: none;
  }
  pre code.hljs {
    color: var(--vg__text-1);
    border-radius: 16px;
    background: var(--vg__bg-1);
    font-size: 12px;
  }
  .markdown-wrap {
    font-size: 12px;
    margin-bottom: 10px;
  }
  pre.code-block-wrapper {
    background: #2b2b2b;
    color: #f8f8f2;
    border-radius: 4px;
    overflow-x: auto;
    padding: 1em;
    position: relative;
  }
  pre.code-block-wrapper code {
    padding: auto;
    font-size: inherit;
    color: inherit;
    background-color: inherit;
    border-radius: 0;
  }
  .code-block-header__copy {
    font-size: 16px;
    margin-left: 5px;
  }
  abbr[data-original-title],
  abbr[title] {
    cursor: help;
    border-bottom: 1px dotted #777;
  }
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #e5e5e5;
  }
  blockquote ol:last-child,
  blockquote p:last-child,
  blockquote ul:last-child {
    margin-bottom: 0;
  }
  blockquote .small,
  blockquote footer,
  blockquote small {
    display: block;
    font-size: 80%;
    line-height: 1.42857143;
    color: #777;
  }
  blockquote .small:before,
  blockquote footer:before,
  blockquote small:before {
    content: '\2014 \00A0';
  }
  .blockquote-reverse,
  blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    text-align: right;
    border-right: 5px solid #eee;
    border-left: 0;
  }
  .blockquote-reverse .small:before,
  .blockquote-reverse footer:before,
  .blockquote-reverse small:before,
  blockquote.pull-right .small:before,
  blockquote.pull-right footer:before,
  blockquote.pull-right small:before {
    content: '';
  }
  .blockquote-reverse .small:after,
  .blockquote-reverse footer:after,
  .blockquote-reverse small:after,
  blockquote.pull-right .small:after,
  blockquote.pull-right footer:after,
  blockquote.pull-right small:after {
    content: '\00A0 \2014';
  }
  .footnotes {
    -moz-column-count: 2;
    -webkit-column-count: 2;
    column-count: 2;
  }
  .footnotes-list {
    padding-left: 2em;
  }
  table,
  ::v-deep .table {
    border-spacing: 0;
    border-collapse: collapse;
    width: 100%;
    max-width: 65em;
    overflow: auto;
    margin-top: 0;
    margin-bottom: 16px;
  }
  table tr,
  ::v-deep .table .tr {
    border-top: 1px solid #e5e5e5;
  }
  table th,
  table td,
  ::v-deep .table .th,
  ::v-deep .table .td {
    padding: 6px 13px;
    border: 1px solid #e5e5e5;
  }
  table th,
  ::v-deep .table .th {
    font-weight: 600;
    background-color: #eee;
  }
  .hljs[class*='language-']:before {
    position: absolute;
    z-index: 3;
    top: 0.8em;
    right: 1em;
    font-size: 0.8em;
    color: #999;
  }
  .hljs[class~='language-js']:before {
    content: 'js';
  }
  .hljs[class~='language-ts']:before {
    content: 'ts';
  }
  .hljs[class~='language-html']:before {
    content: 'html';
  }
  .hljs[class~='language-md']:before {
    content: 'md';
  }
  .hljs[class~='language-vue']:before {
    content: 'vue';
  }
  .hljs[class~='language-css']:before {
    content: 'css';
  }
  .hljs[class~='language-sass']:before {
    content: 'sass';
  }
  .hljs[class~='language-scss']:before {
    content: 'scss';
  }
  .hljs[class~='language-less']:before {
    content: 'less';
  }
  .hljs[class~='language-stylus']:before {
    content: 'stylus';
  }
  .hljs[class~='language-go']:before {
    content: 'go';
  }
  .hljs[class~='language-java']:before {
    content: 'java';
  }
  .hljs[class~='language-c']:before {
    content: 'c';
  }
  .hljs[class~='language-sh']:before {
    content: 'sh';
  }
  .hljs[class~='language-yaml']:before {
    content: 'yaml';
  }
  .hljs[class~='language-py']:before {
    content: 'py';
  }
  .hljs[class~='language-docker']:before {
    content: 'docker';
  }
  .hljs[class~='language-dockerfile']:before {
    content: 'dockerfile';
  }
  .hljs[class~='language-makefile']:before {
    content: 'makefile';
  }
  .hljs[class~='language-javascript']:before {
    content: 'js';
  }
  .hljs[class~='language-typescript']:before {
    content: 'ts';
  }
  .hljs[class~='language-markup']:before {
    content: 'html';
  }
  .hljs[class~='language-markdown']:before {
    content: 'md';
  }
  .hljs[class~='language-json']:before {
    content: 'json';
  }
  .hljs[class~='language-ruby']:before {
    content: 'rb';
  }
  .hljs[class~='language-python']:before {
    content: 'py';
  }
  .hljs[class~='language-bash']:before {
    content: 'sh';
  }
  .hljs[class~='language-php']:before {
    content: 'php';
  }
}
</style>
