<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-01-15 16:57:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 16:55:38
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/interpret/components/chat/index.vue
 * @Description: 
-->
<template>
  <a-card :bodyStyle="cardBodyStyle">
    <div class="headerBox">
      <a-space align="center">
        <p class="fs-18px primaryColor mr-16px fw-500">AI解读</p>
        <router-link
          class="fs-16px color-#000000E0"
          :to="{
            path: '/executiveComments/detail',
            name: 'executiveComments-detail',
            query: { executiveId: executiveId, executiveName: executiveName }
          }"
          >{{ executiveName }}</router-link
        >
        <router-link
          v-if="executiveDetail?.length !== 0"
          :to="{
            path: '/companyInfo/index',
            name: 'companyInfo-index',
            query: {
              companyId: executiveDetail?.[0].companyUniId,
              companyName: executiveDetail?.[0].companyName
            }
          }"
          class="color-#7F7F7F fs-14px"
        >
          {{ executiveDetail?.[0].companyName }}
        </router-link>
      </a-space>
    </div>

    <!-- 消息列表 -->
    <div ref="chatMsgBoxRef" class="chatMsgBox br-12px" @scroll="handleScroll">
      <div v-if="!noMore" class="loadMore mb-8px" v-intersection-observer="handlerIntersectionObserver">
        <div class="endText">
          <p><a-spin tip="加载中..."></a-spin></p>
        </div>
      </div>

      <div v-for="item in chatList" :key="item.id" :id="`msgItem${item.id}`" class="msgItem">
        <MsgItem :msgDirection="item.contentType === 1 ? 'right' : 'left'" :msgText="item.content" :loading="true" :isStop="item.isStop" />
      </div>

      <!-- 没有数据，展示欢迎语 -->
      <div v-if="chatList.length === 0" class="pt-100px">
        <div class="text-center mb32px">
          <h3 class="fs-24px fw-500 color-#8473ff mt-32px mb-16px">商瞳高管AI解读</h3>
          <p class="fs-16px">通过高管言论掌握高管的关注点与偏好、洞察企业动态</p>
        </div>

        <div v-for="(item, index) in promptData" :key="index" class="mb-24px text-center">
          <span class="promptsItem" @click="handlePromptClick(item)"> {{ item }} </span>
        </div>
      </div>
    </div>

    <!-- 提示词 -->
    <div v-if="chatList.length !== 0" class="mb16px">
      <a-space wrap>
        <span class="promptsItem" v-for="(item, index) in promptData" :key="index" @click="handlePromptClick(item)">
          {{ item }}
        </span>
      </a-space>
    </div>

    <!-- 发送框 -->
    <Sender :send-loading="sendLoading" v-model:problem="formData.problem" @send-msg="handleSenderSendMsg" @stop-msg="handleStopMag" />
    <p class="mt-16px text-center fs-10px color-#8f91a8">
      服务生成的所有内容均由人工智能模型生成，其生成内容的准确性和完整性无法保证，不代表我们的态度或观点
    </p>
  </a-card>
</template>

<script setup lang="ts" name="chat">
import Sender from './components/sender.vue'
import MsgItem from './components/msgItem.vue'
import { computed, nextTick, onUnmounted, onMounted, ref, StyleValue, onDeactivated, onBeforeMount, onBeforeUnmount } from 'vue'
import {
  executeSaidExplainChatClose,
  executeSaidExplainChatStream,
  executeSaidExplainCheckLimit,
  executeSaidExplainHistory,
  executeSaidExplainTips,
  executiveSaidExecutiveDetail
} from '@/api/api'
import { isElement, isEmpty, isNull, isUndefined } from 'lodash-es'
import { randomUUID } from '@/utils/util'
import { vIntersectionObserver } from '@vueuse/components'
import useRequest from '@/hooks/useRequest'
import { useRoute } from 'vue-router'

const route = useRoute()

const { executiveId, executiveName, problem } = route.query

// 卡片高度
const cardBodyStyle = computed<StyleValue>(() => {
  // 获取屏幕高度
  const screenHeight = document.documentElement.clientHeight
  return {
    display: 'flex',
    flexDirection: 'column',
    height: `${screenHeight - 64 - 32 - 2}px` // 64s是顶部导航栏的高度，32是内边距，2是card的外边框
  }
})

const { dataList: executiveDetail } = useRequest(executiveSaidExecutiveDetail, { executiveId: executiveId, executiveName: executiveName })

// 获取提示词
const { dataList: promptData } = useRequest(executeSaidExplainTips, { executiveId: executiveId, executiveName: executiveName })

// 获取历史记录
const lastFetchId = ref(0)
const loadingMore = ref(false)
const noMore = computed(() => !loadingMore.value && pagination.value.current >= pagination.value.maxPages)
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  maxPages: 0 // 最大页
})
async function getHistoryData() {
  try {
    loadingMore.value = true
    lastFetchId.value += 1
    const fetchId = lastFetchId.value

    // 临时记录id，用于滚回当前位置
    const tempId = chatList.value.length === 0 ? '' : chatList.value[0].id
    //
    const { result } = await executeSaidExplainHistory({
      executiveId: executiveId as string,
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize
    })
    if (fetchId !== lastFetchId.value) {
      return
    }
    const { records, total, pages } = result
    pagination.value.total = total
    pagination.value.maxPages = pages
    records.forEach(element =>
      chatList.value.unshift({
        id: element.id,
        loading: false,
        isStop: element.isStop,
        contentType: element.contentType,
        content: element.content
      })
    )
    nextTick(() => {
      loadingMore.value = false
      if (tempId !== '') {
        const tempDom = document.querySelector(`#msgItem${tempId}`)
        if (isNull(tempDom)) {
          return
        }
        const rect = tempDom.getBoundingClientRect()
        const parentRect = tempDom.parentElement!.getBoundingClientRect()
        const top = rect.top - parentRect.top
        chatMsgBoxScrollToPosition(top)
      }
    })
  } catch (error) {
    loadingMore.value = false
    console.error(error)
  }
}

// 滚动到界面底部回调方法
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !loadingMore.value && !noMore.value) {
    pagination.value.current += 1
    getHistoryData()
  }
}

interface chatItemType {
  id: string
  loading: boolean // 是否加载中
  isStop: 0 | 1 | 2 // 0: 默认，1：停止，2：异常
  contentType: 1 | 2 // 1: 问题，2：回答
  content: string
}
const sendLoading = ref(false)
const chatList = ref<chatItemType[]>([])
const formData = ref<{ problem: string }>({
  problem: ''
})

// 发送消息
async function handleSendMag() {
  // 监听流式数据
  const onMessage = async (message: ReadableStream) => {
    const reader = message.getReader()
    const decoder = new TextDecoder('utf-8')

    while (true) {
      const { done, value } = await reader.read()
      if (done || !sendLoading.value) break
      const chunk = decoder.decode(value, { stream: true })

      let matches = [...chunk.matchAll(/"(.*?)"/g)].map(match => match[1]).filter(item => item && !item.includes('[DONE]'))
      let tempText = matches.join('')
      if (tempText.includes('SYS_ERROR')) {
        sendLoading.value = false
        chatList.value[chatList.value.length - 1].loading = false
        chatList.value[chatList.value.length - 1].content += '请求过快，稍后重试'
      } else {
        chatList.value[chatList.value.length - 1].content += tempText
      }

      if (chunk.includes('[DONE]')) {
        sendLoading.value = false
        chatList.value[chatList.value.length - 1].loading = false
      }
      // // 判断是否在底部，滚动到底部的时候，页面跟随打字机滚动。页面不是底部的时候，不跟随打字机滚动
      if (isScrollBottom) {
        chatMsgBoxScrollToPosition()
      }
    }
  }

  try {
    if (isUndefined(formData.value.problem)) {
      return
    }
    sendLoading.value = true
    // 判断是否超出次数
    const limitRes = await executeSaidExplainCheckLimit()

    // 发送消息
    addOnceMsg({ content: formData.value.problem, contentType: 1, loading: false, isStop: 0, id: randomUUID() })
    const params = {
      problem: formData.value.problem,
      executiveId: executiveId as string,
      executiveName: executiveName as string
    }
    formData.value.problem = ''
    addOnceMsg({ content: '', contentType: 2, loading: true, isStop: 0, id: randomUUID() })
    const res = await executeSaidExplainChatStream(params)
    onMessage(res)
  } catch (error) {
    sendLoading.value = false
    if (error.code !== 'MEMBER_REQUEST_LIMIT') {
      chatList.value[chatList.value.length - 1].isStop = 1
    }
    chatList.value[chatList.value.length - 1].loading = false
    // chatList.value[chatList.value.length - 1].content += '系统繁忙'
    console.error(222, error)
  }
}

// 停止回答
async function handleStopMag() {
  try {
    await executeSaidExplainChatClose({ executiveId: executiveId as string })
    sendLoading.value = false
    chatList.value[chatList.value.length - 1].loading = false
    chatList.value[chatList.value.length - 1].isStop = 1
    chatMsgBoxScrollToPosition()
  } catch (error) {
    console.error(error)
  }
}

function handleSenderSendMsg() {
  if (sendLoading.value) {
    return
  }
  handleSendMag()
}

// 触发提示词
async function handlePromptClick(promptItem: string) {
  if (sendLoading.value) {
    return
  }
  formData.value.problem = promptItem
  handleSendMag()
}

// 添加单条消息，从底部添加消息
function addOnceMsg(msgItem: chatItemType) {
  chatList.value.push(msgItem)
  chatMsgBoxScrollToPosition()
}

/**
 * 处理消息框滚动相关
 */
const chatMsgBoxRef = ref<HTMLDivElement>()
let isScrollBottom = true
function handleScroll(e: Event) {
  // 确保 event.target 是一个 HTMLElement
  const target = e.target as HTMLElement
  if (target instanceof HTMLElement) {
    // 使用可选链操作符以防止可能的 null 或 undefined 错误
    const scrollDifference = Math.abs(target.scrollHeight - target.clientHeight - (target.scrollTop ?? 0))
    // 判断是否滚动到底部
    isScrollBottom = scrollDifference < 5

    // // 判断是否滚动到顶部
    // if (target.scrollTop <= 0) {
    //   // 滚动到顶部时，重新获取历史记录
    //   console.log('滚动到顶部时，重新获取历史记录')
    //   getMoreHistory()
    // }
  }
}

function chatMsgBoxScrollToPosition(position: number | 'bottom' = 'bottom') {
  nextTick(() => {
    if (!isElement(chatMsgBoxRef.value)) {
      return
    }
    if (position === 'bottom') {
      chatMsgBoxRef.value!.scrollTop = chatMsgBoxRef.value!.scrollHeight
    } else {
      chatMsgBoxRef.value!.scrollTop = position
    }
  })
}

onMounted(async () => {
  executeSaidExplainChatClose({ executiveId: executiveId as string })
  await getHistoryData()
  if (!isEmpty(problem)) {
    formData.value.problem = problem as string
    handleSendMag()
  }

  chatMsgBoxScrollToPosition()
})

onBeforeUnmount(() => {
  console.log('onBeforeMount')
  if (sendLoading.value) {
    handleStopMag()
  }
})
// onDeactivated(() => {
//   console.log('onDeactivated')
// if (sendLoading.value) {
//   handleStopMag()
// }
// })
</script>

<style lang="less" scoped>
.headerBox {
}
.chatMsgBox {
  @apply flex-1 overflow-auto my-16px;

  background: transparent !important;
  box-shadow: none !important;
  transition: none !important;
  // padding: 0 8px 0 0;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    transition: 0.25s;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
  }
  &:focus {
    border: 0;
  }
  &:hover {
    border: 0;
    &::-webkit-scrollbar-thumb {
      background: rgb(224, 226, 235) !important;
    }
  }

  .msgItem {
    + .msgItem {
      @apply mt-16px;
    }
  }
}

.promptsItem {
  @apply transition-all p8px bg-#fff  br-12px fs-16px cursor-pointer;
  border: solid 1px #e8eaf2;
  line-height: 2.5;
  box-shadow: 0 2px 4px -10px rgba(54, 54, 73, 0.04), 0 2px 5px 0 rgba(51, 51, 71, 0.08), 0 0 1px 0 rgba(44, 44, 54, 0.02);
  &:hover {
    color: #6553ee;
    background-color: #eaeafd;
    border-color: #baabff;
  }
  + .promptsItem {
    margin-left: 4px;
  }
}
</style>
