<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 11:27:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-16 11:21:55
 * @FilePath: /corp-elf-web-consumer/src/views/executiveComments/interpret/components/followExecutives.vue
 * @Description: 活跃高管
-->
<template>
  <a-card :bordered="false" class="activity" title="关注高管">
    <template #extra>
      <router-link to="/followOverview/executives">关注列表</router-link>
    </template>
    <a-spin :spinning="loading">
      <a-config-provider :theme="{ components: { Table: { fontSize: '16px', margin: '16px 0 0' } } }">
        <template #renderEmpty>
          <empty empty-text="没有关注的高管" />
        </template>
        <a-table
          size="small"
          :dataSource="dataList"
          :columns="columns"
          :loading="loading"
          :pagination="pageParams"
          :customRow="
            record => {
              return {
                class: 'cursor-pointer',
                onclick: e => {
                  emits('interpret', {
                    executiveId: record.executiveId,
                    executiveName: record.executiveName
                  })
                }
              }
            }
          "
        >
          <template #bodyCell="{ text, column, record }: { text: string, column: TableColumnProps, record: executiveRelationListResType }">
            <template v-if="column.dataIndex === 'executiveName'">
              <div class="executiveBox flex align-center">
                <div class="w-100px h-44px lh-44px ellipsis">
                  <span class="color-#000000E0">{{ text }}</span>
                </div>
                <div class="flex-1 flex flex-direction-column justify-center fs-14px ellipsis">
                  <p class="ellipsis" :title="record.companyExecutivePostList?.[0].post">
                    {{ record.companyExecutivePostList?.[0].post }}
                  </p>

                  <template v-if="record.companyExecutivePostList && record.companyExecutivePostList.length > 1">
                    <!-- 有多个公司的才有hover弹窗 -->
                    <a-popover :getPopupContainer="getPopupContainer">
                      <template #content>
                        <a-space direction="vertical">
                          <div v-for="(item, index) in drop(record.companyExecutivePostList)" :key="item.companyUniId">
                            {{ index + 1 }}. {{ item.companyName }}
                          </div>
                        </a-space>
                      </template>
                      <div class="inline-flex overflow-hidden align-center color-#7F7F7F">
                        <span class="color-#7F7F7F ellipsis">
                          {{ record.companyExecutivePostList?.[0].companyName }}
                        </span>
                        <span class="hoverPrimaryColor flex-1"> （{{ record.companyExecutivePostList.length - 1 }}） </span>
                      </div>
                    </a-popover>
                  </template>
                  <template v-else>
                    <div class="inline-flex overflow-hidden align-center color-#7F7F7F">
                      <span class="color-#7F7F7F ellipsis">
                        {{ record.companyExecutivePostList?.[0].companyName }}
                      </span>
                    </div>
                  </template>
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'num'">
              <span :style="{ color: theme.getColorPrimary }">
                {{ text ? text : '-' }}
              </span>
            </template>
          </template>
        </a-table>
      </a-config-provider>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
import { executiveRelationList } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { useThemeStore } from '@/store'
import { TableColumnProps } from 'ant-design-vue'
import { executiveRelationListResType } from '~/types/api/executiveSaid/relationList'
import empty from '@/components/empty/index.vue'
import { drop } from 'lodash-es'

const emits = defineEmits(['interpret'])

const theme = useThemeStore()
const getPopupContainer = () => document.body

const columns = [
  { title: '高管', dataIndex: 'executiveName', ellipsis: true },
  { title: '近3月言论', dataIndex: 'num', width: 100, align: 'right' }
]

const { dataList, loading, pageParams } = useListLoading(executiveRelationList, {}, { pageParams: { hideOnSinglePage: false } })
</script>

<style lang="less" scoped></style>
