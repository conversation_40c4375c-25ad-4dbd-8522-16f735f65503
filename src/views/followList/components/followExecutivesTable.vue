<template>
  <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
    <template #renderEmpty>
      <empty empty-text="没有关注的高管" />
    </template>
    <Table
      title="关注列表"
      rowKey="executiveId"
      :dataSource="dataList"
      :columns="tableColumns"
      :loading="loading"
      :pagination="{ ...paginationParams, onChange: handlerCurrentChange, hideOnSinglePage: false }"
      :scroll="{ x: 1000 }"
      @selectedRowsChange="handlerSelectedRowsChange"
    >
      <template #cardTitle>
        <a-space>
          关注列表
          <a-input
            v-model:value="params.keyword"
            ref="keywordRef"
            style="width: 220px"
            class="!fs-14 fw-400 transition-all"
            placeholder="高管姓名、职位"
            size="small"
            allowClear
            @change="handlerExecutiveKeyWordChange"
          >
            <template #prefix>
              <iconfontIcon icon="icon-search" :extra-common-props="{ class: 'fs-18px hoverPrimaryColor  color-#7F7F7F' }" />
            </template>
          </a-input>
        </a-space>
      </template>

      <template #button>
        <a-button
          v-show="checkList.length !== 0"
          class="flex items-center"
          @click="handlerBtnClick('goBack')"
          danger
          :icon="h(DeleteOutlined)"
          >取消关注</a-button
        >
      </template>

      <template #executiveName="rowItem: bodyCell<executiveRelationListResType>">
        <span
          class="hoverPrimaryColor"
          @click="openExecutiveDetail({ executiveId: rowItem.record.executiveId, executiveName: rowItem.record.executiveName })"
        >
          {{ rowItem.text }}
        </span>
      </template>

      <template #company="{ record }: bodyCell<executiveRelationListResType>">
        <div class="h-30px flex items-center">
          <p
            class="hoverPrimaryColor ellipsis"
            @click="
              openCompanyInfo({
                companyId: record.companyExecutivePostList![0].companyUniId,
                entName: record.companyExecutivePostList![0].companyName
              })
            "
          >
            {{ record.companyExecutivePostList![0].companyName }}
          </p>
          <a-popover :getPopupContainer="getPopupContainer">
            <template #content>
              <a-space direction="vertical">
                <div v-for="(item, index) in drop(record.companyExecutivePostList)" :key="item.companyUniId">
                  {{ index + 1 }}. {{ item.companyName }}
                </div>
              </a-space>
            </template>
            <span v-if="record.companyExecutivePostList && record.companyExecutivePostList.length > 1" class="hoverPrimaryColor flex-1">
              （{{ record.companyExecutivePostList.length - 1 }}）
            </span>
          </a-popover>
        </div>
      </template>

      <template #postName="rowItem: bodyCell<executiveRelationListResType>">
        {{ rowItem.record.companyExecutivePostList?.[0].post }}
      </template>

      <template #actions="rowItem: actionsBodyCell<executiveRelationListResType>">
        <moreIcon v-show="rowItem.visibility" :menuList="menuList" @click="menuItem => handlerMoreIconClick(menuItem, rowItem)" />
      </template>
    </Table>
  </a-config-provider>
</template>

<script setup lang="ts">
import { Modal, message, theme } from 'ant-design-vue'
import { h, ref, watch } from 'vue'
import { executiveRelationList, executiveRelationRemove } from '@/api/api'
import Table, { actionsBodyCell, bodyCell } from '@comp/table/index'
import moreIcon, { type MenuItem } from '@comp/actionIcon/more.vue'
import useListLoading from '@/hooks/useListLoading'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { debounce, drop } from 'lodash-es'
import { executiveRelationListResType } from '~/types/api/executiveSaid/relationList'
import { DeleteOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import empty from '@comp/empty/index.vue'

const emits = defineEmits(['refresh'])
// const props = defineProps<{ collectId: string }>()

const router = useRouter()
const { useToken } = theme
const { token: themeToken } = useToken()
// const collectId = ref('-1') // 当前选中你的分组id
const getPopupContainer = () => document.body

const tableColumns = ref([
  { title: '高管', dataIndex: 'executiveName', slotName: 'executiveName', width: 120, ellipsis: true },
  { title: '单位', dataIndex: 'company', slotName: 'company', width: 320, ellipsis: true },
  { title: '职位', slotName: 'postName', ellipsis: true },
  { title: '近3月言论', dataIndex: 'num', width: 120 },
  { title: '', slotName: 'actions', width: 64 }
])

// 客户列表请求参数
const params = ref({ keyword: undefined })
const { loading, pageParams: paginationParams, dataList, getData, refresh } = useListLoading(executiveRelationList, params) // 客户列表请求

// // 监听id变动，刷新数据
// watch(() => props.collectId, refresh)

// 关注高管搜索框输入防抖
const handlerExecutiveKeyWordChange = debounce(refresh, 300)

/**
 * @description: pageSize 改变时触发
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerCurrentChange(pageNo: number, pageSize: number) {
  paginationParams.value.current = pageNo
  paginationParams.value.pageSize = pageSize
  getData()
}

/**
 * @description: 选中相关
 * @return {*}
 */
const checkList = ref<executiveRelationListResType[]>([])
function handlerSelectedRowsChange(_keys: string, rows: executiveRelationListResType[]) {
  checkList.value = rows
}

/**
 * @description: 处理批量操作按钮
 * @param {*} type
 * @return {*}
 */
function handlerBtnClick(type: 'edit' | 'goBack') {
  if (checkList.value.length === 0) {
    message.warning('请选择要操作的数据')
    return
  }
  switch (type) {
    case 'goBack':
      goBackCompany(checkList.value.map(item => item.executiveId))
      break

    default:
      break
  }
}

// 更多按钮
const menuList: MenuItem[] = [{ title: '取消关注', key: 'goBack', style: { color: themeToken.value.colorError } }]
/**
 * @description: 处理更多按钮点击事件
 * @param {*} menuItem
 * @param {*} rowItem
 * @return {*}
 */
function handlerMoreIconClick(menuItem: MenuItem, rowItem: { text: string; record: executiveRelationListResType }) {
  switch (menuItem.key) {
    case 'goBack':
      goBackCompany([rowItem.record.executiveId]) // 取消关注
      break

    default:
      break
  }
}

/**
 * @description: 取消关注
 * @param {*} companyId
 * @return {*}
 */
async function goBackCompany(ids: string[]) {
  console.log('companyId: ', ids)
  Modal.confirm({
    title: '提示',
    content: '是否取消关注该高管?',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    onOk: async () => {
      try {
        loading.value = true
        const { message: msg } = await executiveRelationRemove({ executiveIds: ids })
        message.success(msg)
        await refresh()
        emits('refresh')
        loading.value = false
      } catch (error) {
        loading.value = false
        console.error(error)
      }
    }
  })
}

/**
 * @description: 处理点击企业名称
 * @param {Event} event
 * @param {*} item
 * @return {*}
 */
function openCompanyInfo(item: { companyId?: string; cid?: string; entName: string }) {
  const id = item.companyId || item.cid

  console.log('props.companyInfo: ', item)
  // if (item.companyClass === 'A') {
  //   message.warning('系统暂不提供机关、事业单位、社会团体、个体工商户、海外企业的详情展示。')
  // } else if (item.companyClass === 'B') {
  //   message.warning(`该组织已${item.openStatus},不再更新组织详情`)
  // } else
  if (!id) {
    message.warning('该组织详情正在准备中')
  } else {
    router.push({
      path: '/companyInfo/index',
      name: 'companyInfo-index',
      query: {
        companyId: id,
        companyName: item.entName
      }
    })
  }
}

function openExecutiveDetail(item: { executiveId: string; executiveName: string }) {
  router.push({
    path: '/executiveComments/detail',
    name: 'executiveComments-detail',
    query: {
      executiveId: item.executiveId,
      executiveName: item.executiveName
    }
  })
}
</script>

<style scoped lang="less"></style>
