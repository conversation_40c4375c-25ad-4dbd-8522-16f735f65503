<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-25 14:59:42
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 15:18:05
 * @FilePath: /corp-elf-web-consumer/src/views/followCompany/followCompanyList/components/groupCard/components/myselfGroupModal.vue
 * @Description: 
-->
<template>
  <a-modal :title="dialogTitle" v-model:open="visible" @ok="addFavorites" @cancel="visible = false" :confirmLoading="loading" :width="500">
    <a-form ref="favoritesInfoDialog" :model="form" :rules="rules">
      <a-form-item label="分组名称" name="collectName">
        <a-input ref="inputRef" placeholder="字数在1～6个字以内" v-model:value="form.collectName"></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { customerCollectAddOrEdit } from '@/api/api'
import { message } from 'ant-design-vue'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import { nextTick, ref } from 'vue'
import { customerCollectAddOrEditRequestType } from '~/types/api/customer/customerCollectAddOrEdit'
import { getCustomerCollectGroupResponseType } from '~/types/api/customer/getCustomerCollectGroup'

const emits = defineEmits(['refresh'])

const favoritesInfoDialog = ref()
const inputRef = ref<InputRef>()
const loading = ref(false)
const visible = ref(false)
const dialogTitle = ref<'新增分组' | '编辑分组'>('新增分组')
const repeatNameList = ref<string[]>()
const form = ref({ collectName: '', collectId: '', appType: 'LITE' })
const rules = {
  collectName: [
    { required: true, type: 'string', message: '请输入' },
    { max: 6, message: '字数在1～6个字以内' }
  ]
}

/**
 * @description: 显示弹窗
 * @param {*} title 弹窗类型
 * @param {*} name 已有名称列表，判断重复名称
 * @param {getCustomerCollectGroupResponseType} item 修改传
 * @return {*}
 */
function show(title: '新增分组' | '编辑分组', name: string[], item?: getCustomerCollectGroupResponseType): void {
  dialogTitle.value = title
  repeatNameList.value = name
  form.value.collectName = item?.collectName || ''
  form.value.collectId = item?.collectId || ''
  visible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 新增或修改分组
async function addFavorites() {
  try {
    const valid = await favoritesInfoDialog.value.validate()
    console.log('valid: ', valid)

    if (repeatNameList.value?.includes(form.value.collectName)) {
      message.warning('该分组名称已存在，请重新输入')
      return
    }
    loading.value = true

    const params: Record<string, customerCollectAddOrEditRequestType> = {
      新增分组: {
        collectName: form.value.collectName,
        appType: 'LITE'
      },
      编辑分组: {
        ...form.value,
        appType: 'LITE'
      }
    }

    const { message: msg } = await customerCollectAddOrEdit(params[dialogTitle.value])
    loading.value = false
    visible.value = false
    message.success(msg)
    favoritesInfoDialog.value.resetFields()
    // return getList()
    emits('refresh')
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

defineExpose({ show })
</script>

<style scoped></style>
