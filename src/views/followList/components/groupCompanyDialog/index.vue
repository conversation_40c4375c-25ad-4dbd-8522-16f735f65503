<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-04-18 15:07:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 11:07:00
 * @FilePath: /corp-elf-web-consumer/src/views/followCompany/followCompanyList/components/groupCompanyDialog/index.vue
 * @Description: 
-->
<template>
  <a-modal
    title="编辑分组"
    v-model:open="visible"
    class="groupDialog"
    @ok="submit"
    @cancel="hide"
    :width="500"
    :confirmLoading="confirmLoading"
  >
    <a-form ref="formRef" :model="form" :rules="rules">
      <a-form-item label="分组名称" name="collectId">
        <a-select v-model:value="form.collectId" placeholder="分组名称">
          <a-select-option :value="item.collectId" v-for="(item, index) in groupList" :key="index">
            {{ item.collectName }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts" name="GroupDialog">
import { getCustomerCollectGroup, customerGroup } from '@/api/api'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

const emit = defineEmits(['confirm'])

const visible = ref(false)
const confirmLoading = ref(false)
const groupList = ref()
let companyMaps: Record<string, string> = {}
const formRef = ref()
const form = ref({ collectId: undefined })
const rules = {
  collectId: [{ required: true, message: '请选择', trigger: 'change' }]
}

async function getGroupList() {
  try {
    const { result } = await getCustomerCollectGroup({ appType: 'LITE' })
    console.log('getCustomerCollectGroup result: ', result)
    groupList.value = (result || []).filter(item => !['全部', '未分组'].includes(item.collectName))
  } catch (error) {
    console.error(error)
  }
}

function show({ companyMaps: _companyMaps }: { companyMaps: Record<string, string> }) {
  getGroupList()
  visible.value = true
  companyMaps = _companyMaps
}

function hide() {
  visible.value = false
  confirmLoading.value = false
  formRef.value.resetFields()
}

async function submit() {
  try {
    confirmLoading.value = true
    await formRef.value.validate()
    console.log('companyMaps: ', companyMaps)
    const { message: msg } = await customerGroup({
      collectId: form.value.collectId!,
      companyMaps,
      companyId: Object.keys(companyMaps).map(key => key)
    })
    message.success(msg)
    hide()
    emit('confirm')
    confirmLoading.value = false
  } catch (error) {
    confirmLoading.value = false
    console.error(error)
  }
}

defineExpose({ show })
</script>

<style lang="less" scoped></style>
