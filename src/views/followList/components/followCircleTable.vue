<template>
  <Table
    title="圈子列表"
    :loading="loading"
    :columns="columns"
    :data-source="dataList"
    :pagination="{ ...pageParams, onChange: handlerCurrentChange, hideOnSinglePage: false }"
    rowKey="id"
    @selectedRowsChange="handlerSelectedRowsChange"
    :sort-config="sortOptions"
    v-model:sort="sortType"
  >
    <template #button>
      <a-button
        v-show="checkList.length !== 0"
        class="flex items-center"
        danger
        :icon="h(DeleteOutlined)"
        @click="unReceiveLayer(checkList)"
        >取消关注</a-button
      >
    </template>

    <template #extra>
      <div style="height: 32px; line-height: 32px">
        <filterForm v-model:value="filterFormState" :config="filterFormConfig" @ok="handlerFilterFun" @reset="handlerFilterReset" />
      </div>
    </template>

    <template #layerName="{ text, record }">
      <div class="flex-start-center" style="height: 35px; line-height: 35px">
        <p class="hoverPrimaryColor ellipsis" style="margin-right: 8px" @click="openCircleLayerDetail(record)">
          {{ text }}
        </p>
        <a v-if="record?.website" target="_blank" class="flex1" :href="transformWebsite(record?.website)">官网</a>
      </div>
    </template>

    <template #location="{ record }">
      {{ transformLocation({ province: record?.province || '', city: record?.city || '' }) }}
    </template>

    <template #actions="rowItem: actionsBodyCell<ShowLayerResponse>">
      <moreIcon v-show="rowItem.visibility" :menuList="menuList" @click="menuItem => handlerMoreIconClick(menuItem, rowItem)" />
    </template>
  </Table>
</template>

<script setup lang="ts">
import { layerShowLayer, layerUnReceiveLayer } from '@/api/api'
import Table, { actionsBodyCell } from '@comp/table/index'
import useListLoading from '@/hooks/useListLoading'
import { computed, h, ref, watch } from 'vue'
import filterForm, { filterFromProps } from '@/components/filterForm/index.vue'
import provincesAndRegionsOptions from '@/assets/json/provincesAndRegions.json'
import { useTransformCascaderData } from '@/hooks/useTransformCascaderData'
import { cloneDeep, has } from 'lodash-es'
import { transformLocation, transformWebsite } from '@/utils/util'
import { message, Modal, theme } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { ShowLayerRequest, ShowLayerResponse } from '~/types/api/layer/showLayer'
import { DeleteOutlined } from '@ant-design/icons-vue'
import moreIcon, { type MenuItem } from '@comp/actionIcon/more.vue'
import iconfontIcon from '@/components/tools/iconfontIcon'

const router = useRouter()
const { useToken } = theme
const { token: themeToken } = useToken()

function openCircleLayerDetail(record: ShowLayerResponse) {
  router.push({
    path: '/circleLayer/detail',
    query: { name: record.layerName, id: record.id }
  })
}

// 更多按钮
const menuList: MenuItem[] = [{ title: '取消关注', key: 'goBack', style: { color: themeToken.value.colorError } }]
/**
 * @description: 处理更多按钮点击事件
 * @param {*} menuItem
 * @param {*} rowItem
 * @return {*}
 */
function handlerMoreIconClick(menuItem: MenuItem, rowItem: { text: string; record: ShowLayerResponse }) {
  switch (menuItem.key) {
    case 'goBack':
      unReceiveLayer([rowItem.record])
      break

    default:
      break
  }
}

// 退回按钮
async function unReceiveLayer(layerList: Array<any>) {
  const receiveLayerList = layerList.filter(item => item.isReceive !== false).map(item => item.layerName) // 过滤出领取的数据
  if (receiveLayerList.length === 0) {
    message.warning('无可退回数据')
    return
  }

  Modal.confirm({
    title: '提示',
    content: '是否取消关注该圈层?',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    onOk: async () => {
      try {
        loading.value = true
        const { message: msg } = await layerUnReceiveLayer({ layerNames: receiveLayerList })
        message.success(msg)
        getData()
        loading.value = false
      } catch (error) {
        loading.value = false
        console.error(error)
      }
    }
  })
}

// 排序方法
const sortType = ref<'company_cnt' | 'client_cnt2'>('company_cnt')
const sortOptions = [
  { label: '关联企业数', value: 'company_cnt' },
  { label: '关联我的关注', value: 'client_cnt2' }
]
watch(sortType, newVal => {
  console.log('newVal,: ', newVal)
  handlerCurrentChange(1, pageParams.value.pageSize!)
})

// 筛选器相关
const filterFormState = ref<Record<string, any>>({}) // 筛选默认值
const filterFormConfig: filterFromProps['config'] = [
  {
    label: '圈子名称',
    key: 'layerName',
    formItemType: 'input'
  },
  {
    label: '所属地区',
    key: 'provincesAndRegions',
    formItemType: 'cascader',
    optionsData: provincesAndRegionsOptions,
    mode: 'multiple',
    placeholder: '省/市/区'
  },
  {
    label: '圈子级别',
    key: 'layerLevel',
    formItemType: 'select',
    optionsData: [
      { label: '国家级', value: '国家级' },
      { label: '省级', value: '省级' },
      { label: '省会级', value: '省会级' },
      { label: '地市级', value: '地市级' },
      { label: '未知', value: '未知' }
    ]
  },
  {
    label: '关联企业数',
    key: 'companyCnt',
    formItemType: 'rangeInputNumber',
    max: Infinity
  },
  {
    label: '关联我的关注',
    key: 'clientCnt',
    formItemType: 'rangeInputNumber',
    max: Infinity
  }
]

// 筛选确定回掉
function handlerFilterFun() {
  handlerCurrentChange(1, pageParams.value.pageSize as number)
}
// 筛选重置回掉
function handlerFilterReset() {
  filterFormState.value = {}
  handlerCurrentChange(1, pageParams.value.pageSize as number)
}
const filterParams = computed(() => {
  const _filterFormState = cloneDeep(filterFormState.value)

  let returnData = { ..._filterFormState }

  // 关联企业数
  let companyCnt = {}
  if (has(_filterFormState, 'companyCnt')) {
    companyCnt = {
      minCompanyCnt: _filterFormState.companyCnt[0] || null,
      maxCompanyCnt: _filterFormState.companyCnt[1] || null
    }
    delete returnData.companyCnt
    returnData = {
      ...returnData,
      ...companyCnt
    }
  }

  // 关联客户数
  let clientCnt = {}
  if (has(_filterFormState, 'clientCnt')) {
    clientCnt = {
      minClientCnt: _filterFormState.clientCnt[0] || null,
      maxClientCnt: _filterFormState.clientCnt[1] || null
    }
    delete returnData.clientCnt
    returnData = {
      ...returnData,
      ...clientCnt
    }
  }

  let provincesAndRegions
  if (has(_filterFormState, 'provincesAndRegions')) {
    provincesAndRegions = useTransformCascaderData(_filterFormState.provincesAndRegions, provincesAndRegionsOptions).map(item => item.value)
    returnData = {
      ...returnData,
      ...{ provincesAndRegions }
    }
  }

  console.log('returnData: ', returnData)
  return returnData
})

// 表格相关参数
const columns = [
  { title: '圈子', dataIndex: 'layerName', slotName: 'layerName', ellipsis: true },
  { title: '所属地区', slotName: 'location' },
  { title: '圈子级别', dataIndex: 'layerLevel' },
  { title: '会长/理事长', dataIndex: 'managerName' },
  { title: '关联企业数', dataIndex: 'companyCnt' },
  { title: '关联我的关注', dataIndex: 'clientCnt' },
  { title: '', slotName: 'actions', width: 100 }
]

const params = computed<ShowLayerRequest>(() => ({
  filter: {
    isOwner: true,
    isOnlyOwner: true,
    ...filterParams.value,
    orderColumns: [{ columnName: sortType.value, asc: false }]
  }
}))
// 选中企业相关
const checkList = ref<ShowLayerResponse[]>([])
function handlerSelectedRowsChange(keys: string, rows: ShowLayerResponse[]) {
  console.log('rows: ', rows)
  checkList.value = rows
}
const { dataList, pageParams, loading, getData } = useListLoading(layerShowLayer, params, {
  pageParams: { pageSize: 30 }
})
// 分页改变
function handlerCurrentChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}
</script>

<style lang="less" scoped>
.navBox {
  height: fit-content;
  max-height: calc(100vh - 40px);

  ::v-deep .ant-collapse-content-box {
    padding: 0;
    padding-block: 0 !important;
  }
  ::v-deep .ant-collapse-header {
    padding: 0;
    padding-inline-start: 0 !important;
  }

  .navItem {
    text-align: center;
    margin-bottom: 4px;
    .navTitle {
      a {
        color: #000;
        cursor: pointer;
        line-height: 40px;
        border-radius: 6px;
        font-weight: 500;
        display: block;
        &:hover {
          color: #fff;
          background-color: #6553ee;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }

      .active {
        color: #fff;
        background-color: #6553ee;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
      }
    }
  }
}
</style>
