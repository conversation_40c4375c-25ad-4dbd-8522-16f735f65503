<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-01 14:03:40
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 14:50:34
 * @FilePath: /corp-elf-web-consumer/src/views/followList/components/followCompanyTable.vue
 * @Description: 
-->
<template>
  <Table
    title="关注列表"
    rowKey="id"
    :dataSource="companyListData"
    :columns="tableColumns"
    :loading="loading"
    :pagination="{ ...paginationParams, onChange: handlerCurrentChange, hideOnSinglePage: false }"
    :scroll="{ x: 1000 }"
    @selectedRowsChange="handlerSelectedRowsChange"
  >
    <template #button>
      <a-space>
        <template v-if="checkCompanyList.length !== 0">
          <a-button class="flex items-center" @click="handlerBtnClick('edit')" :icon="h(EditOutlined)">编辑分组</a-button>
          <a-button class="flex items-center" @click="handlerBtnClick('goBack')" danger :icon="h(DeleteOutlined)">取消关注</a-button>
        </template>
        <template v-else>
          <a-button class="flex items-center" @click="addFollowCompanyRef.show()" :icon="h(PlusOutlined)">关注</a-button>
        </template>
      </a-space>
    </template>

    <template #extra>
      <div style="height: 32px; line-height: 32px">
        <filterForm v-model:value="filterFormState" :config="filterFormConfig" @ok="handlerFilterFun" @reset="handlerFilterReset" />
      </div>
    </template>

    <template #developmentStage="rowItem">{{ !isEmpty(rowItem.text) ? rowItem.text : '-' }} </template>

    <template #collectName="rowItem">
      <a-tag>{{ rowItem.text }}</a-tag>
    </template>

    <template #actions="rowItem">
      <moreIcon v-show="rowItem.visibility" :menuList="menuList" @click="menuItem => handlerMoreIconClick(menuItem, rowItem)" />
    </template>
  </Table>
  <groupCompanyDialog ref="groupCompanyDialogRef" @confirm="refreshTableAndGroup" />
  <addFollowCompany ref="addFollowCompanyRef" :collectId="collectId" @refresh="refreshTableAndGroup" modal-type="1" />
</template>

<script setup lang="ts">
import { Modal, message, theme } from 'ant-design-vue'
import { computed, h, ref, toRef, watch } from 'vue'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { customerCancel, customerList } from '@/api/api'
import Table from '@comp/table/index'
import moreIcon, { type MenuItem } from '@comp/actionIcon/more.vue'
import useListLoading from '@/hooks/useListLoading'
import iconfontIcon from '@/components/tools/iconfontIcon'
import groupCompanyDialog from './groupCompanyDialog/index.vue'
import addFollowCompany from '@comp/addFollowModal/index.vue'
import { customerListRequestType, customerListResponseType } from '~/types/api/customer/list'
import filterForm, { filterFromProps } from '@/components/filterForm/index.vue'
import provincesAndRegionsOptions from '@/assets/json/provinceCity.json'
import { useTransformCascaderData } from '@/hooks/useTransformCascaderData'
import { cloneDeep, has, isEmpty } from 'lodash-es'

const emits = defineEmits(['refresh'])
const props = defineProps<{ collectId: string }>()

const { useToken } = theme
const { token: themeToken } = useToken()
const groupCompanyDialogRef = ref() // 编辑公司分组弹窗
const addFollowCompanyRef = ref() // 添加关注公司弹窗

const collectId = toRef(props, 'collectId') // 当前选中你的分组id

const tableColumns = ref([
  { title: '企业', slotName: 'companyCard' },
  { title: '地区', dataIndex: 'address', slotName: 'address', width: '10%', ellipsis: true },
  { title: '发展阶段', dataIndex: 'developmentStage', slotName: 'developmentStage', width: '15%', ellipsis: true },
  { title: '类型', dataIndex: 'collectName', slotName: 'collectName', width: '8%', ellipsis: true },
  // { title: '近期动态', dataIndex: 'events', width: '8%', ellipsis: true },
  // { title: '发展阶段', dataIndex: 'events', width: '8%', ellipsis: true },
  { title: '', slotName: 'actions', width: '64px' }
])
// 筛选器相关
interface filterFormStateType {
  companyName: string | undefined
  provincesAndRegions: string[][] | undefined
}
const filterFormState = ref<filterFormStateType>({
  companyName: undefined,
  provincesAndRegions: undefined
}) // 筛选默认值
const filterFormConfig: filterFromProps['config'] = [
  {
    label: '企业名称',
    key: 'companyName',
    formItemType: 'input'
  },
  {
    label: '地区',
    key: 'provincesAndRegions',
    formItemType: 'cascader',
    optionsData: provincesAndRegionsOptions,
    mode: 'multiple',
    placeholder: '省/市/区'
  }
]

// 筛选确定回掉
function handlerFilterFun() {
  handlerCurrentChange(1, paginationParams.value.pageSize as number)
}
// 筛选重置回掉
function handlerFilterReset() {
  filterFormState.value = { companyName: undefined, provincesAndRegions: undefined }
  handlerCurrentChange(1, paginationParams.value.pageSize as number)
}
const filterParams = computed(() => {
  const _filterFormState = cloneDeep(filterFormState.value)
  console.log('_filterFormState: ', _filterFormState)

  let returnData: Omit<filterFormStateType, 'provincesAndRegions'> = { ..._filterFormState }

  // let provincesAndRegions
  if (has(_filterFormState, 'provincesAndRegions')) {
    const provincesAndRegions = useTransformCascaderData(_filterFormState.provincesAndRegions, provincesAndRegionsOptions).map(
      item => item.value
    )
    returnData = {
      ...returnData,
      ...{ provincesAndRegions }
    }
  }

  console.log('returnData: ', returnData)
  return returnData
})

const params = computed<customerListRequestType>(() => ({
  isFirst: false,
  isAsc: false,
  collectId: collectId.value,
  customerType: 'PERSONAL',
  ...filterParams.value, //过滤参数
  // 排序参数
  orderColumns: [
    {
      columnName: 'powerful_rank_score',
      asc: false
    }
  ]
}))

const { loading, pageParams: paginationParams, dataList: companyListData, getData, refresh } = useListLoading(customerList, params)
watch(() => collectId.value, refresh) // 监听id变动，刷新数据
/**
 * @description: pageSize 改变时触发
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerCurrentChange(pageNo: number, pageSize: number) {
  paginationParams.value.current = pageNo
  paginationParams.value.pageSize = pageSize
  getData()
}

/**
 * @description: 选中企业相关
 * @return {*}
 */
const checkCompanyList = ref<customerListResponseType[]>([])
function handlerSelectedRowsChange(_keys: string, rows: customerListResponseType[]) {
  checkCompanyList.value = rows
}

/**
 * @description: 处理批量操作按钮
 * @param {*} type
 * @return {*}
 */
function handlerBtnClick(type: 'edit' | 'goBack') {
  if (checkCompanyList.value.length === 0) {
    message.warning('请选择要操作的数据')
    return
  }
  switch (type) {
    case 'edit':
      groupCompany(checkCompanyList.value)
      break
    case 'goBack':
      goBackCompany(checkCompanyList.value.map(item => item.companyId))
      break

    default:
      break
  }
}

// // 导入客户
// const importCustomerRef = ref()
// function showImportCustomer() {
//   importCustomerRef.value.show({
//     type: '潜在客户名单',
//     isPotentialCustomers: true
//   })
// }

// 更多按钮
const menuList: MenuItem[] = [
  { title: '编辑分组', key: 'edit' },
  { title: '取消关注', key: 'goBack', style: { color: themeToken.value.colorError } }
]
/**
 * @description: 处理更多按钮点击事件
 * @param {*} menuItem
 * @param {*} rowItem
 * @return {*}
 */
function handlerMoreIconClick(menuItem: MenuItem, rowItem: { text: string; record: customerListResponseType }) {
  switch (menuItem.key) {
    case 'edit':
      groupCompany([rowItem.record])
      break
    case 'goBack':
      goBackCompany([rowItem.record.companyId]) // 取消关注
      break

    default:
      break
  }
}

/**
 * @description: 显示分组弹窗
 * @param {*} companyRecord
 * @return {*}
 */
function groupCompany(companyRecord: customerListResponseType[]) {
  const companyMaps: Record<string, string> = {}
  companyRecord.forEach(item => (companyMaps[item.companyId] = item.collectId || '-'))
  console.log('companyMaps: ', companyMaps)
  groupCompanyDialogRef.value.show({ companyMaps })
}
/**
 * @description: 公司分组完成，刷新分组数据和表格数据
 * @return {*}
 */
async function refreshTableAndGroup() {
  await refresh()
  emits('refresh')
}

/**
 * @description: 取消关注公司
 * @param {*} companyId
 * @return {*}
 */
async function goBackCompany(companyId: string[]) {
  console.log('companyId: ', companyId)
  Modal.confirm({
    title: '提示',
    content: '是否取消关注该企业?',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    onOk: async () => {
      try {
        loading.value = true
        const { message: msg } = await customerCancel({ companyId })
        message.success(msg)
        refreshTableAndGroup()
        loading.value = false
      } catch (error) {
        loading.value = false
        console.error(error)
      }
    }
  })
}
</script>

<style lang="less" scoped>
.followCompanyList {
}
</style>
