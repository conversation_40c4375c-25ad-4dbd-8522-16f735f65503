<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-01 14:03:40
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:35:42
 * @FilePath: /corp-elf-web-consumer/src/views/followList/index.vue
 * @Description: 
-->
<template>
  <div class="relative mb16px">
    <a @click="router.back()" class="flexCenter"> <iconfontIcon icon="icon-chevron-left" /> 返回 </a>
  </div>

  <a-row :gutter="16" :wrap="false">
    <a-col flex="256px">
      <a-affix :offset-top="80">
        <groupCard />
        <!-- v-model:collectId="collectId" -->
      </a-affix>
    </a-col>

    <a-col flex="auto">
      <router-view v-slot="{ Component, route }">
        <component :is="Component" v-bind="route.path === '/followOverview/company' ? { collectId: route.query.collectId } : {}" />
      </router-view>

      <!-- :collectId="collectId" @refresh="refreshGroupCard" :key="route.path" -->
      <!-- <KeepAlive>
        <component
          :is="collectId !== 'followExecutives' ? followCompanyTable : followExecutivesTable"
          :collectId="collectId"
          @refresh="refreshGroupCard"
        ></component>
      </KeepAlive> -->
    </a-col>
  </a-row>

  <!-- <div class="followCompanyList flex h-full relative">
  
  </div> -->
</template>

<script lang="ts">
export default defineComponent({
  beforeRouteEnter: (to, from) => {
    if (to.path === '/followOverview/company' && isEmpty(to.query.collectId)) {
      return { ...to, query: { collectId: '-1', t: new Date().valueOf() } }
    }
  }
})
</script>

<script setup lang="ts">
import { defineComponent } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import groupCard from './components/groupCard/index.vue'
import { useRouter } from 'vue-router'
import { isEmpty } from 'lodash-es'

const router = useRouter()
</script>

<style lang="less" scoped>
.followCompanyList {
}
</style>
