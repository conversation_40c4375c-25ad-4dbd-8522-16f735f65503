<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-14 14:42:13
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-10 16:47:32
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/overview/overviewDetailModal.vue
 * @Description: 
-->
<template>
  <div>
    <div class="seeMoreViewpoint pl-38px mt-8px">
      <a class="flex items-center" @click="handlerOpenModal">
        <span>查看观点</span>
        <div class="iconBox flex items-center justify-center overflow-hidden border-radius-50% ml-8px p4px">
          <iconfontIcon icon="icon-arrow-right" :extraCommonProps="{ class: 'fw-450 !fs-16px color-#fff' }" />
        </div>
      </a>
    </div>

    <a-modal width="1200px" v-model:open="open" @cancel="handlerCancel" :footer="null" class="overviewDetailModal" :getContainer="false">
      <template #title>
        <p class="fs-18">{{ props.title }}</p>
      </template>

      <div class="eiaConclusion">
        <a-typography-title :level="5" class="title">商瞳结论</a-typography-title>
        <ul class="viewpointContent p-16px pr-28px">
          <li v-for="(item, index) in props.viewpointList" :key="index" class="fs-16px mb-16px flex items-start text-justify">
            <div class="block w30px text-center mr-8px point">
              <iconfontIcon icon="icon-point" :extraCommonProps="{ class: ['!fs-12px'] }" />
            </div>
            <p class="flex-1">{{ item.viewpointContent }}</p>
          </li>
        </ul>
      </div>

      <div class="researchReportConclusion mt-32px">
        <a-typography-title :level="5" class="title">研报观点</a-typography-title>
        <div class="reportMask" v-if="!userStore.isVip">
          <a-result>
            <template #icon> <img src="@/assets/images/vip.png" class="m-0 m-auto w96px" /> </template>
            <template #title>
              <span class="color-#fea127"> 开通会员查看剩余内容</span>
            </template>
            <template #extra>
              <a-button type="primary" @click="openVipModal">开通会员</a-button>
            </template>
          </a-result>
        </div>

        <a-list v-else :loading="infiniteLoading" item-layout="horizontal" :data-source="dataList" class="researchReportList">
          <template #renderItem="{ item, index }">
            <li :key="index" class="researchReportItem p16px pr-28px border-radius-8">
              <div class="researchReportTitle flex justify-between">
                <a-space>
                  <iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px'] }" />
                  <p>{{ item.author }}</p>
                </a-space>

                <a-typography-text type="secondary" class="flex items-center">
                  <a-space>
                    <span>《{{ item.title.replace(/\.[^\.]+$/g, '') }}》</span>
                    <span>{{ item.publishDate }}</span>
                  </a-space>
                </a-typography-text>
              </div>
              <ul class="researchReportSubList">
                <li
                  v-for="(subItem, subIndex) in item.viewpointList"
                  :key="subIndex"
                  class="fs-16px mb-16px flex items-start color-#7F7F7F text-justify"
                >
                  <div class="block w30px text-center mr-8px point">
                    <span class="circle inline-block background-7F7F7F w8px h8px" />
                  </div>

                  <p class="flex-1">{{ subItem.viewpointContent }}</p>
                </li>
              </ul>
            </li>
          </template>
          <template #loadMore>
            <div v-if="dataList.length > 0" class="loadMore py-8px">
              <div v-intersection-observer="handlerIntersectionObserver" class="endText">
                <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                <p v-else>没有更多了</p>
              </div>
            </div>
          </template>
        </a-list>

        <!-- <a-spin :spinning="loading">
          <ul class="researchReportList">
            <li v-for="(item, index) in knowledgeNewsData" :key="index" class="researchReportItem p16px pr-28px border-radius-8">
              <div class="researchReportTitle flex justify-between">
                <a-space>
                  <iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px'] }" />
                  <p>{{ item.author }}</p>
                </a-space>

                <a-typography-text type="secondary" class="flex items-center">
                  <a-space>
                    <span>《{{ item.title.replace(/\.[^\.]+$/g, '') }}》</span>
                    <span>{{ item.publishDate }}</span>
                  </a-space>
                </a-typography-text>
              </div>
              <ul class="researchReportSubList">
                <li
                  v-for="(subItem, subIndex) in item.viewpointList"
                  :key="subIndex"
                  class="fs-16px mb-16px flex items-start color-#7F7F7F text-justify"
                >
                  <div class="block w30px text-center mr-8px point">
                    <span class="circle inline-block background-7F7F7F w8px h8px" />
                  </div>

                  <p class="flex-1">{{ subItem.viewpointContent }}</p>
                </li>
              </ul>
            </li>
          </ul>
        </a-spin> -->
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useThemeStore, useUserStore } from '@/store'
import { inject, ref } from 'vue'
import { ListIndustrySummaryViewpointResponse } from '~/types/api/industry/Information/listIndustrySummaryViewpoint'
import { listIndustryViewpoint } from '@/api/api'
import { ListIndustryViewpointRequest } from '~/types/api/industry/Information/listIndustryViewpoint'
import { ViewpointTypeList } from '~/types/api/industry/Information/getIndustryInformationConfig'
import { isEmpty } from 'lodash-es'
import { theme } from 'ant-design-vue'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { vIntersectionObserver } from '@vueuse/components'
import { useEventBus } from '@vueuse/core'

const { useToken } = theme
const { token: themeToken } = useToken()
const { emit: openVipModal } = useEventBus('openVipModal')
const themeStore = useThemeStore()
const userStore = useUserStore()
const props = defineProps<{
  title: string
  industryId?: string
  viewpointType?: ViewpointTypeList
  viewpointList: ListIndustrySummaryViewpointResponse[]
}>()

const open = ref(false)
function handlerOpenModal() {
  open.value = true
  if (isEmpty(dataList.value) && userStore.isVip) {
    refresh()
  }
}
function handlerCancel() {
  open.value = false
}

const params = ref<ListIndustryViewpointRequest>({ industryId: props.industryId!, viewpointType: props.viewpointType?.viewpointTypeCode })

const {
  dataList,
  noMore,
  loading: infiniteLoading,
  refresh,
  onLoadMore
} = useInfiniteLoading(listIndustryViewpoint, params, {
  immediateReqData: false
})

/**
 * @description: 滚动到界面底部回调方法
 * @param {*} intersectionObserverList
 * @return {*}
 */
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !infiniteLoading.value && !noMore.value) {
    onLoadMore()
  }
}
</script>

<style lang="less" scoped>
.seeMoreViewpoint {
  a:hover {
    .iconBox {
      background-color: #927ffa;
    }
  }
  .iconBox {
    background-color: v-bind('themeStore.getColorPrimary');
  }
}

.overviewDetailModal {
  .title {
    color: v-bind('themeStore.getColorPrimary');
    // color: red;
  }
  .eiaConclusion {
    // color: red;
    .viewpointContent {
      li {
        white-space: pre-wrap;
      }
    }
  }

  .reportMask {
    background-image: url('@/assets/images/report-mask.jpg');
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;
    height: 320px;
  }

  .researchReportConclusion {
    .researchReportList {
      .researchReportItem {
        background-color: v-bind('themeToken.colorPrimaryBg');
        + .researchReportItem {
          margin-top: 16px;
        }
        .researchReportTitle {
          margin-bottom: 16px;
        }
        .researchReportSubList {
          li {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>
