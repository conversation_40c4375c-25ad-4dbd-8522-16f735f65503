<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-14 10:17:55
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-13 11:32:53
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/overview/index.vue
 * @Description: 
-->
<template>
  <div class="overview">
    <a-spin :spinning="loading">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" v-for="(viewpointVal, viewpointKey) in knowledgeViewpointData" :key="`${props.industryId}-${viewpointKey}`">
          <a-card :bordered="false" class="">
            <div class="viewpointItem">
              <div class="viewpointTitle flex items-center mb8px">
                <iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px mr-8px'] }" />
                <a-typography-title :level="5" class="flex-1 !mb0">{{ viewpointK<PERSON> }}</a-typography-title>
              </div>
              <ul class="viewpointContent overflow-hidden">
                <template v-if="!isEmpty(viewpointVal)">
                  <li v-for="(item, index) in viewpointVal" :key="index" class="fs-16px pr-32px flex items-start">
                    <div class="block w30px text-center mr-6px point">
                      <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
                    </div>

                    <p class="flex-1 ellipsis-5">{{ item.viewpointContent }}</p>
                  </li>
                </template>
                <template v-else>
                  <empty />
                </template>
              </ul>
              <!-- 更多观点 -->
              <div v-if="!isEmpty(viewpointVal)">
                <OverviewDetailModal
                  :title="viewpointKey"
                  :industryId="props.industryId"
                  :viewpointType="find(props.knowledgeTypeList, { viewpointTypeName: viewpointKey })"
                  :viewpointList="viewpointVal"
                />
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col class="flex items-end">
          <companyWrapper />
        </a-col>
      </a-row>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { find, isEmpty, isUndefined } from 'lodash-es'
import { watch, ref } from 'vue'
import { ViewpointTypeList } from '~/types/api/industry/Information/getIndustryInformationConfig'
import { listIndustrySummaryViewpoint } from '@/api/api'
import { ListIndustrySummaryViewpointResponse } from '~/types/api/industry/Information/listIndustrySummaryViewpoint'
import iconfontIcon from '@/components/tools/iconfontIcon'
import OverviewDetailModal from './overviewDetailModal.vue'
import empty from '@/components/empty/index.vue'

const props = defineProps<{ industryId: string | undefined; knowledgeTypeList: ViewpointTypeList[] | undefined }>()
const loading = ref(false)

const knowledgeViewpointData = ref<Record<string, ListIndustrySummaryViewpointResponse[]>>({
  现状: [],
  前景: [],
  环境: [],
  挑战: [],
  机遇: []
})
/**
 * @description: 获取洞见观点
 * @return {*}
 */
async function getKnowledgeViewpoint() {
  try {
    loading.value = true
    const { result } = await listIndustrySummaryViewpoint({
      industryId: props.industryId!
      // viewpointType: 'CHALLENGE'
    })
    let viewPointList: ListIndustrySummaryViewpointResponse[] = []
    result.forEach(item => {
      const tempList: ListIndustrySummaryViewpointResponse[] = item.viewpointContent.split('\n').map(textItem => ({
        industryId: item.industryId,
        viewpointContent: textItem,
        viewpointTypeName: item.viewpointTypeName
      }))
      viewPointList = viewPointList.concat(tempList)
    })
    // knowledgeViewpointData.value = groupBy(viewPointList, 'viewpointTypeName')
    const tempData: Record<string, ListIndustrySummaryViewpointResponse[]> = {
      现状: [],
      前景: [],
      环境: [],
      挑战: [],
      机遇: []
    }
    viewPointList.forEach(item => tempData[item.viewpointTypeName].push(item))
    knowledgeViewpointData.value = tempData
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

// 监听行业id变动，刷新数据
watch(
  () => props.industryId,
  async newVal => {
    if (!isUndefined(newVal)) {
      getKnowledgeViewpoint()
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.overview {
  width: 100%;
  min-height: calc(100vh - 64px - 32px);
  .viewpointItem {
    .viewpointTitle {
    }
    .viewpointContent {
      height: 294px;
      li {
        // margin-bottom: 8px;
      }
      p {
        line-height: 2;
      }
      // @media screen and (min-height: 768px) {
      //   height: 294px;
      // }
      @media screen and (min-height: 1200px) {
        height: 422px;
      }
    }
  }
}
</style>
