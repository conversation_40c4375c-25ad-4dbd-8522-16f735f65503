<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 17:17:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-03-14 11:44:56
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/overview/components/activity/index.vue
 * @Description: 行业活动
-->
<template>
  <a-card :bordered="false" class="activity" title="行业活动">
    <a-spin :spinning="loading">
      <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
        <a-table size="small" :dataSource="dataList" :columns="columns" :loading="loading" :pagination="false">
          <template #bodyCell="{ text, column }">
            <template v-if="column.dataIndex === 'exhibitionDate'">
              <span :style="{ color: theme.getColorPrimary }">
                {{ text ? text : '-' }}
              </span>
            </template>
          </template>
        </a-table>
      </a-config-provider>
    </a-spin>
  </a-card>
</template>

<script lang="ts">
export default {
  name: 'activity'
}
</script>

<script setup lang="ts">
import { listIndustryExhibition } from '@/api/api'
import { useThemeStore } from '@/store'
import { isUndefined } from 'lodash-es'
import { ref, watch } from 'vue'
import { ListIndustryExhibitionResponse } from '~/types/api/industry/Information/listIndustryExhibition'
const props = defineProps<{ industryId: string | undefined }>()

const theme = useThemeStore()
const columns = [
  { title: '活动名称', dataIndex: 'name', ellipsis: true },
  { title: '活动日期', dataIndex: 'exhibitionDate', width: 120, align: 'center' }
]

const loading = ref(true)
const dataList = ref<ListIndustryExhibitionResponse[]>([])

async function getActivityList() {
  try {
    loading.value = true
    const { result } = await listIndustryExhibition({ industryId: props.industryId! })
    dataList.value = result
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

// 监听行业id变化，重新请求数据
watch(
  () => props.industryId,
  newVal => {
    if (!isUndefined(newVal)) {
      getActivityList()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped></style>
