<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-12 17:26:08
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-21 10:08:29
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/benchmark/components/caseList/caseItem.vue
 * @Description: 
-->
<template>
  <div class="caseItem">
    <a-card
      :bordered="false"
      :bodyStyle="{
        padding: '18px 16px 4px'
      }"
      class="mt-16px"
    >
      <div class="titleContent flex items-center">
        <iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px mr-8px'] }" />
        <div class="flex-1">
          <a-typography-title :level="5" class="companyName hoverPrimaryColor inline-block" @click="openCompanyInfo">
            {{ props.caseData.companyAbbr || '' }}
          </a-typography-title>
        </div>
        <!-- <iconfontIcon
          v-if="caseList.length > 1 || noMore"
          @click="shrinkCardBtnClick"
          icon="icon-chevron-up"
          :extraCommonProps="{ class: ['color-#ccc hoverPrimaryColor transition-all', !shrinkCard ? 'rotate--180' : ''] }"
        /> -->
      </div>

      <a-list :loading="loading" item-layout="horizontal" :data-source="caseList" class="pl-40px">
        <!-- 渲染列表 -->
        <template #renderItem="{ item }">
          <div>
            <!-- class="h-130px" -->
            <div class="mainContent">
              <div class="time">
                <a-typography-text type="secondary">{{ getPublishDate(item.publishDate) }}</a-typography-text>
              </div>
              <div class="content color-#7F7F7F fs-16px ellipsis-8 text-justify">
                {{ item.viewpointContent }}
              </div>
            </div>

            <div class="secondaryContent pl2em color-#00000073 text-right">
              <text-clamp
                :max-lines="1"
                location="middle"
                :text="`—— 《${item.title.replace(/\.[^\.]+$/g, '')}》 ${item.author}`"
                :class="item.informationType === 'NEWS' && !isEmpty(item.sourceUrl) ? 'hoverPrimaryColor' : ''"
                @click="openSourceUrl(item)"
              >
              </text-clamp>
            </div>
          </div>
        </template>

        <!-- 加载更多按钮 -->
        <template #loadMore>
          <div class="loadMore mt2px text-center">
            <a-typography-text
              v-if="noMore"
              type="secondary"
              class="hoverPrimaryColor fs-13px text-center inline-flex justify-center items-center"
              @click="moreDataBtnClick"
            >
              <iconfontIcon icon="icon-chevron-down" :extra-common-props="{ class: 'fs-12px' }" />
              查看历史
            </a-typography-text>
          </div>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { computed, ref } from 'vue'
import {
  companyCaseViewpointType,
  industryCompanyViewpointRequestType,
  industryCompanyViewpointResponseType
} from '~/types/api/industry/Information/industryCompanyViewpoint'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { industryInformationAllIndustryCompanyViewpoint } from '@/api/api'
import { isEmpty, unionBy } from 'lodash-es'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const props = defineProps<{
  caseData: industryCompanyViewpointResponseType
  companyUniId: string
  getMoreDataParams: industryCompanyViewpointRequestType
}>()

// 解析日期
const getPublishDate = (date: number) => {
  const diffDay = dayjs(date).set('h', 0).set('m', 0).set('s', 0)
  const nowDay = dayjs().set('h', 0).set('m', 0).set('s', 0)
  const diffRes = nowDay.diff(diffDay, 'd')
  if (diffRes < 1) {
    return '今日'
  } else if (diffRes === 1) {
    return '昨日'
  } else {
    return dayjs(date).format('M月D日')
  }
}

/** 是否收缩 */
const shrinkCard = ref(false)
/** 是否显示加载更多按钮 */
const noMore = computed(() => {
  if (props.caseData.moreData && !loading.value) {
    return shrinkCard.value ? caseList.value.length < paginationParams.value.total : true
  } else {
    return false
  }
})

const loading = ref(false)
const dataList = ref<companyCaseViewpointType[]>([])
const paginationParams = ref({
  current: 0, // 当前页数
  pageSize: 5, // 每页显示条目个数
  total: 0, // 总条目数
  maxPages: 0 // 最大页
})
const caseList = computed(() => {
  if (!shrinkCard.value) {
    return props.caseData.list
  } else {
    return unionBy([...props.caseData.list, ...dataList.value], 'id').sort((a, b) =>
      a.publishDate < b.publishDate ? 1 : a.publishDate > b.publishDate ? -1 : 0
    )
  }
})
/**
 * @description: 获取跟多数据
 * @return {*}
 */
async function getMoreData() {
  try {
    loading.value = true
    const { result } = await industryInformationAllIndustryCompanyViewpoint({
      pageNo: paginationParams.value.current,
      pageSize: paginationParams.value.pageSize,
      companyUniId: props.companyUniId,
      ...props.getMoreDataParams
    })
    const { total, pages, records } = result
    dataList.value = dataList.value.concat(records)
    paginationParams.value.total = total
    paginationParams.value.maxPages = pages
    shrinkCard.value = true // 设置为展开状态
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

function shrinkCardBtnClick() {
  shrinkCard.value = !shrinkCard.value

  if (shrinkCard.value) {
    paginationParams.value.current = 1
    dataList.value = []
    getMoreData()
  }
}

function moreDataBtnClick() {
  if (!shrinkCard.value) {
    paginationParams.value.current = 1
    dataList.value = []
  } else {
    paginationParams.value.current++
  }
  getMoreData()
}

function openSourceUrl(item: companyCaseViewpointType) {
  if (item.informationType === 'NEWS' && !isEmpty(item.sourceUrl)) {
    window.open(item.sourceUrl, '_blank')
  }
}

// 打开企业
function openCompanyInfo() {
  // props.caseData[0]
  console.log('props.caseData[0]: ', props.caseData)
  if (props.caseData.companyUniId) {
    router.push({
      path: '/companyInfo/index',
      name: 'companyInfo-index',
      query: {
        companyId: props.caseData.companyUniId,
        companyName: props.caseData.companyName
      }
    })
  } else {
    message.warning('该组织详情正在准备中')
  }
}
</script>

<style lang="less" scoped>
.caseItem {
  &:hover {
    .loadMore {
      visibility: initial;
    }
  }

  .titleContent {
    margin-bottom: 4px;
    .companyName {
      margin-bottom: 0px;
    }
  }

  .mainContent {
    .time {
      margin-bottom: 4px;
    }
    .content {
      margin-bottom: 4px;
      white-space: pre-wrap;
    }
  }

  .secondaryContent {
  }

  .loadMore {
    visibility: hidden;
    height: 20px;
  }
}
</style>
