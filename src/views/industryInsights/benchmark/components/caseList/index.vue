<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-12 17:24:51
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-13 11:08:38
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/benchmark/components/caseList/index.vue
 * @Description: 
-->
<template>
  <div class="caseList">
    <a-card :bordered="false" :bodyStyle="{ padding: '0' }" class="caseListTitle">
      <template #title>
        <div class="flex items-center">
          <p class="mr-8px">标杆案例</p>

          <div class="flex items-center">
            <iconfontIcon
              icon="icon-search"
              :extra-common-props="{ class: 'fs-18px hoverPrimaryColor mr-8px color-#7F7F7F' }"
              @click="handlerSearchIconBtnClick"
            />
            <a-input
              v-show="showKeyWordInput"
              ref="keywordRef"
              v-model:value="filterParams.keyword"
              style="width: 220px"
              class="!fs-14 fw-400 transition-all"
              placeholder="关键词搜索"
              size="small"
              @change="handlerKeyWordChange"
              @blur="handlerKeyWordBlur"
              allowClear
            ></a-input>
          </div>
        </div>
      </template>
      <!-- <template #extra>
        <a-select v-model:value="filterParams.timeRange" size="small" placeholder="请选择" @change="refresh" :bordered="false">
          <a-select-option value="所有时间" title="所有时间">所有时间</a-select-option>
          <a-select-option value="最近1天" title="最近1天">最近1天</a-select-option>
          <a-select-option value="最近1周" title="最近1周">最近1周</a-select-option>
          <a-select-option value="近1个月" title="近1个月">近1个月</a-select-option>
          <a-select-option value="近3个月" title="近3个月">近3个月</a-select-option>
        </a-select>
      </template> -->
    </a-card>

    <div class="caseListContent">
      <a-list :loading="infiniteLoading" item-layout="horizontal" :data-source="dataList">
        <template #renderItem="{ item, index }">
          <CaseItem :caseData="dataList[index]" :getMoreDataParams="params" :companyUniId="item.companyUniId"></CaseItem>
        </template>
        <template #loadMore>
          <div v-if="dataList.length > 0" class="loadMore py-8px">
            <div
              v-if="isEmpty(userStore.getToken)"
              class="inline-flex items-center justify-center hoverPrimaryColor endText"
              @click="showLoginModal"
            >
              <iconfontIcon icon="icon-lock-on" />
              <p class="mx4px">登录即可获取更多数据</p>
              <iconfontIcon icon="icon-chevron-down" />
            </div>
            <div v-else v-intersection-observer="handlerIntersectionObserver" class="endText">
              <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
              <p v-else>没有更多了</p>
            </div>
          </div>
        </template>
      </a-list>
    </div>
  </div>
</template>

<script setup lang="ts" name="caseList">
import { useThemeStore, useUserStore } from '@/store'
import { computed, nextTick, ref, watch } from 'vue'
import { industryInformationListIndustryCompanyViewpoint } from '@/api/api'
import { industryCompanyViewpointRequestType } from '~/types/api/industry/Information/industryCompanyViewpoint'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import dayjs from 'dayjs'
import CaseItem from './caseItem.vue'
import { vIntersectionObserver } from '@vueuse/components'
import { debounce, isEmpty, isUndefined } from 'lodash-es'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import showLoginModal from '@/components/loginModal'

const userStore = useUserStore()
type timeRangeType = '所有时间' | '最近1天' | '最近1周' | '近1个月' | '近3个月'
const props = defineProps<{ industryId: string | undefined }>()
const theme = useThemeStore()
const keywordRef = ref<InputRef>()
const emits = defineEmits(['loadingDone'])
const showKeyWordInput = ref(false)
/** 表单过滤参数 */
const filterParams = ref<{ timeRange: timeRangeType; keyword: string | undefined }>({
  timeRange: '所有时间',
  keyword: undefined
})
/** 请求参数 */
const params = computed<industryCompanyViewpointRequestType>(() => {
  let startTime: string | undefined = undefined
  let endTime: string | undefined = undefined

  switch (filterParams.value.timeRange) {
    case '所有时间':
      startTime = undefined
      endTime = undefined
      break
    case '最近1天':
      startTime = dayjs().subtract(1, 'd').format('YYYY-MM-DD HH:mm:ss')
      endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      break
    case '最近1周':
      startTime = dayjs().subtract(1, 'w').format('YYYY-MM-DD HH:mm:ss')
      endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      break
    case '近1个月':
      startTime = dayjs().subtract(1, 'M').format('YYYY-MM-DD HH:mm:ss')
      endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      break
    case '近3个月':
      startTime = dayjs().subtract(3, 'M').format('YYYY-MM-DD HH:mm:ss')
      endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      break

    default:
      startTime = undefined
      endTime = undefined
      break
  }

  return {
    industryId: props.industryId!, // 行业ID
    viewpointType: 'CASE', // 观点类型
    keyword: filterParams.value.keyword, // 关键字
    startTime, // 开始时间
    endTime // 结束时间
  }
})

const {
  dataList,
  noMore,
  loading: infiniteLoading,
  refresh,
  onLoadMore
} = useInfiniteLoading(industryInformationListIndustryCompanyViewpoint, params, {
  immediateReqData: false
})

// type caseListDataType = { key: string; caseData: industryCompanyViewpointResponseType[] }[]
/** 列表使用的数据 */
// const caseListData = computed<caseListDataType>(() => {
//   const tempMap = groupBy(dataList.value, 'companyUniId') // 根据企业id分组数据
//   const tempList: caseListDataType = []
//   Object.keys(tempMap).forEach(key => tempList.push({ key, caseData: tempMap[key] }))
//   console.log('tempList: ', tempList)
//   return tempList
// })

/**
 * @description: 滚动到界面底部回调方法
 * @param {*} intersectionObserverList
 * @return {*}
 */
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !infiniteLoading.value && !noMore.value) {
    onLoadMore()
  }
}

/** 关键字变动 */
const handlerKeyWordChange = debounce(refresh, 300)

/** 关键字失焦处理 */
function handlerKeyWordBlur() {
  console.log('arguments: ', arguments)
  if (isEmpty(filterParams.value.keyword)) {
    showKeyWordInput.value = false
  }
}

// /**
//  * @description: 更新企业的案例数据
//  * @param {*} updateData
//  * @param {*} key
//  * @return {*}
//  */
// function handlerUpdateCaseData(updateData: industryCompanyViewpointResponseType[], key: string): void {
//   const temp = updateData.map(item => ({ ...item, companyUniId: key }))
//   dataList.value = unionBy([...dataList.value, ...temp], 'id')
// }

/**
 * @description: 关键字输入框icon点击事件
 * @return {*}
 */
function handlerSearchIconBtnClick() {
  showKeyWordInput.value = true
  nextTick(() => keywordRef.value?.focus())
}

// watchOnce(
//   dataList,
//   () => {
//     console.log('dataList.value: ', dataList.value);
//   },
// )

const stopeDataListWatch = watch(dataList, () => {
  nextTick(() => {
    emits('loadingDone')
    stopeDataListWatch()
  })
})

// 监听行业id变动，刷新数据
watch(
  () => props.industryId,
  newVal => {
    if (!isUndefined(newVal)) {
      refresh()
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.caseList {
  .caseListTitle {
    :deep(.ant-select-selector) {
      color: #999;
    }
  }

  .caseListContent {
  }
}
</style>
