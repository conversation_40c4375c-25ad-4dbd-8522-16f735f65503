<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 17:17:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-15 11:16:33
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/benchmark/components/directory/index.vue
 * @Description: 企业名录
-->
<template>
  <a-card class="directory" :bordered="false">
    <template #title>
      <div class="flex items-center">
        <p class="mr-8px">企业名录</p>
        <a-select
          size="small"
          style="width: 220px"
          placeholder="请选择"
          v-model:value="params.rankIdList[0]"
          :bordered="false"
          :virtual="false"
        >
          <!-- @change="handlerSelectChange" -->
          <!-- :getPopupContainer="getPopupContainer" -->
          <a-select-option v-for="(item, index) in directoryList" :key="index" :value="item.id" :title="item.rankTitle">
            {{ item.rankTitle }}
          </a-select-option>
        </a-select>
      </div>
    </template>
    <template #extra> </template>

    <a-spin :spinning="loading">
      <a-config-provider :theme="{ components: { Table: { margin: `16px 0px 0`, fontSize: '16px' } } }">
        <companyDirectory
          :rankTitle="rankTitle"
          :reqParams="{
            industryId: params.industryId,
            rankIdList: params.rankIdList
          }"
        />
      </a-config-provider>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts" name="directory">
import { find, isEmpty, isUndefined } from 'lodash-es'
import { rankIndustryRankList } from '@/api/api'
import { computed, ref, watch } from 'vue'
import { useThemeStore } from '@/store'
import { IndustryRankListResponse } from '~/types/api/rank/industryRankList'
import companyDirectory from '@comp/companyDirectory/index.vue'

const props = defineProps<{ industryId: string | undefined }>()
const theme = useThemeStore()
const params = ref<{ rankIdList: string[]; industryId: string }>({ rankIdList: [], industryId: '' })
const directoryList = ref<IndustryRankListResponse[]>([]) // 名录列表
const loading = ref(true)
// const rankTitle = computed(() => find(directoryList.value, { id: params.value.rankIdList[0] }))
const rankTitle = computed(() => find(directoryList.value, { id: params.value.rankIdList[0] })?.rankTitle)
// const getPopupContainer = (_triggerNode:HTMLElement) => document.body

/**
 * @description: 请求名录列表
 * @return {*}
 */
async function getDirectoryData() {
  try {
    loading.value = true
    const { result } = await rankIndustryRankList({ industryId: props.industryId! })
    console.log('result: ', result)
    directoryList.value = result
    params.value.rankIdList = !isEmpty(directoryList.value) ? [directoryList.value[0].id] : []
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

// /**
//  * @description: 选中的名录变化重新请求列表
//  * @return {*}
//  */
// //
// function handlerSelectChange(key, item) {
//   handlerSizeChange(1, 10)
// }

// 监听行业id变化，重新请求数据
watch(
  () => props.industryId,
  newVal => {
    if (!isUndefined(newVal)) {
      params.value.industryId = props.industryId as string
      params.value.rankIdList = []
      getDirectoryData()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.directory {
  :deep(.ant-card-head-title) {
    overflow: initial;
  }

  :deep(.ant-select-selector) {
    color: #999;
  }
  .secondaryText {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
