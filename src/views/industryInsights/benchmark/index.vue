<template>
  <a-row :gutter="[16, 16]">
    <a-col span="16">
      <a-row :gutter="[16, 16]">
        <a-col span="24">
          <caseList :industryId="props.industryId" />
        </a-col>
      </a-row>
    </a-col>
    <a-col span="8">
      <!-- <activity :industryId="props.industryId" class="mb-16px" /> -->

      <div class="sticky t-80px">
        <directory :industryId="props.industryId" class="mb-16px" />
        <companyWrapper />
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
import companyWrapper from '@/components/companyWrapper/index.vue'
import activity from './components/activity/index.vue'
import directory from './components/directory/index.vue'
import caseList from './components/caseList/index.vue'

const props = defineProps<{ industryId: string | undefined }>()
</script>

<style scoped></style>
