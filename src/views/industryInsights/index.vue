<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-01-22 14:28:21
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 15:06:45
 * @FilePath: /corp-elf-web-consumer/src/views/industryInsights/index.vue
 * @Description: 行业洞察
-->
<template>
  <div class="industryInsightsOverview relative flex">
    <a-collapse
      :activeKey="activeIndustryId"
      ghost
      accordion
      class="navBox sticky top-80px pr-16px w100px min-w-100px"
      @change="handlerCollapseChange"
    >
      <a-collapse-panel v-for="item in industryData" :key="item.industryId" :showArrow="false" class="navItem">
        <template #header>
          <div class="navTitle">
            <a :class="[item.industryId === activeIndustryId ? 'active' : '']">
              {{ item.industryName }}
            </a>
          </div>
        </template>
        <div class="navSubList">
          <ul>
            <li v-for="(item, index) in navSubList" :key="index">
              <a @click="changeActiveRoute(item.value)" :class="[activeRoute === item.value ? 'active' : '', 'hoverPrimaryColor']">
                {{ item.label }}
              </a>
            </li>
          </ul>
        </div>
      </a-collapse-panel>
    </a-collapse>

    <div class="flex-1 w100%">
      <keep-alive>
        <component
          :is="activeComp"
          :industryId="activeIndustryId"
          :industryName="activeIndustryInfo?.industryName"
          :knowledgeTypeList="viewpointTypeList"
          type="1"
        ></component>
      </keep-alive>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue'
import { getIndustryInformationConfig } from '@/api/api'
import { find, first, isEmpty } from 'lodash-es'
import { UnlockIndustryList, ViewpointTypeList } from '~/types/api/industry/Information/getIndustryInformationConfig'
import { theme } from 'ant-design-vue'
import 'driver.js/dist/driver.css'
import { useUserStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import benchmarkComp from './benchmark/index.vue'
import executiveComments from '@/views/executiveComments/list/index.vue'
import { useEventBus } from '@vueuse/core'

const route = useRoute()
const router = useRouter()
const { useToken } = theme
const { token: themeToken } = useToken()
const loading = ref(false)

const activeIndustryId = ref<string>() // 当前选中的行业id
const activeIndustryInfo = computed(() => find(industryData.value, { industryId: activeIndustryId.value }))
const industryData = ref<UnlockIndustryList[]>([]) // 所有可选行业数据
const viewpointTypeList = ref<ViewpointTypeList[]>() // 观点列表数据
// 子页面
type activeRouteType = 'Benchmark' | 'ExecutiveComments' | 'Overview'
const activeRoute = ref<activeRouteType>(isEmpty(route.query) ? 'Benchmark' : (route.query.activeRoute as activeRouteType))
// const activeRoute = toRef(props, 'activeRoute')
const activeComp = computed(() => find(navSubList, { value: activeRoute.value })?.component)
const navSubList: { label: string; value: activeRouteType; component: any }[] = [
  { label: '标杆事件', value: 'Benchmark', component: benchmarkComp },
  { label: '高管说', value: 'ExecutiveComments', component: executiveComments }
  // { label: '智库观点', value: 'Overview', component: overviewComp }
]

async function getIndustryList() {
  try {
    loading.value = true
    const { result } = await getIndustryInformationConfig({})
    industryData.value = result.unlockIndustryList
    viewpointTypeList.value = result.viewpointTypeList
    activeIndustryId.value = first(industryData.value)?.industryId
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

function handlerCollapseChange(_key: string | undefined) {
  if (!isEmpty(_key)) {
    activeIndustryId.value = _key
    activeRoute.value = 'Benchmark'
  }
}

const userStore = useUserStore()
const { emit: openVipModal } = useEventBus('openVipModal')
function changeActiveRoute(val: activeRouteType) {
  if (!userStore.isVip && val === 'ExecutiveComments') {
    openVipModal()
    return
  }
  activeRoute.value = val
}

// /**
//  * @description: 新手指引
//  * @return {*}
//  */
// function showTips() {
//   const tourGuideStore = useTourGuideStore()
//   if (!tourGuideStore.isShowTourGuide) {
//     return
//   }

//   const driverObj = driver()
//   driverObj.setConfig({
//     showProgress: true,
//     popoverOffset: 30,
//     stageRadius: 8,
//     progressText: '{{current}}/{{total}}',
//     nextBtnText: '下一条',
//     prevBtnText: '上一条',
//     doneBtnText: '立即体验',
//     animate: false,
//     overlayOpacity: 0.45,
//     onPopoverRender: el => {
//       el.nextButton.classList.add('driver-btn', 'driver-btn-next')
//       el.previousButton.classList.add('driver-btn', 'driver-btn-prev')
//     },
//     onDestroyStarted: () => {
//       console.log(1212)
//       if (driverObj.hasNextStep()) {
//         const step = driverObj.getActiveIndex()
//         console.log('step: ', step)
//         if (step === 0) {
//           activeRoute.value = 'Overview'
//           nextTick(() => driverObj.moveNext())
//         } else if (step === 1) {
//           router.push({ path: '/followCompany/tourGuide' }).then(() => driverObj.moveNext())
//         } else {
//           driverObj.moveNext()
//         }
//       } else {
//         driverObj.destroy()
//       }
//     },
//     onDestroyed: () => {
//       router.replace({ path: '/industryInsights/overview' }).then(() => {
//         activeRoute.value = 'Benchmark'
//       })
//     }
//   })

//   driverObj.setSteps([
//     {
//       element: '.ant-collapse .navItem:nth-child(1) .navSubList li:nth-child(1) a',
//       popover: {
//         title: '标杆事件',
//         description: `
//         <div class="tourItem">
//           <p>标杆案例：浏览行业领军企业的报道，抓住目标客户的行业发展动向</p>
//         </div>
//         <div class="tourItem">
//           <p>企业名录：寻找行业内细分领域的企业名录，发现潜在客户资源</p>
//         </div>
//         `,
//         onNextClick: () => {
//           activeRoute.value = 'Overview'
//           nextTick(() => {
//             driverObj.moveNext()
//           })
//         },
//         showButtons: ['next'],
//         side: 'right', // "top" | "right" | "bottom" | "left";
//         align: 'start' // "start" | "center" | "end";
//       }
//     },
//     {
//       element: '.ant-collapse .navItem:nth-child(1) .navSubList li:nth-child(3) a',
//       popover: {
//         title: '智库观点',
//         description: `
//         <div class="tourItem">
//           <p>综合多个机构研报，得出对目标客户所在行业现状、前景、环境、挑战、机遇的总结</p>
//         </div>
//         <div class="tourItem">
//           <p>可查看更多机构研报的详细观点</p>
//         </div>
//         `,
//         onPrevClick: () => {
//           activeRoute.value = 'Benchmark'
//           nextTick(() => {
//             driverObj.movePrevious()
//           })
//         },
//         onNextClick() {
//           router.push({ path: '/followCompany/tourGuide' }).then(() => driverObj.moveNext())
//         },
//         side: 'right',
//         align: 'start'
//       }
//     },
//     {
//       element: '.headerMenu .navItem:nth-child(3) span',
//       popover: {
//         title: '关注企业',
//         description: `
//         <div class="tourItem">
//           <p>跟踪目标客户的动态，及时抓住销售机会</p>
//         </div>
//         <div class="tourItem">
//           <p>可管理多个类型分组，跟踪客户、伙伴、竞对等不同角色的动态</p>
//         </div>
//         `,
//         onPrevClick: () => {
//           router.back()
//           nextTick(() => {
//             setTimeout(() => {
//               driverObj.movePrevious()
//             }, 100)
//           })
//         },
//         side: 'bottom',
//         align: 'start'
//       }
//     },
//     {
//       element: '.customCircle .ant-card-head-title p span',
//       popover: {
//         title: '客户圈子',
//         description: '<div class="tourItem"><p>挖掘与已关注企业密切相关的高价值圈子，与圈子共办活动以批量影响目标客户</p></>'
//       }
//     }
//   ])

//   driverObj.drive(0)

//   // 设置新手指引已经访问过，下次不显示
//   const key = tourGuideStore.getTourGuideStorageKey
//   setLocal(key, false, null)
//   tourGuideStore.setShowTourGuide(false)
// }

onBeforeMount(async () => {
  await getIndustryList()
})
</script>

<style lang="less" scoped>
.industryInsightsOverview {
  position: relative;
  min-height: calc(100vh - 64px - 32px);

  .navBox {
    height: fit-content;
    max-height: calc(100vh - 40px);

    :deep(.ant-collapse-content-box) {
      padding: 0;
    }
    :deep(.ant-collapse-header) {
      padding: 0;
    }

    .navItem {
      text-align: center;
      margin-bottom: 4px;
      .navTitle {
        a {
          color: #000;
          cursor: pointer;
          line-height: 40px;
          border-radius: 6px;
          font-weight: 500;
          display: block;
          &:hover {
            color: #fff;
            background-color: #6553ee; // v-bind('theme.getColorPrimary');
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
          }
        }

        .active {
          color: #fff;
          background-color: #6553ee; // v-bind('theme.getColorPrimary');
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }
      }
      .navSubList {
        li {
          text-align: right;
          // line-height: 32px;
          padding: 4px 14px 4px 0;
          a {
            display: inline-block;
            color: v-bind('themeToken.colorTextSecondary');
            margin-top: 4px;
            cursor: pointer;
            &:hover {
              color: #6553ee; // v-bind('themeToken.colorPrimaryTextHover');
              .tips {
                color: #ff4d4f;
              }
            }
          }
          .active {
            color: #6553ee; // v-bind('themeToken.colorPrimaryTextHover');
          }
        }
      }
    }
  }
}
</style>
