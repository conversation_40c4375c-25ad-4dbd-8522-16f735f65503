<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-10 14:22:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-10 11:17:38
 * @FilePath: /corp-elf-web-consumer/src/views/overview/index.vue
 * @Description: 
-->
<template>
  <a-layout class="bg-#fafafe">
    <a-layout-header v-if="!isWechat" :style="{ backgroundColor: '#fff' }">
      <div class="flex items-center flex-1 max-w1260px m-0 m-x-auto">
        <Logo />

        <div class="flex-1 flex items-center justify-end">
          <MenuLIst />
          <UserMenu />
        </div>
      </div>
    </a-layout-header>

    <a-layout-content class="scrollLock">
      <div :class="['banner bg-#fff pt60px pb30px', isWechat ? 'px32px' : '']">
        <div :class="['h360px m-0 m-x-auto ', !isWechat ? 'max-w1260px' : 'h440px']">
          <a-row class="h100%">
            <a-col :span="isWechat ? 24 : 12" class="h100% flex flex-direction-column">
              <h1 class="color-#000 fw-600 fs-42px">帮助ToB人提升客户洞察力</h1>
              <ul :class="['fs-22px color-#000000a8 mt32px', isWechat ? 'mb16px' : 'mb48px']">
                <li class="flex items-start mb8px">
                  <div class="block w30px text-center mr-6px point">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
                  </div>
                  挖掘潜在客户
                </li>
                <li class="flex items-start mb8px">
                  <div class="block w30px text-center mr-6px point">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
                  </div>
                  跟踪客户活动，捕获潜在线索
                </li>
                <li class="flex items-start mb8px">
                  <div class="block w30px text-center mr-6px point">
                    <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
                  </div>
                  了解决策人的背景、偏好和决策风格
                </li>
              </ul>
              <a-space :size="8" direction="vertical">
                <p class="fs-20px fw-500 color-#000000a8" v-if="isRegisterCode">
                  您的好友向您推荐「ToB人的客户洞察工具」，注册立即获赠 1 个月会员
                </p>
                <a-button type="primary" size="large"><router-link to="/home"> 立即登录 </router-link></a-button>
                <span class="fs-20px fw-500 color-#000000a8" v-if="!isRegisterCode">注册赠送 7 天会员</span>
              </a-space>
            </a-col>
            <a-col v-if="!isWechat" :span="isWechat ? 24 : 12" class="h100% flex justify-end">
              <img src="//www.bengine.com.cn/images/product/eia/banner.png" class="h100%" />
            </a-col>
          </a-row>
        </div>
      </div>

      <div :class="['m-x-auto mt64px mb16px', !isWechat ? 'max-w1260px' : 'px32px']">
        <div>
          <h2 class="fs-28px fw-600 mb16px">商瞳的服务</h2>

          <template v-if="isWechat">
            <a-space direction="vertical" :size="16" class="w100%">
              <FollowCompany />
              <ExecutiveSaid />
              <EnterpriseNews />
              <Circle />
              <IndustryInsights />
              <Viewpoint />
            </a-space>
          </template>

          <template v-else>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-space direction="vertical" :size="16" class="w100%">
                  <!-- 发现潜在客户 -->
                  <FollowCompany />
                  <!-- 监控客户活动，捕获潜在线索 -->
                  <EnterpriseNews />
                </a-space>
              </a-col>
              <a-col :span="12">
                <a-space direction="vertical" :size="16" class="w100%">
                  <!-- 通过决策人的言论，了解其背景、偏好和决策风格 -->
                  <ExecutiveSaid />
                  <!-- 洞察与客户互动的高影响力圈子 -->
                  <Circle />
                  <!-- 跟踪行业标杆客户活动 -->
                  <IndustryInsights />
                  <!-- <Viewpoint /> -->
                </a-space>
              </a-col>
            </a-row>
          </template>
        </div>

        <a-divider />

        <div class="color-#666 text-center">
          <p>珠海横琴指数动力科技有限公司</p>
          <p><a class="color-#666" href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2022023386号</a></p>
        </div>
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts">
import Logo from '@/components/layouts/headerMenu/logo.vue'
import UserMenu from '@comp/layouts/headerMenu/userMenu/index.vue'
import Viewpoint from './components/viewpoint.vue'
import IndustryInsights from './components/industryInsights.vue'
import FollowCompany from './components/followCompany.vue'
import ExecutiveSaid from './components/executiveSaid.vue'
import EnterpriseNews from './components/enterpriseNews.vue'
import Circle from './components/circle.vue'
import MenuLIst from '@/components/layouts/headerMenu/menuList/index'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useRoute } from 'vue-router'
import { computed } from 'vue'
import { has } from 'lodash-es'
import { onMounted } from 'vue'
import { UAParser } from 'ua-parser-js'

const route = useRoute()
const isRegisterCode = computed(() => has(route.query, 'registerCode'))

const parser = new UAParser()
const { browser, device } = parser.getResult()
const isWechat = computed(() => browser.name === 'WeChat' && device.type === 'mobile')

function setViewport() {
  // 如果是移动设备，设置缩放比例为 1.0；如果是 PC 端，设置较低的缩放比例
  let viewport = document.querySelector<HTMLMetaElement>('meta[name=viewport]')

  if (!viewport) {
    // 如果没有 viewport 标签，则创建一个
    viewport = document.createElement('meta')
    viewport.name = 'viewport'
    document.head.appendChild(viewport)
  }

  // 设置或更新 viewport 缩放
  viewport.content = `width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0`
}

onMounted(() => {
  setViewport()
  // <meta
  //     charset="utf-8"
  //     name="viewport"
  //     content=""
  //   />
})
</script>

<style scoped lang="less">
.banner {
  background-image: url('//www.bengine.com.cn/images/common/bannerBg.png');
  background-repeat: no-repeat;
  background-size: cover;
}

.serverItem {
  img {
    border-radius: 8px;
    overflow: hidden;
  }
  p {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    margin-top: 16px;
    color: #393939;
  }
}
</style>
