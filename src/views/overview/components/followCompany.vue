<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-12 16:40:03
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-13 17:23:25
 * @FilePath: /corp-elf-web-consumer/src/views/overview/components/followCompany.vue
 * @Description: 
-->
<template>
  <a-card :bordered="false" title="发现潜在客户">
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px', margin: '16px 0 0' } } }">
      <!-- :pagination="{ ...pageParams, onChange: handlerSizeChange }" -->
      <a-table :columns="columns" :data-source="dataList" :loading="loading" size="small" :pagination="false">
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'entName'">
            <div class="flex">
              <a-tag>{{ record.collectName }}</a-tag>
              <p
                class="flex-1 ellipsis hoverPrimaryColor"
                @click="openCompanyInfo({ companyId: record.companyId, companyName: record.entName })"
              >
                {{ text }}
              </p>
            </div>
          </template>
          <template v-if="column.dataIndex === 'address'">
            {{ transformLocation(record) }}
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { customerList } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { transformLocation } from '@/utils/util'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { customerListRequestType } from '~/types/api/customer/list'

const columns = [
  { title: '企业名称', dataIndex: 'entName', ellipsis: true },
  // { title: '近期动态', dataIndex: 'events', width: '120px', ellipsis: true }
  { title: '地区', dataIndex: 'address', slotName: 'address', ellipsis: true, align: 'center' }
]
const params = computed<customerListRequestType>(() => ({
  collectId: '-1',
  companyName: undefined,
  isFirst: false,
  customerType: 'PERSONAL',
  isAsc: false,
  orderColumns: [
    {
      columnName: 'powerful_rank_score',
      asc: false
    }
  ]
}))
const { dataList, loading } = useListLoading(customerList, params)

const router = useRouter()
function openCompanyInfo(item: { companyId: string; companyName: string }) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.companyId,
      companyName: item.companyName
    }
  })
}
</script>

<style scoped></style>
