<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-05 11:32:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-18 10:38:35
 * @FilePath: /corp-elf-web-consumer/src/views/search/advance/components/saveConditionListModal.vue
 * @Description: 
-->
<template>
  <div class="saveConditionListModal">
    <a @click="onOpen"> 已存条件组（{{ conditionTotal }}） </a>

    <a-modal
      title="已存条件组"
      v-model:open="visible"
      width="1000px"
      @cancel="handleSaveConditionListDialogClose"
      :footer="null"
      class="saveConditionModal"
    >
      <div class="saveConditionList">
        <a-form :model="form" style="margin-bottom: 12px" :wrapper-col="{ span: 8 }">
          <a-form-item>
            <a-input v-model:value="form.name" placeholder="条件组名" size="medium" @change="handlerSearchNameChange" />
          </a-form-item>
        </a-form>

        <a-config-provider>
          <template #renderEmpty>
            <empty empty-text="没有保存的条件" />
          </template>

          <a-list
            :loading="loading"
            item-layout="vertical"
            :pagination="{ ...paginationConfig, onChange: handlerSizeChange }"
            :data-source="conditionList"
          >
            <template #renderItem="{ item }">
              <li class="conditionItem" @click="openDetailPage(item)">
                <div>
                  <h3>{{ item.name }}</h3>
                  <a-tag v-for="(searchItem, index) in item.searchContent.conditions" :key="index" size="medium" effect="plain">
                    {{ searchItem.fieldName }}
                    ：
                    <template v-if="searchItem.fieldType.includes('BOOL')">
                      {{ searchItem.conditionValue ? '是' : '否' }}
                    </template>
                    <template v-else-if="searchItem.fieldType.includes('STRING')">
                      {{ searchItem.values.join(',') }}
                    </template>
                    <template v-else-if="searchItem.fieldType.includes('ENUM')">
                      <template
                        v-if="
                          searchItem.fieldIndex === 'annual_revenue' ||
                          searchItem.fieldIndex === 'financing_info' ||
                          searchItem.fieldIndex === 'annual_revenue' ||
                          searchItem.fieldIndex === 'patent_types'
                        "
                      >
                        {{ getEnumData(searchItem) }}
                      </template>
                      <template v-else> {{ searchItem.values.join(',') }}</template>
                    </template>
                    <template v-else-if="searchItem.fieldType.includes('DATE')">
                      {{ searchItem.dates.join('~') }}
                    </template>
                    <template v-else-if="searchItem.fieldType.includes('NUM')">
                      {{ searchItem.nums.join('~') }}
                      <!-- map(item => item * searchItem.unitValue). -->
                      {{ searchItem.unitName }}
                    </template>
                    <template v-else-if="searchItem.fieldType.includes('ID')">
                      {{ searchItem.values }}
                    </template>
                  </a-tag>
                </div>
                <!-- <delete-outlined  /> -->
                <iconfontIcon icon="icon-delete" @click.stop="deleteSearch(item)" class="hoverPrimaryColor" />
              </li>
            </template>
          </a-list>
        </a-config-provider>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { companyRemoveSearchCondition, companyShowSearchConditionV2 } from '@/api/api'
//  companySearchEnums,
import iconfontIcon from '@/components/tools/iconfontIcon'
import { randomUUID } from '@/utils/util'
import { message, Modal } from 'ant-design-vue'
import { debounce } from 'lodash-es'
import { h, defineComponent } from 'vue'
import empty from '@/components/empty/index.vue'

export default defineComponent({
  name: 'SaveConditionListDialog',
  components: { iconfontIcon, empty },
  data() {
    return {
      visible: false,
      loading: false,
      conditionList: [],
      // searchEnumsList: [],
      form: {
        name: undefined
      },
      conditionTotal: 0,
      paginationConfig: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        resTotal: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        showTotal: total => `共 ${total} 条`,
        // hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
        responsive: true // 当 size 未指定时，根据屏幕宽度自动调整尺寸
      }
    }
  },
  computed: {
    
    getEnumData() {
      return e => {
        const { value: enumValue } = this.searchEnumsList[e.fieldIndex]
        const temp = []
        for (let index = 0; index < e.values.length; index++) {
          const element = e.values[index]
          // const ENUM.value.filter((item) => item.value === element)
          for (let enumIndex = 0; enumIndex < enumValue.length; enumIndex++) {
            const enumItem = enumValue[enumIndex]
            if (enumItem.value === element) {
              temp.push(enumItem.label)
            }
          }
        }
        return temp.join()
      }
    }
  },
  created() {
    this.getConditionListTotal()
    // this.getCompanySearchEnums()
  },
  methods: {
    onOpen() {
      this.visible = true
      this.paginationConfig.current = 1

      this.getSearchConditionList()
    },
    // // 获取枚举类型
    // async getCompanySearchEnums() {
    //   try {
    //     const { result } = await companySearchEnums({})
    //     this.searchEnumsList = result
    //   } catch (error) {
    //     console.error(error)
    //   }
    // },
    // 获取保存的搜索条件列表
    async getSearchConditionList() {
      try {
        this.loading = true
        console.log('this.form: ', this.form)
        const { result } = await companyShowSearchConditionV2({
          pageSize: this.paginationConfig.pageSize,
          pageNo: this.paginationConfig.current,
          ...this.form
        })
        const { records = [], total = 0 } = result
        this.conditionList = records
        this.paginationConfig.total = total
        console.log('this.conditionList: ', this.conditionList)
        this.loading = false
      } catch (error) {
        this.loading = false
        console.error(error)
      }
    },
    // 获取总数
    async getConditionListTotal() {
      try {
        const { result } = await companyShowSearchConditionV2.call(this, {})
        const { total = 0 } = result
        this.conditionTotal = total
      } catch (error) {
        console.error(error)
      }
    },
    // 删除保存搜索项
    deleteSearch(item) {
      Modal.confirm({
        title: '提示',
        content: '是否删除该条件组？',
        icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
        autoFocusButton: null,
        onOk: () => {
          companyRemoveSearchCondition({ ids: [item.id] })
            .then(({ message: msg }) => {
              message.success(msg)

              // 判断当前页是否是最后一页 且 为最后一条数据
              const lastPage = Math.ceil(this.paginationConfig.total / this.paginationConfig.pageSize)
              if (
                this.paginationConfig.current > 1 &&
                this.paginationConfig.current === lastPage &&
                this.paginationConfig.total % this.paginationConfig.pageSize === 1
              ) {
                this.paginationConfig.current--
              }

              this.getSearchConditionList()
              this.getConditionListTotal()
            })
            .catch(err => {
              console.error(err)
            })
        }
      })
    },
    // 关闭保存搜索条件dialog
    handleSaveConditionListDialogClose() {
      this.paginationConfig.current = 1
      this.form.name = undefined
      this.visible = false
    },
    // // 改变分页大小
    // handleSizeChange(val) {
    //   this.paginationConfig.pageSize = val
    //   this.getSearchConditionList()
    // },
    // // 改变分页页数
    // handleCurrentChange(val) {
    //   this.paginationConfig.current = val
    //   this.getSearchConditionList()
    // },
    // 分页变动
    handlerSizeChange(current: number, pageSize: number) {
      this.paginationConfig.current = current
      this.paginationConfig.pageSize = pageSize
      this.getSearchConditionList()
    },
    // 点击保存的搜索条件
    openDetailPage(condition) {
      console.log('condition: ', condition)
      this.$emit('searchCompany', {
        ...condition.searchContent,
        conditions: condition.searchContent.conditions.map(item => ({ ...item, uuid: randomUUID() }))
      })

      this.visible = false
    },
    handlerSearchNameChange: debounce(function () {
      this.paginationConfig.current = 1
      this.getSearchConditionList()
    }, 600)
  }
})
</script>

<style lang="less" scoped>
.saveConditionListModal {
  display: inline-block;
}

.saveConditionList {
  .conditionItem {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    div {
      line-height: 2;
      flex: 1;
    }

    .iconfontIcon {
      visibility: hidden;
    }

    &:hover {
      .iconfontIcon {
        visibility: inherit;
      }
    }
  }

  .ant-tag {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
