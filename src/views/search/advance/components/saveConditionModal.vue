<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-05 11:32:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-12 16:08:44
 * @FilePath: /corp-elf-web-consumer/src/views/search/advance/components/saveConditionModal.vue
 * @Description: 
-->
<template>
  <a-modal
    title="保存条件"
    v-model:open="visible"
    width="450px"
    :confirmLoading="loading"
    @cancel="handleSaveConditionDialogClose"
    @ok="saveConditionDialogSubmit"
  >
    <a-form ref="saveConditionFormForm" :model="saveConditionForm" :rules="rules">
      <a-form-item label="条件组名" name="name">
        <a-input v-model:value="saveConditionForm.name" placeholder="条件组名" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
import { companyCreateSearchCondition } from '@/api/api'
import { message } from 'ant-design-vue'
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SaveConditionDialog',
  data() {
    return {
      // 保存条件
      visible: false,
      loading: false,
      saveConditionForm: {
        name: undefined,
        searchContent: {}
      },
      rules: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    onOpen(searchData) {
      console.log('searchData: ', searchData)
      this.saveConditionForm.searchContent = searchData
      this.visible = true
    },
    // 保存搜索条件
    async saveConditionDialogSubmit() {
      try {
        const valid = await this.$refs.saveConditionFormForm.validate()

        console.log('valid: ', valid)
        console.log('this.saveConditionForm: ', this.saveConditionForm)
        this.loading = true
        companyCreateSearchCondition({ ...this.saveConditionForm, version: 'V_2_0' })
          .then(({ result }) => {
            console.log('result: ', result)
            this.loading = false
            message.success('保存成功')
            this.handleSaveConditionDialogClose()
            this.$emit('refreshConditionList')
          })
          .catch(err => {
            console.error(err)
            this.loading = false
            message.error('保存失败')
          })
      } catch (error) {
        console.error(error)
      }
    },
    // 关闭保存搜索条件dialog
    handleSaveConditionDialogClose() {
      this.$refs.saveConditionFormForm.resetFields()
      this.saveConditionForm.name = undefined
      this.saveConditionForm.searchContent = {}
      this.visible = false
    }
  }
})
</script>

<style lang="less" scoped></style>
