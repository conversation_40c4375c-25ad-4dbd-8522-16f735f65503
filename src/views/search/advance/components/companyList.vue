<template>
  <div>
    <Table
      title="企业列表"
      :dataSource="listData"
      :columns="tableColumns"
      :loading="loading"
      :pagination="paginationParams"
      :isHaveCheckBox="false"
      v-model:sort="sortType"
      :sort-config="[
        { label: '营收规模', value: 'annual_revenue' },
        { label: '实力指数', value: 'powerful_rank_score' },
        { label: '综合排序', value: 'isIntegratedSort' }
      ]"
      @selectedRowsChange="handlerSelectedRowsChange"
    >
      <template #extra>
        <div style="height: 32px; line-height: 32px">
          <filterForm v-model:value="filterFormState" :config="filterFormConfig" @ok="handlerFilterFun" @reset="handlerFilterReset" />
        </div>
      </template>
      <template #actions="rowItem">
        <a-button
          v-if="!(rowItem.record.companyLabels || []).includes('已关注')"
          type="link"
          @click="addFollowModalRef?.show({ companyName: rowItem.record.entName, companyId: rowItem.record.cid })"
        >
          关注
        </a-button>
      </template>
    </Table>
    <addFollowModal ref="addFollowModalRef" @refresh="getList" />
  </div>
</template>

<script setup lang="ts" name="advanced-search-companyList">
import { companySearchV2 } from '@/api/api'
import { ref, nextTick, computed, watch, getCurrentInstance, ComponentInternalInstance } from 'vue'
import { cloneDeep, has } from 'lodash-es'
import { companyCardType } from '~/types/common/companyCardType'
import { searchFormItemType } from '~/types/common/searchForm'
import filterForm, { filterFromProps } from '@/components/filterForm/index.vue'
import { useTransformCascaderData } from '@/hooks/useTransformCascaderData'
import industryClassificationOptions from '@/assets/json/industryClassification.json'
import provincesAndRegionsOptions from '@/assets/json/provincesAndRegions.json'
import Table from '@comp/table/index'
import annualRevenueMap from '@/assets/json/annualRevenues.json'
import addFollowModal from '@comp/addFollowModal/index.vue'
import { CompanySearchResType } from '~/types/common/companySearch'
import useRequest from '@/hooks/useRequest'
import { CascaderProps } from 'ant-design-vue'
import { useSearchField } from '@/store'

const searchFieldStore = useSearchField()
const addFollowModalRef = ref<InstanceType<typeof addFollowModal>>()
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const loading = ref(false)
const tableColumns = [
  { title: '企业', slotName: 'companyCard' },
  { title: '所属地区', dataIndex: 'address', slotName: 'address', width: '13%', ellipsis: true },
  { title: '成立日期', dataIndex: 'startDate', width: '13%', ellipsis: true },
  { title: '营收规模', dataIndex: 'annualRevenue', slotName: 'annualRevenue', width: '13%', ellipsis: true },
  { title: '实力指数', dataIndex: 'powerfulRankScore', width: '8%', ellipsis: true },
  { title: '', slotName: 'actions', width: '5%', ellipsis: true }
]
const listData = ref<CompanySearchResType[]>([])
//  分页参数
const paginationParams = ref({
  current: 1, // 当前页数
  pageSize: 30, // 每页显示条目个数
  total: 0, // 总条目数
  resTotal: 0, // 总条目数
  showSizeChanger: true, // 显示pagesize修改
  showQuickJumper: false, // 显示快速跳转
  hideOnSinglePage: true, // 只有一页时是否隐藏
  pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
  responsive: true, // 当 size 未指定时，根据屏幕宽度自动调整尺寸
  showTotal: (total: number) => `共 ${total} 条`,
  onChange: handlerSizeChange
})

const props = defineProps<{
  searchForm: {
    conditions: Array<searchFormItemType>
    isAnd: boolean
  }
}>()

// 获取推荐企业
async function getList() {
  try {
    loading.value = true

    const { result } = await companySearchV2({
      ...props.searchForm,
      ...filterParams.value,
      pageNo: paginationParams.value.current,
      pageSize: paginationParams.value.pageSize,
      isIntegratedSort: sortType.value === 'isIntegratedSort',
      orderColumns: [
        {
          columnName: sortType.value === 'isIntegratedSort' ? 'powerful_rank_score' : sortType.value,
          asc: false
        }
      ]
    })

    const { total = 0, data = [] } = result
    listData.value = data
    paginationParams.value.total = total > 10000 ? 10000 : total
    paginationParams.value.resTotal = total
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

// 分页变动
function handlerSizeChange(current: number, pageSize: number) {
  paginationParams.value.current = current
  paginationParams.value.pageSize = pageSize
  getList()
  // 翻页时列表回到顶部
  const top = $(proxy?.$el).position().top
  $('body, html').animate({ scrollTop: top - 64 }, 300) // 64是顶部导航栏的高度
  console.log('top: ', top)
}

// // const receiveAssignRef = ref()
// const isHaveCheckBox = ref(true)
// // 更多按钮回调
// const menuList = computed(() => {
//   const baseMenu: Array<{ title: string; key: string }> = []
//   baseMenu.unshift({ title: '领取', key: 'receive' })
//   return baseMenu
// })
// function handlerMoreIconClick(menuItem, rowItem) {
//   switch (menuItem.key) {
//     // case 'receive':
//     //   receiveAssignRef.value.batchReceive([rowItem.record])
//     //   break
//     // case 'assign':
//     //   receiveAssignRef.value.batchAssign([rowItem.record])
//     //   break
//     case 'noLongerRecommended':
//       break

//     default:
//       break
//   }
// }

// 选中企业相关
const checkCompanyList = ref<companyCardType[]>([])
function handlerSelectedRowsChange(_keys: string, rows: companyCardType[]) {
  checkCompanyList.value = rows
}

// 重置企业列表
function resetCompanyList() {
  loading.value = true
  listData.value = []
  checkCompanyList.value = []
  // companyCheckList.value = []
  paginationParams.value.current = 1
  paginationParams.value.total = 0
  loading.value = false
}
// 对外暴露搜索方法
function handlerSearch() {
  nextTick(() => {
    paginationParams.value.current = 1
    paginationParams.value.pageSize = 30
    getList()
  })
}

// 排序方法
const sortType = ref<'powerful_rank_score' | 'annual_revenue' | 'isIntegratedSort'>('isIntegratedSort')
watch(sortType, () => {
  if (props.searchForm.conditions.length === 0) {
    return
  }

  paginationParams.value.current = 1
  getList()
})
// function handlerSortTypeChange(_sortType) {
//   sortType.value = _sortType
//   getList()
// }

const filterFormConfig: filterFromProps['config'] = [
  {
    label: '企业名称',
    key: 'searchContent',
    formItemType: 'input',
    placeholder: '工商名称'
  },
  // {
  //   label: '商瞳行业',
  //   key: 'tags',
  //   formItemType: 'cascader',
  //   mode: 'multiple',
  //   optionsData: industryClassListV2({})
  //     .then(({ result }) => {
  //       return result
  //     })
  //     .catch(err => {
  //       console.error(err)
  //       return []
  //     })
  // },
  {
    label: '工商行业',
    key: 'industryClassification',
    formItemType: 'cascader',
    optionsData: industryClassificationOptions,
    mode: 'multiple'
  },
  {
    label: '所属地区',
    key: 'provincesAndRegions',
    formItemType: 'cascader',
    optionsData: provincesAndRegionsOptions,
    mode: 'multiple',
    placeholder: '省/市/区'
  },
  {
    label: '成立年限',
    key: 'establishmentTime',
    formItemType: 'rangeInputNumber',
    max: Infinity,
    placeholder: ['开始年限', '结束年限']
  },
  {
    label: '实力指数',
    key: 'rankPowerfulScore',
    formItemType: 'rangeInputNumber',
    max: Infinity,
    placeholder: ['最低分', '最高分']
  },
  {
    label: '营收规模',
    key: 'annualRevenues',
    formItemType: 'select',
    mode: 'multiple',
    optionsData: annualRevenueMap
  },
  {
    label: '人员规模',
    key: 'staffSizes',
    formItemType: 'select',
    mode: 'multiple',
    optionsData: [
      { label: '小于50人', value: '小于50人' },
      { label: '50-99人', value: '50-99人' },
      { label: '100-499人', value: '100-499人' },
      { label: '500-999人', value: '500-999人' },
      { label: '1000-4999人', value: '1000-4999人' },
      { label: '5000-9999人', value: '5000-9999人' },
      { label: '1万人以上', value: '1万人以上' }
    ]
  }
  // {
  //   label: '客户范围',
  //   key: 'companyRange',
  //   formItemType: 'select',
  //   optionsData: [
  //     { label: '全部', value: 'ALL_COMPANY' },
  //     { label: '未领取', value: 'NON_CUSTOMER_COMPANY' },
  //     { label: '我的客户', value: 'MINE_CUSTOMER_COMPANY' },
  //     { label: '企业客户', value: 'ALL_CUSTOMER_COMPANY' }
  //   ]
  // }
]
const filterFormState = ref<Record<string, any>>({ companyRange: 'ALL_COMPANY' }) // 筛选默认值
// 筛选确定回掉
function handlerFilterFun() {
  if (props.searchForm.conditions.length === 0) {
    return
  }
  paginationParams.value.current = 1
  getList()
}
// 筛选重置回掉
function handlerFilterReset() {
  filterFormState.value = { companyRange: 'ALL_COMPANY' }
  if (props.searchForm.conditions.length === 0) {
    return
  }
  paginationParams.value.current = 1
  getList()
}
// 准备筛选器的参数
const filterParams = computed(() => {
  const _filterFormState = cloneDeep(filterFormState.value)

  let returnData = {
    ..._filterFormState
    // ...establishmentTime,
    // ...rankPowerfulScore,
    // tags,
    // industryClassification,
    // provincesAndRegions
  }

  let establishmentTime = {}
  if (has(_filterFormState, 'establishmentTime')) {
    establishmentTime = {
      minEstablishmentTime: _filterFormState.establishmentTime[0] || null,
      maxEstablishmentTime: _filterFormState.establishmentTime[1] || null
    }
    delete returnData.establishmentTime
    returnData = {
      ...returnData,
      ...establishmentTime
    }
  }

  let rankPowerfulScore = {}
  if (has(_filterFormState, 'rankPowerfulScore')) {
    rankPowerfulScore = {
      minRankPowerfulScore: _filterFormState.rankPowerfulScore[0] || null,
      maxRankPowerfulScore: _filterFormState.rankPowerfulScore[1] || null
    }
    delete returnData.rankPowerfulScore
    returnData = {
      ...returnData,
      ...rankPowerfulScore
    }
  }

  let tags
  if (has(_filterFormState, 'tags')) {
    tags = useTransformCascaderData(_filterFormState.tags, searchFieldStore.industryList as unknown as CascaderProps['options']).map(
      item => item.label
    )
    returnData = {
      ...returnData,
      tags
    }
  }

  let industryClassification
  if (has(_filterFormState, 'industryClassification')) {
    industryClassification = useTransformCascaderData(_filterFormState.industryClassification, industryClassificationOptions).map(
      item => item.value
    )
    returnData = {
      ...returnData,
      ...{ industryClassification }
    }
  }

  let provincesAndRegions
  if (has(_filterFormState, 'provincesAndRegions')) {
    provincesAndRegions = useTransformCascaderData(_filterFormState.provincesAndRegions, provincesAndRegionsOptions).map(item => item.value)
    returnData = {
      ...returnData,
      ...{ provincesAndRegions }
    }
  }

  console.log('returnData: ', returnData)
  return returnData
})

defineExpose({
  handlerSearch,
  resetCompanyList
})
</script>

<style lang="less" scoped>
.companyList {
  .options {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .company_list {
    // :deep(.company_item) {
    //   padding: 16px 0px;
    // }
    :deep(.ant-list-pagination) {
      margin: 16px 0;
      padding: 0 16px;
    }
    .record {
      padding: 16px 0 0 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>
