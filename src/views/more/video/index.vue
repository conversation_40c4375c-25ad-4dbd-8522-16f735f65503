<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-04-15 16:22:29
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-25 14:43:12
 * @FilePath: /corp-elf-web-consumer/src/views/more/video/index.vue
 * @Description: 
-->
<template>
  <div class="methodologyVideo">
    <a-card :border="false">
      <a-row :gutter="[16, 16]">
        <a-col :span="24">
          <a-typography-title :level="3">{{ videoData?.title }}</a-typography-title>
          <a-typography-text type="secondary" class="fs-16px">
            {{ dayjs(videoData?.createTime).format('YYYY-MM-DD HH:mm') }}
          </a-typography-text>
        </a-col>

        <a-col :span="16">
          <div class="bg-#ededed h-720px relative overflow-hidden border-radius-8 flex items-center justify-center mb-16px">
            <div id="videoContainer"></div>
          </div>

          <div class="videoContent color-#7F7F7F fs-16px white-space-pre-line">
            {{ videoData?.content }}
          </div>
        </a-col>

        <a-col :span="8">
          <div class="mb12px">
            <a-typography-text type="secondary" class="fs-16px">更多视频</a-typography-text>
          </div>
          <a-list :loading="moreListLoading" item-layout="horizontal" :data-source="moreData">
            <template #renderItem="{ item }">
              <div class="moreItem cursor-pointer flex items-center" @click="handlerMoreVideoClick(item)">
                <div class="poster bg-#000 h80px w80px relative overflow-hidden border-radius-8 flex items-center justify-center">
                  <img :src="item.coverImageUrl" class="posterImg" />
                  <PlayCircleFilled class="playBtn color-white transition-all-500 fs-36px absolute" />
                  <span class="videoTime color-white fs-12px absolute l-4 b-0">
                    {{ dayjs.duration(item.videoTime, 's').format('mm:ss') }}
                  </span>
                </div>
                <div class="textContent ml-16px flex-1">
                  <!-- <p class="title">{{ item.title }}</p>
                <span class="content">{{ item.content }}</span> -->
                  <a-typography-title class="hoverPrimaryColor" :level="5">{{ item.title }}</a-typography-title>
                  <div class="content color-#7F7F7F ellipsis-2 fs-14px">
                    {{ item.content }}
                  </div>
                </div>
              </div>
            </template>
            <template #loadMore>
              <div v-if="moreData.length > 0" class="loadMore  py-8px" v-intersection-observer="handlerIntersectionObserver">
                <p class="endText" v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                <p class="endText" v-else>没有更多了</p>
              </div>
            </template>
          </a-list>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { marketingVideoGetById } from '@/api/api'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MarketingVideo } from '~/types/api/marketingVideo/common'
import { marketingVideoPage } from '@/api/api'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { useThemeStore } from '@/store'
import { PlayCircleFilled } from '@ant-design/icons-vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { vIntersectionObserver } from '@vueuse/components'

dayjs.extend(duration) // 启用dayjs的时长功能

const router = useRouter()
const route = useRoute()
const { id: videoId } = route.query

const themeStore = useThemeStore()

const loading = ref(false)
const videoData = ref<MarketingVideo>()
async function getVideoDetail() {
  try {
    loading.value = true
    const { result } = await marketingVideoGetById({ id: videoId as string })
    videoData.value = result
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

const moreParams = ref({})
const {
  dataList: moreData,
  loading: moreListLoading,
  noMore,
  onLoadMore
} = useInfiniteLoading(marketingVideoPage, moreParams)

let player: Player | null = null
function initPlayer() {
  player = new Player({
    id: 'videoContainer',
    url: videoData.value?.videoUrl,
    height: '100%',
    width: '100%',
    autoplay: true,
    playsinline: true,
    plugins: [],
    commonStyle: {
      // 进度条底色
      // progressColor: '',
      // 播放完成部分进度条底色
      playedColor: themeStore.getColorPrimary,
      // 进度条滑块样式
      sliderBtnStyle: {
        background: 'rgb(255 255 255 / 30%)'
      },
      // 音量颜色
      volumeColor: themeStore.getColorPrimary
    }
  })
}

/**
 * @description: 滚动到界面底部回调方法
 * @param {*} intersectionObserverList
 * @return {*}
 */
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !moreListLoading.value && !noMore.value) {
    onLoadMore()
  }
}

function handlerMoreVideoClick(item: MarketingVideo) {
  router.replace({ path: '/more/video', query: { id: item.id, t: new Date().valueOf() } })
  // window.open(`/methodology/video?id=${item.id}`, '_blank')
}

onMounted(async () => {
  await getVideoDetail()
  initPlayer()
})

onUnmounted(() => {
  player?.destroy()
})
</script>

<style lang="less">
.methodologyVideo {
  position: relative;
  min-height: calc(100vh - 64px - 32px);

  .moreItem {
    + .moreItem {
      margin-top: 16px;
    }

    .poster {
      &:hover {
        .posterImg {
          transform: scale(1.04);
        }
        .playBtn {
          opacity: 0.7;
        }
      }

      .posterImg {
        pointer-events: none;
        transition: all 0.5s ease 0.1s;
      }
      .playBtn {
        opacity: 0;
        top: calc(50% - 18px);
        left: calc(50% - 18px);
      }
    }
  }
}

.xgplayer .xg-options-list li {
  height: auto !important;
  line-height: normal !important;

  &:hover,
  &.selected {
    color: var(--g-primary-color) !important;
  }
}
</style>
