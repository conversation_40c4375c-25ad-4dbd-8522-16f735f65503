<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-26 15:06:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-05-14 14:17:51
 * @FilePath: /corp-elf-web-consumer/src/views/more/overview/index.vue
 * @Description: 帮助
-->
<template>
  <div class="methodology">
    <a-card :border="false">
      <div class="pt-24px">
        <Methodology :is-show-link-button="true" />
      </div>
    </a-card>

    <a-spin :spinning="loading">
      <a-row :gutter="[16, 16]" class="mt32px">
        <a-col v-for="item in dataList" :key="item.id" :span="6">
          <VideoItem :video-item="item" :key="item.id" />
        </a-col>
        <a-col :span="24" class="flex items-end mb48px">
          <a-space wrap :size="36">
            <div class="text-center">
              <a-image :width="140" :preview="false" :src="shipinghao" />
              <p>视频号</p>
            </div>
            <div class="text-center">
              <a-image :width="140" :preview="false" :src="gonzhonhao" />
              <p>公众号</p>
            </div>
            <div class="text-center">
              <a-image :width="140" :preview="false" :src="zhihu" />
              <p>知乎</p>
            </div>
            <div class="text-center">
              <a-image :width="140" :preview="false" :src="xiaohonshu" />
              <p>小红书</p>
            </div>
          </a-space>
        </a-col>
      </a-row>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { marketingVideoPage } from '@/api/api'
import Methodology from '@/components/methodologyModal/methodology.vue'
import useListLoading from '@/hooks/useListLoading'
import { ref } from 'vue'
import VideoItem from './components/videoItem.vue'
import shipinghao from '@/assets/images/qrcode/视频号.png'
import gonzhonhao from '@/assets/images/qrcode/公众号.png'
import zhihu from '@/assets/images/qrcode/知乎.png'
import xiaohonshu from '@/assets/images/qrcode/小红书.png'

const params = ref({})
const { dataList, loading } = useListLoading(marketingVideoPage, params, {
  pageParams: { pageSize: 1000 }
})
</script>

<style scoped>
.methodology {
  position: relative;
  min-height: calc(100vh - 64px - 32px);
}
</style>
