<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-04-15 14:21:18
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-25 14:43:02
 * @FilePath: /corp-elf-web-consumer/src/views/more/overview/components/videoItem.vue
 * @Description: 
-->
<template>
  <div class="videoItem cursor-pointer" @click="openVideoModal">
    <div class="poster bg-#000 relative overflow-hidden border-radius-8 h406px flex items-center justify-center">
      <img :src="props.videoItem.coverImageUrl" class="posterImg" />
      <PlayCircleFilled class="playBtn color-white transition-all-500 fs-50px absolute" />
      <span class="videoTime color-white absolute r-8 b-8">{{ dayjs.duration(props.videoItem.videoTime, 's').format('mm:ss') }}</span>
    </div>
    <div class="contentText mt16px">
      <a-typography-title class="hoverPrimaryColor" :level="5">{{ props.videoItem.title }}</a-typography-title>
      <div class="content color-#7F7F7F ellipsis-2 fs-14px">
        {{ props.videoItem.content }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MarketingVideo } from '~/types/api/marketingVideo/common'
import { PlayCircleFilled } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)

const props = defineProps<{ videoItem: MarketingVideo }>()

function openVideoModal() {
  window.open(`/more/video?id=${props.videoItem.id}&t=${new Date().valueOf()}`, '_blank')
}
</script>

<style lang="less" scoped>
.videoItem {
  .poster {
    &:hover {
      .posterImg {
        transform: scale(1.04);
      }
      .playBtn {
        opacity: 0.7;
      }
    }

    .posterImg {
      pointer-events: none;
      transition: all 0.5s ease 0.1s;
    }
    .playBtn {
      opacity: 0;
      top: calc(50% - 25px);
      left: calc(50% - 25px);
    }
  }
}
</style>
