<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-05-11 14:47:56
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-05-17 12:12:39
 * @FilePath: /corp-elf-web-consumer/src/views/dataStore/overview/index.vue
 * @Description: 
-->
<template>
  <div class="dataStore">
    <!-- <a-card :bordered="false" :bodyStyle="{ padding: '0' }">
      <template #title>
        <p :style="{ color: theme.getColorPrimary, fontSize: '16px' }">数据商店</p>
      </template>
    </a-card> -->

    <a-list
      class="list"
      item-layout="horizontal"
      :data-source="dataList"
      :loading="{ spinning: loading, tip: '加载中...' }"
      :grid="{ gutter: 16, column: 4 }"
    >
      <!-- :pagination="{ ...pageParams, size: 'small', onChange: handlerCurrentChange }" -->
      <template #loadMore>
        <div v-if="dataList.length > 0" class="loadMore  py-8px" v-intersection-observer="handlerIntersectionObserver">
          <p class="endText" v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
          <p class="endText" v-else>没有更多了</p>
        </div>
      </template>
      <template #renderItem="{ item }">
        <a-list-item class="p0!">
          <a-card :bordered="false" class="cursor-pointer hoverPrimaryColor" @click="openDetail(item)">
            <template #cover>
              <img :src="item?.cover" />
            </template>
            <a-card-meta>
              <!-- :title="item.name" -->
              <template #title>
                <!-- <a-typography-title
                  class="hoverPrimaryColor m0!"
                  :level="3"
                  :ellipsis="{ rows: 1 }"
                  :content=""
                ></a-typography-title> -->
                <span class="hoverPrimaryColor">
                  {{ item.name }}
                </span>
              </template>
              <template #description>
                <a-space class="fs-14px color-#7F7F7F">
                  <iconfontIcon icon="icon-history" :extra-common-props="{ style: { fontSize: '18px' } }" />
                  <span>{{ dayjs(isEmpty(item.updateDate) ? new Date() : item.updateDate).format('YYYY-MM') }}</span>
                </a-space>
              </template>
            </a-card-meta>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
  </div>
</template>

<script setup lang="ts">
import { datastorePage } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { vIntersectionObserver } from '@vueuse/components'
import { datastorePageResponse } from '~/types/api/datastore/pageResponse'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { isEmpty } from 'lodash-es'
import dayjs from 'dayjs'
// import useListLoading from '@/hooks/useListLoading'
import { ref } from 'vue'

const { dataList, noMore, loading, onLoadMore } = useInfiniteLoading(datastorePage, ref({}), {
  pageParams: { pageSize: 12 }
})
// 滚动到界面底部回调方法
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !loading.value && !noMore.value) {
    onLoadMore()
  }
}

// const { loading, dataList, pageParams, getData } = useListLoading<datastorePageResponse>(datastorePage)
// // 第几页改变时触发
// function handlerCurrentChange(pageNo, pageSize) {
//   pageParams.value.current = pageNo
//   pageParams.value.pageSize = pageSize
//   getData()
// }

// const router = useRouter()
function openDetail(item: datastorePageResponse) {
  window.open(`/dataStore/detail?id=${item.id}&t=${new Date().valueOf()}`, '_blank')
  // router.push({
  //   name: 'dataStore-detail',
  //   path: '/dataStore/detail',
  //   query: {
  //     id: item.id,
  //     t: new Date().valueOf()
  //   }
  // })
}
</script>

<style lang="less" scoped></style>
