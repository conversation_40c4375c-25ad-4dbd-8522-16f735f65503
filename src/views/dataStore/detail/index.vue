<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-05-11 14:47:56
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-05-29 17:52:52
 * @FilePath: /corp-elf-web-consumer/src/views/dataStore/detail/index.vue
 * @Description: 
-->
<template>
  <div class="dataStoreDetail pb16px">
    <a-card :bordered="false">
      <a-row :gutter="16">
        <a-col :span="18">
          <div ref="leftBox">
            <div class="flex items-start max-h250px">
              <a-skeleton :loading="detailLoading" active avatar>
                <a-avatar
                  shape="square"
                  :src="detailData?.cover"
                  :size="250"
                  class="bg-#ccc border-0"
                  :style="{ boxShadow: '0px 6px 10px #fafafa' }"
                >
                  <template #icon>
                    <iconfontIcon icon="icon-faxian" :extraCommonProps="{ style: { fontSize: 'inherit' }, class: 'opacity-80' }" />
                  </template>
                </a-avatar>

                <div class="flex-1 ml-16px h250px flex flex-direction-column">
                  <div class="flex-1">
                    <h1 class="ellipsis-1 fs-24px fw-600">{{ detailData?.name }}</h1>

                    <div class="mt-16px">
                      <a-typography-text type="secondary" class="fs-16px">
                        <iconfontIcon icon="icon-history" :extra-common-props="{ style: { fontSize: '18px' } }" />
                        <span class="ml8px">
                          {{ dayjs(detailData?.updateType === '0' ? new Date() : detailData?.updateDate).format('YYYY-MM') }}
                        </span>
                      </a-typography-text>
                    </div>

                    <div class="mt-16px fs-16px bg-#fef5ee border-radius-8px py-12px pl12px flex items-center">
                      价格：
                      <span class="fs-28px fw-600 color-#e28d3b">
                        {{ detailData?.priceType === '0' ? '面议' : `¥ ${detailData?.price || ''}` }}
                      </span>
                    </div>
                  </div>

                  <div class="mt-16px color-#7F7F7F">
                    <a-popover placement="rightTop">
                      <template #content>
                        <img class="w180px h180px" src="@/assets/images/qrcode/dataStoreQrCode.png" alt="建议与反馈" />
                      </template>
                      <a-button type="primary" size="large">立即咨询</a-button>
                    </a-popover>

                    <MailOutlined class="fs-16px ml8px" />
                    <span> 联系客服咨询更多详细信息</span>
                  </div>
                </div>
              </a-skeleton>
            </div>

            <p class="fs-18px fw-500 flex items-center mt-32px">
              <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
              <a-typography-text class="fs-16px ml8px">数据介绍</a-typography-text>
            </p>

            <a-skeleton :loading="detailLoading" active>
              <div id="editor-container"></div>
            </a-skeleton>
          </div>
        </a-col>

        <a-col :span="6">
          <div ref="rightBox" :style="{ height: `${recommendListHeight}px` }" class="overflow-auto">
            <div class="mb12px">
              <a-typography-text type="secondary" class="fs-16px">更多数据</a-typography-text>
            </div>

            <a-list item-layout="horizontal" :data-source="recommendDataList" :loading="{ spinning: recommendLoading, tip: '加载中...' }">
              <template #loadMore>
                <div v-if="recommendDataList.length > 0" class="loadMore py-8px" v-intersection-observer="handlerIntersectionObserver">
                  <p class="endText" v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
                  <p class="endText" v-else>没有更多了</p>
                </div>
              </template>
              <template #renderItem="{ item }">
                <div class="cursor-pointer flex items-start hoverPrimaryColor moreItem" @click="openDetail(item)">
                  <a-avatar shape="square" :src="item?.cover" :size="80" class="bg-#ccc border-0">
                    <template #icon>
                      <iconfontIcon icon="icon-faxian" :extraCommonProps="{ style: { fontSize: 'inherit' }, class: 'opacity-80' }" />
                    </template>
                  </a-avatar>
                  <div class="textContent ml-16px flex-1">
                    <a-typography-title
                      class="hoverPrimaryColor"
                      :level="5"
                      :ellipsis="{ rows: 2 }"
                      :content="item.name"
                    ></a-typography-title>
                    <a-space class="color-#7F7F7F">
                      <iconfontIcon icon="icon-history" :extra-common-props="{ style: { fontSize: '18px' } }" />
                      <span>{{ dayjs(isEmpty(item.updateDate) ? new Date() : item.updateDate).format('YYYY-MM') }}</span>
                    </a-space>
                  </div>
                </div>
              </template>
            </a-list>
          </div>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { datastoreDetailShow, datastoreDetailRecommend } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { vIntersectionObserver } from '@vueuse/components'
import { dataPackageItemType } from '~/types/api/datastore/dataPackageItem'
import { datastorePageResponse } from '~/types/api/datastore/pageResponse'
import iconfontIcon from '@/components/tools/iconfontIcon'
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { createEditor } from '@wangeditor/editor'
import { MailOutlined } from '@ant-design/icons-vue'

const route = useRoute()
const { id: dataPackageId } = route.query

const detailLoading = ref(false)
const detailData = ref<dataPackageItemType>()
/**
 * 异步获取数据包详情。
 * 更新组件内的detailData和detailLoading状态。
 */
async function getDataPackageDetail() {
  try {
    detailLoading.value = true
    const { result } = await datastoreDetailShow({ dataStoreId: dataPackageId as string })
    detailData.value = result
    detailLoading.value = false
  } catch (error) {
    detailLoading.value = false
    console.error(error)
  }
}

// 获取更多数据推荐列表
const {
  dataList: recommendDataList,
  noMore,
  loading: recommendLoading,
  onLoadMore
} = useInfiniteLoading(datastoreDetailRecommend, ref({}), { pageParams: { pageSize: 20 } })
// 滚动到界面底部回调方法
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !recommendLoading.value && !noMore.value) {
    onLoadMore()
  }
}

const router = useRouter()
function openDetail(item: datastorePageResponse) {
  router.replace({ path: '/dataStore/detail', query: { id: item.id, t: new Date().valueOf() } })
}

// const editorRef = shallowRef()
// function handleCreated(editor) {
//   // editorRef.value = editor // 记录 editor 实例，重要！
//   console.log('editor: ', editor)
// }
function initEditor() {
  const editor = createEditor({
    selector: '#editor-container',
    html: detailData.value?.description,
    config: {
      placeholder: '请输入内容...',
      readOnly: true
    },
    mode: 'simple'
  })
  editor.disable()
}

const leftBox = ref<HTMLElement>()
const recommendListHeight = ref<number>(200)
onMounted(async () => {
  await getDataPackageDetail()
  await initEditor()
  recommendListHeight.value = leftBox.value!.clientHeight
  // leftBox.value
  // rightBox
})
</script>

<style lang="less" scoped>
:deep(#editor-container) {
  .table-container {
    border-width: 0px;
    padding-left: 0;
    overflow: initial;
  }
}

.moreItem {
  + .moreItem {
    margin-top: 16px;
  }
}
</style>
