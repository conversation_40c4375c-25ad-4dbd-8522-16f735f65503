/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 14:45:12
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-09 14:05:22
 * @FilePath: /corp-elf-web-consumer/src/main.ts
 * @Description:
 */
import { createApp } from 'vue'
import App from './App.vue'
import { setupRouter } from './router'
import { setupStore } from './store'

import 'ant-design-vue/dist/reset.css'
import './theme/index.less'
// import '@unocss/reset/tailwind-compat.css'
import 'virtual:uno.css'

// dayjs
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')

// mockjs
import './mock/index'

import 'animate.css'

// jq
import $ from 'jquery'
window['$'] = $

import TextClamp from 'vue3-text-clamp' // vue3版本文字截断

const app = createApp(App)

setupStore(app)
setupRouter(app)
(app)

app.use(TextClamp).mount('#app')
