/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-23 00:00:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-23 11:15:08
 * @FilePath: /corp-elf-web-consumer/src/directives/vipAuth.ts
 * @Description: 登录检查指令
 */
import { DirectiveBinding } from 'vue'
import { useUserStore } from '@/store'
import { useEventBus } from '@vueuse/core'
import { has } from 'lodash-es'
import showLoginModal from '@/components/loginModal'

// 定义指令绑定值的类型
interface vipAuthBindingValue {
  /** 绑定显示的文本 */
  text?: string
}

export const vipAuthDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<vipAuthBindingValue>) {
    const userStore = useUserStore()
    const { emit: openVipModal } = useEventBus('openVipModal')

    // 点击事件处理函数
    const handleClick = (e: Event) => {
      e.stopPropagation()
      e.preventDefault()
      if (!userStore.isLogin) {
        console.log('请登录')
        showLoginModal()
        return
      }
      if (!userStore.isVip) {
        // TODO: 这里可以添加打开VIP弹窗的逻辑
        console.log('请开通VIP')
        openVipModal()
      }
    }

    if (!userStore.isVip) {
      el.innerText = has(binding.value, 'text') ? binding.value.text! : el.innerText
      el.addEventListener('click', handleClick, true)
      // 将事件处理函数存储在元素上，以便后续卸载
      ;(el as any)._vipClickHandler = handleClick
    }
  },
  unmounted(el: HTMLElement) {
    // 卸载时移除事件监听
    if ((el as any)._vipClickHandler) {
      el.removeEventListener('click', (el as any)._vipClickHandler, true)
      delete (el as any)._vipClickHandler
    }
  }
}
