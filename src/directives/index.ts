/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-23 00:00:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-23 11:11:45
 * @FilePath: /corp-elf-web-consumer/src/directives/index.ts
 * @Description: 全局指令注册
 */
import type { App } from 'vue'
import { vipAuthDirective } from './vipAuth'
import { loginAuthDirective } from './loginAuth'

/**
 * 注册全局指令
 * @param app Vue应用实例
 */
export function setupDirectives(app: App) {
  // 注册会员检查指令
  app.directive('vipAuth', vipAuthDirective)
  // 注册登录检查指令
  app.directive('loginAuth', loginAuthDirective)
}

export default setupDirectives
