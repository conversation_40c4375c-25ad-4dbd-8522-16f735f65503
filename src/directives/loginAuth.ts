/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-23 00:00:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-23 11:14:51
 * @FilePath: /corp-elf-web-consumer/src/directives/loginAuth.ts
 * @Description: 登录检查指令
 */
import { DirectiveBinding } from 'vue'
import { useUserStore } from '@/store'
import { has } from 'lodash-es'
import showLoginModal from '@/components/loginModal'

// 定义指令绑定值的类型
interface LoginAuthBindingValue {
  /** 绑定显示的文本 */
  text?: string
}

export const loginAuthDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<LoginAuthBindingValue>) {
    const userStore = useUserStore()

    // 点击事件处理函数
    const handleClick = (e: Event) => {
      e.stopPropagation()
      e.preventDefault()
      if (!userStore.isLogin) {
        console.log('请登录')
        showLoginModal()
        return
      }
    }

    if (!userStore.isVip) {
      el.innerText = has(binding.value, 'text') ? binding.value.text! : el.innerText
      el.addEventListener('click', handleClick, true)
      // 将事件处理函数存储在元素上，以便后续卸载
      ;(el as any)._loginClickHandler = handleClick
    }
  },
  unmounted(el: HTMLElement) {
    // 卸载时移除事件监听
    if ((el as any)._loginClickHandler) {
      el.removeEventListener('click', (el as any)._loginClickHandler, true)
      delete (el as any)._loginClickHandler
    }
  }
}
