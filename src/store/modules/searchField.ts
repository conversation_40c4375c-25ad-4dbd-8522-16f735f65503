/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-27 16:44:18
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 17:19:26
 * @FilePath: /corp-elf-web-consumer/src/store/modules/searchField.ts
 * @Description:
 */
import { companySearchCondition, companySearchEnums, companySearchField, companySearchFieldAffix, industryClassListV2 } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import { defineStore } from 'pinia'

export const useSearchField = defineStore('searchField', () => {
  const { dataList: searchConditionList } = useRequest(companySearchCondition)
  const { dataList: searchEnumsList } = useRequest(companySearchEnums)
  const { dataList: fieldList } = useRequest(companySearchField)
  const { dataList: fieldAffix } = useRequest(companySearchFieldAffix)
  const { dataList: industryList } = useRequest(industryClassListV2)

  return {
    searchConditionList,
    searchEnumsList,
    fieldList,
    fieldAffix,
    industryList
  }
})
