import { theme } from 'ant-design-vue'
import { defineStore } from 'pinia'

export const useThemeStore = defineStore('theme', {
  state: () => {
    return {
      theme: <'light' | 'dark'>'light',
      config: {
        colorPrimary: '#6553ee',
        colorInfo: '#6553ee'
      }
    }
  },
  getters: {
    getThemeToken: () => {
      const { useToken } = theme
      const { token: themeToken } = useToken()
      return themeToken
    },
    getThemeMode: state => {
      return state.theme
    },
    getConfig: state => {
      return state.config
    },
    getColorPrimary: state => {
      return state.config.colorPrimary
    }
  },
  actions: {
    setThemeMode(mode: 'light' | 'dark') {
      this.theme = mode
    },
    setThemeColorPrimary(color: string) {
      this.config.colorPrimary = color
      this.config.colorInfo = color
      document.documentElement.style.setProperty('--g-primary-color', color)
    }
  }
})
