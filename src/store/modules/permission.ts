/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 16:07:44
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 14:31:18
 * @FilePath: /corp-elf-web-consumer/src/store/modules/permission.ts
 * @Description:
 */
import { defineStore } from 'pinia'
import { basicRouterMap } from '@/router/routes'
import { generateIndexRouter } from '@/utils/util'
import { RouteRecordRaw } from 'vue-router'

export const usePermissionStore = defineStore('permission', {
  state: () => {
    return {
      routers: basicRouterMap,
      addRouters: <RouteRecordRaw[]>[],
      permissionList: <RouteRecordRaw[]>[],
      activationSecondRouter: <RouteRecordRaw>{}
    }
  },
  getters: {
    getPermissionList: state => state.permissionList,
    getActivationSecondRouter: state => state.activationSecondRouter
  },
  actions: {
    // 动态添加主界面路由，需要缓存
    async updateRouterList(addRouter: RouteRecordRaw[]): Promise<RouteRecordRaw[]> {
      try {
        this.permissionList = addRouter // 设置路由
        console.log('addRouter: ', addRouter);
        const asyncRoutes = generateIndexRouter(addRouter)
        this.addRouters = asyncRoutes
        this.routers = basicRouterMap.concat(asyncRoutes)
        return asyncRoutes
      } catch (error) {
        console.error(error)
        throw error
      }
    }
    // setActivationSecondRouter(router) {
    //   this.activationSecondRouter = router
    // }
  }
})
