/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 10:31:36
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 11:42:34
 * @FilePath: /corp-elf-web-consumer/src/store/modules/user.ts
 * @Description:
 */
import { removeLocal, setLocal } from '@/utils/storage'
import { defineStore } from 'pinia'
import { queryPermissionsByUser, userMemberInfo } from '@/api/api'
import { usePermissionStore } from '@/store'
import { getToken, removeToken, setToken } from '@/utils/auth/token'
import { asyncRouter as NON_AUTH_ROUTE } from '@/router/routes'
import { isEmpty } from 'lodash-es'
import { loginBaseResponseType } from '~/types/common/loginUser'
import { userMemberInfoResType } from '~/types/api/userMember/info'

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      token: '',
      username: '',
      realName: '',
      // avatar: '',
      userInfo: <loginBaseResponseType['userVo']>{},
      memberInfo: <userMemberInfoResType>{}
    }
  },
  getters: {
    /** 判断用户是否已登录 */
    isLogin: state => !!state.token,
    /** 获取用户token */
    getToken: state => state.token,
    /** 获取用户名 */
    getUsername: state => state.username,
    /** 获取用户昵称 */
    getNickname: state => state.realName,
    /** 获取用户信息 */
    getUserInfo: state => state.userInfo,
    /** 判断用户是否是会员 */
    isVip: state => state.memberInfo.isMember,
    /** 获取会员信息 */
    getMemberInfo: state => state.memberInfo
  },
  actions: {
    removeToken() {
      this.token = ''
    },
    setToken(token: string) {
      this.token = token
    },
    setUserInfo(userInfo: loginBaseResponseType['userVo']) {
      this.username = userInfo.username
      this.realName = userInfo.realName
      this.userInfo = userInfo
    },
    /**
     * @description: 登录成功，设置用户信息和token
     * @param {loginBaseResponseType} params
     * @return {*}
     */
    loginSuccess(params: loginBaseResponseType): void {
      const { token, userVo: userInfo } = params
      this.setUserInfo(userInfo)
      this.setToken(token)
      setToken(this.token)
      setLocal('userInfo', this.userInfo)
      window.location.replace('/')
    },
    // 获取会员信息
    async getUserMemberInfo() {
      try {
        const token = this.token || getToken()
        if (!isEmpty(token)) {
          const { result } = await userMemberInfo()
          this.memberInfo = result
        }
      } catch (error) {
        console.error(error)
      }
    },
    // 获取权限点
    async fetchPermissionList() {
      try {
        const permissionStore = usePermissionStore()
        // const token = this.token || getToken()
        let asyncRouter = NON_AUTH_ROUTE
        // if (!isEmpty(token)) {
        //   const { result } = await queryPermissionsByUser({ token: token as string })
        //   const { menu } = result
        //   // 只有线上环境才会使用后端加载的路由
        //   if (import.meta.env.MODE === 'production') {
        //     asyncRouter = menu
        //   }
        // }
        if (asyncRouter && asyncRouter.length > 0) {
          const addRouter = await permissionStore.updateRouterList(asyncRouter)
          return Promise.resolve(addRouter)
        } else {
          return Promise.reject('getPermissionList：权限必须是非空数组！')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },
    // 登出
    async logout() {
      removeToken()
      removeLocal('userInfo')

      this.$reset()
      window.location.replace('/')
    }
  }
})
