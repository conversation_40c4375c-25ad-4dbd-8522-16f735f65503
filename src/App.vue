<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 14:45:12
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 17:14:59
 * @FilePath: /corp-elf-web-consumer/src/App.vue
 * @Description: 
-->
<template>
  <a-config-provider :locale="locale" virtual :getPopupContainer="getPopupContainer" :input="{ autocomplete: 'off' }" :theme="antdTheme">
    <template #renderEmpty>
      <empty />
    </template>
    <router-view />
  </a-config-provider>

  <a-float-button-group shape="circle">
    <a-popover placement="leftBottom">
      <template #content>
        <div class="qrCodeBox">
          <div class="qrCodeItem">
            <img src="@/assets/images/qrcode/公众号.png" alt="公众号" />
            <p>公众号</p>
          </div>
          <div class="qrCodeItem">
            <img src="@/assets/images/qrcode/建议与反馈.png" alt="建议与反馈" />
            <p>建议与反馈</p>
          </div>
        </div>
      </template>
      <a-float-button>
        <template #icon>
          <CustomerServiceOutlined />
        </template>
      </a-float-button>
    </a-popover>

    <a-back-top />
  </a-float-button-group>
</template>

<script setup lang="ts">
import locale from 'ant-design-vue/es/locale/zh_CN'
import empty from '@/components/empty/index.vue'
import { computed, onMounted, provide } from 'vue'
import { useUserStore } from '@/store'
import { getToken } from '@/utils/auth/token'
import { isEmpty, isFunction, isNull } from 'lodash-es'
import { getLocal } from '@/utils/storage'
import { useThemeStore } from '@/store/modules/theme'
import { loginBaseResponseType } from '~/types/common/loginUser'
import showLoginModal from '@/components/loginModal'
import { ThemeConfig } from 'ant-design-vue/es/config-provider/context'
import { CustomerServiceOutlined } from '@ant-design/icons-vue'
import { UAParser } from 'ua-parser-js'

const themeStore = useThemeStore()

const antdTheme: ThemeConfig = {
  token: { ...themeStore.getConfig },
  components: {
    Layout: {
      // colorBgHeader: '#fff',
      // colorBgBody: '#fff'
    },
    Form: { marginLG: 16 },
    Card: { paddingLG: 16, fontSizeLG: 18, colorTextHeading: '#6553ee' },
    Modal: { marginXS: 20, marginSM: 16 },
    Tag: { colorFillAlter: '#eae7fb', colorText: '#6553ee' },
    Table: { colorFillAlter: 'rgba(152, 133, 237, 0.06)' },
    Alert: { paddingContentHorizontalLG: 16 },
    List: { paddingContentVertical: 16, paddingContentHorizontalLG: 16 },
    Result: { paddingLG: 12 }
  }
}

/** 全局注入打开登录弹窗方法 */
provide('authCheck', (func: Function | (() => any)) => {
  if (isEmpty(userStore.getToken)) {
    showLoginModal()
  } else if (isFunction(func)) {
    func()
  }
})

function getPopupContainer(_triggerNode: HTMLElement, _dialogContext: HTMLElement) {
  if (_triggerNode) {
    return _triggerNode.parentNode
  } else {
    return document.body
  }
}

const userStore = useUserStore()

// 获取设备ua
const parser = new UAParser()
const { browser, device } = parser.getResult()
const isWechat = computed(() => browser.name === 'WeChat' && device.type === 'mobile')

onMounted(() => {
  // 初始化token
  const token = getToken() as string
  if (!isEmpty(token)) {
    userStore.setToken(token)
  }
  // 初始化用户信息
  const userInfo = getLocal('userInfo') as loginBaseResponseType['userVo']
  console.log('userInfo: ', userInfo)
  if (!isEmpty(userInfo)) {
    userStore.setUserInfo(userInfo)
  }

  userStore.getUserMemberInfo() // 获取用户会员信息

  // 设置页面最大宽度,微信浏览器不进行设置
  const appDom = document.getElementById('app')
  if (!isWechat.value && appDom) {
    appDom.style.minWidth = '1400px'
  }
})
</script>

<style lang="less" scoped>
.qrCodeBox {
  // display: flex;
  // align-items: center;
  .qrCodeItem {
    text-align: center;
    img {
      pointer-events: none;
      width: 140px;
    }
    p {
      margin-top: 8px;
    }
  }
}
</style>
