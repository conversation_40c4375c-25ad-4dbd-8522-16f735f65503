/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 15:50:23
 * @LastEditors: 黄宏智(<PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 17:22:44
 * @FilePath: /corp-elf-web-consumer/src/router/guard/permission.ts
 * @Description:
 */
import type { Router } from 'vue-router'
import { useUserStore } from '@/store'
import NProgress from 'nprogress'
import showLoginModal from '@/components/loginModal'
import { UAParser } from 'ua-parser-js'

// 基础白名单
const BASE_WHITE_ROUTE_LIST = ['/404', '/term', '/term/privacyPolicy', '/term/userAgreement', '/term/membershipAgreement']
// 未登录允许访问的界面
const NOT_LOGGED_IN_ALLOWS_ACCESS_TO_PAGES = [
  // 找企业
  '/findCompany',
  '/findCompany/index',
  '/findCompany/advanceSearch',
  // 高管说
  '/executiveComments',
  '/executiveComments/overview/more',
  // 圈层
  '/circleLayer',
  '/circleLayer/overview',
  // 行业洞察
  '/industryInsights',
  '/industryInsights/overview',
  // 数据商店
  '/dataStore',
  '/dataStore/index',
  '/dataStore/detail',
  // 更多
  '/more',
  '/more/overview',
  '/more/video',
  '/more/bengine'
]
// 访问白名单
const WHITE_ROUTE_LIST = [...BASE_WHITE_ROUTE_LIST, ...NOT_LOGGED_IN_ALLOWS_ACCESS_TO_PAGES]

const parser = new UAParser()
const { browser, device } = parser.getResult()
const isWechat = () => browser.name === 'WeChat' && device.type === 'mobile'

export function createPermissionGuard(router: Router) {
  const userStore = useUserStore()
  // const permissionStore = usePermissionStore()
  router.beforeEach(async (to, from) => {
    console.log('to: ', to)
    NProgress.start()

    // 判断访问根地址情况
    if (to.path === '/') {
      return userStore.isLogin ? { path: '/home' } : { path: '/executiveComments/overview/more' }
    }

    // 判断访问的是高管说界面
    if (to.path === '/executiveComments') {
      return userStore.isLogin ? { path: '/executiveComments/overview/follow' } : { path: '/executiveComments/overview/more' }
    }

    // 先判断有没有token
    if (!userStore.isLogin) {
      // 没有，判断前往的路由不在白名单内
      if (!WHITE_ROUTE_LIST.includes(to.path)) {
        // 弹窗，停止导航
        const registerCode = from.query.registerCode
        if (!isWechat()) {
          showLoginModal()
          return false
        } else {
          // 在微信内点击链接，跳转小程序处理
          let href = `weixin://dl/business/?appid=wx4d86b574b94ddcf1&path=pages/insight/index`
          if (registerCode) {
            const queryParams = encodeURIComponent(`registerCode=${registerCode}&from=register`)
            href += `&query=${queryParams}`
          }
          window.open(href, '_blank')
        }
      }
    }
  })

  router.afterEach(() => {
    NProgress.done()
  })
}
