/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-02 14:52:22
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 16:15:59
 * @FilePath: /corp-elf-web-consumer/src/router/guard/index.ts
 * @Description:
 */
import type { Router } from 'vue-router'
import { createPermissionGuard } from './permission'
import { createTrackPageGuard } from './trackPage'

export function setupRouterGuard(router: Router) {
  createPermissionGuard(router)
  createTrackPageGuard(router)
}
