/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-23 15:22:31
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 16:42:10
 * @FilePath: /corp-elf-web-consumer/src/router/guard/trackPage.ts
 * @Description: 路由守卫，用于追踪页面访问记录和用户导航行为
 */
import { trackPage } from '@/api/api'
import { RouteLocationNormalized, Router } from 'vue-router'
import { IndexLogReqType } from '~/types/api/index/log'

type NavigationType = 'forward' | 'back' | 'reload' | 'navigate'
// 判断导航类型的函数
function getNavigationType(router: Router, from: RouteLocationNormalized): NavigationType {
  // 1. 首次加载或刷新页面
  if (!from.name) return 'reload'

  // 2. 检查浏览器 Performance API (判断是否刷新)
  const perfEntries = performance.getEntriesByType('navigation')
  if (perfEntries.length && (perfEntries[0] as PerformanceNavigationTiming).type === 'reload') {
    return 'reload'
  }

  // 3. 检查 Vue Router 的 history.state
  const currentState = router.options.history.state
  if (currentState.forward) {
    return 'forward'
  } else if (currentState.back) {
    return 'back'
  }

  // 4. 默认情况（普通跳转）
  return 'navigate'
}
export function createTrackPageGuard(router: Router) {
  router.beforeResolve((to, from) => {
    const navigationType = getNavigationType(router, from)
    const params: IndexLogReqType = {
      pageUrl: to.fullPath,
      title: to.meta.title as string,
      action: navigationType
    }
    trackPage(params)
  })
}
