import { layerDetailShowDetailResType } from './../../types/api/layer/detail/showDetail.d'
import { UpdateSearchConditionReqType, UpdateSearchConditionResType } from '~/types/api/company/updateSearchCondition.d'
import { AddRecommendIgnoreReqType } from '~/types/api/company/addRecommendIgnore.d'
import { ListRecommendIgnoreReqType, ListRecommendIgnoreResType } from '~/types/api/company/listRecommendIgnore.d'
import { ListRecommendV3ReqType, ListRecommendV3ResType } from '~/types/api/company/listRecommendV3.d'
import { RemoveRecommendIgnoreReqType } from '~/types/api/company/removeRecommendIgnore.d'
import { IndexLogReqType } from '~/types/api/index/log.d'
import { CompanyDetailWebsiteReqType, CompanyDetailWebsiteResType } from '~/types/api/company/companyDetail/website.d'
import { CompanyDetailWechatReqType, CompanyDetailWechatResType } from '~/types/api/company/companyDetail/wechat.d'
import { CompanyDetailJobReqType, CompanyDetailJobResType } from '~/types/api/company/companyDetail/job.d'
import { CompanyDetailAppReqType, CompanyDetailAppResType } from '~/types/api/company/companyDetail/app.d'
import { CompanyDetailIpoReqType, CompanyDetailIpoResType } from '~/types/api/company/companyDetail/ipo.d'
import { CompanyDetailBiddingReqType, CompanyDetailBiddingResType } from '~/types/api/company/companyDetail/bidding.d'
import { CompanyDetailClientReqType, CompanyDetailClientResType } from '~/types/api/company/companyDetail/client.d'
import { CompanyDetailSupplierReqType, CompanyDetailSupplierResType } from '~/types/api/company/companyDetail/supplier.d'
import { CompanyDetailBranchReqType, CompanyDetailBranchResType } from '~/types/api/company/companyDetail/branch.d'
import { CompanyDetailHeadReqType, CompanyDetailHeadResType } from '~/types/api/company/companyDetail/head.d'
import { CompanyDetailBusinessReqType, CompanyDetailBusinessResType } from '~/types/api/company/companyDetail/business.d'
import { CompanyDetailShareholderReqType, CompanyDetailShareholderResType } from '~/types/api/company/companyDetail/shareholder.d'
import { CompanyDetailFinancingReqType, CompanyDetailFinancingResType } from '~/types/api/company/companyDetail/financing.d'
import { CompanyDetailProductReqType, CompanyDetailProductResType } from '~/types/api/company/companyDetail/product.d'
import { CompanyDetailOutInvestReqType, CompanyDetailOutInvestResType } from '~/types/api/company/companyDetail/outInvest.d'
import { CompanyDetailPatentReqType, CompanyDetailPatentResType } from '~/types/api/company/companyDetail/patent.d'
import { CompanyDetailTrademarkReqType, CompanyDetailTrademarkResType } from '~/types/api/company/companyDetail/trademark.d'
import { CompanyDetailChangeReqType, CompanyDetailChangeResType } from '~/types/api/company/companyDetail/change.d'
/*
 * @Author: 黄宏智(HarryWong)
 * @Date: 2025-04-02 14:52:22
 * @LastEditors: 黄宏智(HarryWong)
 * @LastEditTime: 2025-06-03 18:54:59
 * @FilePath: /corp-elf-web-consumer/src/api/api.ts
 * @Description:
 */
import { CompanyDetailSoftwareBookReqType, CompanyDetailSoftwareBookResType } from '~/types/api/company/companyDetail/softwareBook.d'
import { CompanyDetailPagePreviewReqType, CompanyDetailPagePreviewResType } from '~/types/api/company/companyDetail/pagePreview.d'
import { CompanyDetailLitigationReqType, CompanyDetailLitigationResType } from '~/types/api/company/companyDetail/litigation.d'
import { CompanyDetailCertificateReqType, CompanyDetailCertificateResType } from '~/types/api/company/companyDetail/certificate.d'
import { SearchFieldResType } from '~/types/api/company/searchField.d'
import { SearchFieldAffixResType } from '~/types/api/company/searchFieldAffix.d'
import { SearchConditionResType } from '~/types/api/company/searchCondition.d'
import { SearchV2ReqType, SearchV2ResType } from '~/types/api/company/searchV2.d'
import { ClassListV2ResType } from '~/types/api/industry/classListV2.d'
import { SearchEnumsResType } from '~/types/api/company/searchEnums.d'
import { RemoveSearchConditionReqType, RemoveSearchConditionResType } from '~/types/api/company/removeSearchCondition.d'
import { ShowSearchConditionV2ReqType, ShowSearchConditionV2ResType } from '~/types/api/company/showSearchConditionV2.d'
import { CreateSearchConditionReqType, CreateSearchConditionResType } from '~/types/api/company/createSearchCondition.d'
import {
  executeSaidExplainHistoryReqType,
  executeSaidExplainHistoryResType
} from '~/types/api/executeSaidExplain/executeSaidExplainHistory'
import { executiveHotResType } from '~/types/api/executiveSaid/executiveHot'
import { userInviteRegisterStatisticsResType } from '~/types/api/userInviteRegister/statistics'
import { userInviteRegisterInvitationListResType } from '~/types/api/userInviteRegister/invitationList'
import { userInviteRegisterRewardListResType } from '~/types/api/userInviteRegister/rewardList'
import { resetPasswordReqType } from '~/types/api/auth/resetPassword'
import { executiveRelationListReqType, executiveRelationListResType } from '~/types/api/executiveSaid/relationList'
import { paymentGetPayStatusReqType, paymentGetPayStatusResType } from '~/types/api/payment/getPayStatus'
import { userMemberPricesResType } from '~/types/api/userMember/prices'
import { userMemberPaymentResType, userMemberPaymentReqType } from '~/types/api/userMember/payment'
import { userMemberInfoResType } from '~/types/api/userMember/info'
import { loginBaseRequestType, loginBaseResponseType } from '~/types/common/loginUser'
import {
  companyCaseViewpointType,
  industryCompanyViewpointRequestType,
  industryCompanyViewpointResponseType
} from '~/types/api/industry/Information/industryCompanyViewpoint'
import { phoneCodeRequestType, phoneCodeResponseType } from '~/types/api/common/phoneCode'
import { bindMpRequestType } from '~/types/api/auth/bindMp'
import { checkMpSubscribeRequestType } from '~/types/api/auth/checkMpSubscribe'
import { loginMpTicketResponseType } from '~/types/api/auth/loginMpTicket'
import { CompanySearchTipReqType, CompanySearchTipResType } from '~/types/api/company/searchTip'
import { receiveRequestType } from '~/types/api/customer/receive'
import { customerListRequestType, customerListResponseType } from '~/types/api/customer/list'
import { HotNewsRequest, HotNewsResponse } from '~/types/api/industry/hotNews'
import { IndustryRankDetailRequest, IndustryRankDetailResponse } from '~/types/api/rank/industryRankDetail'
import { IndustryRankListRequest, IndustryRankListResponse } from '~/types/api/rank/industryRankList'
import { ShowLayerRequest, ShowLayerResponse } from '~/types/api/layer/showLayer'
import { ListIndustryExhibitionResponse, ListIndustryExhibitionRequest } from '~/types/api/industry/Information/listIndustryExhibition'
import { ListIndustryViewpointRequest, ListIndustryViewpointResponse } from '~/types/api/industry/Information/listIndustryViewpoint'
import {
  ListIndustrySummaryViewpointRequest,
  ListIndustrySummaryViewpointResponse
} from '~/types/api/industry/Information/listIndustrySummaryViewpoint'
import { GetIndustryInformationConfigResponse } from '~/types/api/industry/Information/getIndustryInformationConfig'
import { getAction, getSSEAction, mockGetAction, postAction } from './manage'
import { paginationBaseRequest, paginationBaseResponse } from '~/types/common/pagination'
import { MarketingVideoGetByIdRequest, MarketingVideoGetByIdResponse } from '~/types/api/marketingVideo/getById'
import { MarketingVideoPageRequest, MarketingVideoPageResponse } from '~/types/api/marketingVideo/page'
import { getUserPermissionRequestType, getUserPermissionResponseType } from '~/types/api/auth/getUserPermission'
import { customerFollowInfoReqType, customerFollowInfoResType } from '~/types/api/customer/followInfo'
import { companyGroupReqType, companyGroupResType } from '~/types/api/company/companyDetail/companyGroup'
import { surveyReqType, surveyResType } from '~/types/api/company/companyDetail/survey'
import { newsBusinessReqType, newsBusinessResType } from '~/types/api/company/companyDetail/newsBusiness'
import { recommendScoreReqType, recommendScoreResType } from '~/types/api/company/companyDetail/recommendScore'
import { companyBaseInfoReqType, companyBaseInfoResType } from '~/types/api/company/companyDetail/companyBaseInfo'
import {
  companyCompanyDetailPortraitShowTagsReqType,
  companyCompanyDetailPortraitShowTagsResType
} from '~/types/api/company/companyDetail/showTags'
import { contactInfoReqType, contactInfoResType } from '~/types/api/contact/info'
import { contactListReqType, contactListResType } from '~/types/api/contact/list'
import { contactTypeReqType, contactTypeResType } from '~/types/api/contact/type'
import { getCompanyTypeReqType, getCompanyTypeResType } from '~/types/api/common/getCompanyType'
import { customerErrorInfoReqType } from '~/types/api/customer/errorInfo'
import { saveRecentBrowsingReqType } from '~/types/api/company/saveRecentBrowsing'
import { changePasswordReqType } from '~/types/api/auth/changePassword'
import { customerCancelRequestType, customerCancelResponseType } from '~/types/api/customer/customerCancel'
import { customerDeleteRequestType, customerDeleteResponseType } from '~/types/api/customer/customerDelete'
import { customerCollectAddOrEditRequestType, customerCollectAddOrEditResponseType } from '~/types/api/customer/customerCollectAddOrEdit'
import { getCustomerCollectGroupRequestType, getCustomerCollectGroupResponseType } from '~/types/api/customer/getCustomerCollectGroup'
import { customerGroupRequestType, customerGroupResponseType } from '~/types/api/customer/customerGroup'
import { dataPackageItemType } from '~/types/api/datastore/dataPackageItem'
import { datastorePageResponse } from '~/types/api/datastore/pageResponse'
import { listRecentBrowsingResType } from '~/types/api/company/listRecentBrowsingResType'
import { listSameCompanyExecutiveReqType, listSameCompanyExecutiveResType } from '~/types/api/executiveSaid/listSameCompanyExecutive'
import { executiveDetailReqType, executiveDetailResType } from '~/types/api/executiveSaid/executiveDetail'
import { listActiveCompanyReqType, listActiveCompanyResType } from '~/types/api/executiveSaid/listActiveCompany'
import { listActiveExecutiveReqType, listActiveExecutiveResType } from '~/types/api/executiveSaid/listActiveExecutive'
import { listCompanyExecutiveReqType, listCompanyExecutiveResType } from '~/types/api/executiveSaid/listCompanyExecutive'
import { executiveSaidListReqType, executiveSaidListResType } from '~/types/api/executiveSaid/executiveSaidList'
import { couponInfoReqType, couponInfoResType } from '~/types/api/coupon/info'

// index
export const indexSaveRecentBrowsing = (params: saveRecentBrowsingReqType) => postAction(`/index/saveRecentBrowsing`, params) // 首页->保存最近浏览
export const indexListRecentBrowsing = params => getAction<listRecentBrowsingResType[]>(`/index/listRecentBrowsing`, params) // 首页->最近浏览列表 - huanbin

// common
export const commonGetCompanyType = (params: getCompanyTypeReqType) => getAction<getCompanyTypeResType>('/common/getCompanyType', params) // 获取公司类型
export const commonPhoneCode = (params: phoneCodeRequestType) => postAction<phoneCodeResponseType>(`/common/phoneCode`, params) // 获取验证码

// auth
export const login = (params: loginBaseRequestType) => postAction<loginBaseResponseType>('/auth/login', params) // 登录
export const logout = () => getAction('/auth/logout', {}) // 登出
export const queryPermissionsByUser = (params: getUserPermissionRequestType) =>
  getAction<getUserPermissionResponseType>('/auth/permission/getUserPermission', params) // 获取权限点
export const authUserChangePassword = (params: changePasswordReqType) => postAction(`/auth/user/changePassword`, params) // 修改密码
export const authUserResetPassword = (params: resetPasswordReqType) => postAction(`/auth/user/resetPassword`, params) // 修改密码
export const authLoginMpTicket = () => getAction<loginMpTicketResponseType>(`/auth/loginMpTicket`, {}) // 公开联系人-联系方式类型
export const authCheckMpSubscribe = (params: checkMpSubscribeRequestType) =>
  postAction<loginBaseResponseType>(`/auth/checkMpSubscribe`, params) // 微信公众号ticket检查是否关注
export const authBindMp = (params: bindMpRequestType) => postAction<loginBaseResponseType>(`/auth/bindMp`, params) // 微信公众号登录绑定

// industryuserMemberInfo
export const getIndustryInformationConfig = params =>
  getAction<GetIndustryInformationConfigResponse>(`/industry/information/getIndustryInformationConfig`, params) // 行业列表
export const listIndustrySummaryViewpoint = (params: ListIndustrySummaryViewpointRequest) =>
  postAction<ListIndustrySummaryViewpointResponse[]>(`/industry/information/listIndustrySummaryViewpoint`, params) // 获取洞见总结观点
export const listIndustryViewpoint = (params: ListIndustryViewpointRequest) =>
  postAction<paginationBaseResponse<ListIndustryViewpointResponse>>(`/industry/information/listIndustryViewpoint`, params) // 获取洞见新闻观点
export const listIndustryExhibition = (params: ListIndustryExhibitionRequest) =>
  postAction<ListIndustryExhibitionResponse[]>(`/industry/information/listIndustryExhibition`, params) // 获取行业活动
export const industryHotNews = (params: HotNewsRequest) => postAction<paginationBaseResponse<HotNewsResponse>>(`/industry/hotNews`, params) // 新闻列表
export const industryInformationListIndustryCompanyViewpoint = (params: industryCompanyViewpointRequestType) =>
  postAction<paginationBaseResponse<industryCompanyViewpointResponseType>>(`/industry/information/listIndustryCompanyViewpoint`, params) // 获取标杆案例
export const industryInformationAllIndustryCompanyViewpoint = (params: industryCompanyViewpointRequestType) =>
  postAction<paginationBaseResponse<companyCaseViewpointType>>(`/industry/information/allIndustryCompanyViewpoint`, params) // 获取标杆案例-根据公司id

// layer
export const layerShowLayer = (params: ShowLayerRequest, cancelToken?: AbortController) =>
  postAction<paginationBaseResponse<ShowLayerResponse>>(`/layer/showLayer`, params, cancelToken) // 行业圈子

// rank
export const rankIndustryRankList = (params: IndustryRankListRequest) =>
  postAction<IndustryRankListResponse[]>('/rank/industryRankList', params) // 企业名录列表
export const rankIndustryRankDetail = (params: IndustryRankDetailRequest) =>
  postAction<paginationBaseResponse<IndustryRankDetailResponse[]>>('/rank/industryRankDetail', params) // 企业名录企业列表

// customer
export const customerList = (params: customerListRequestType) =>
  postAction<paginationBaseResponse<customerListResponseType>>('/customer/list', params) // 获取用户关注企业分组详情
export const customerReceive = (params: receiveRequestType) => postAction('/customer/receive', params) // 添加关注企业
export const getCustomerCollectGroup = (params: getCustomerCollectGroupRequestType) =>
  getAction<getCustomerCollectGroupResponseType[]>('/customer/customerCollect', params) // 获取用户关注企业分组
export const customerCollectAddOrEdit = (params: customerCollectAddOrEditRequestType) =>
  postAction<customerCollectAddOrEditResponseType>('/customer/customerCollect/saveOrUpdate', params) // 新增、修改分组
export const customerCollectDelete = (params: customerDeleteRequestType) =>
  getAction<customerDeleteResponseType>('/customer/customerCollect/delete', params) // 分组（删除）
export const customerGroup = (params: customerGroupRequestType) => postAction<customerGroupResponseType>('/customer', params) // 我的潜客-客户分组
export const customerCancel = (params: customerCancelRequestType) => postAction<customerCancelResponseType>('/customer/cancel', params) // 我的潜客-客户取消关注
export const customerFollowInfo = (params: customerFollowInfoReqType) =>
  getAction<customerFollowInfoResType>(`/customer/followInfo`, params) // 公司详情-跟进信息
export const customerIsUnlock = (params: { companyId: string }) =>
  getAction<{ label: string; type: string }[]>('/customer/isUnlock', params) // 是否解锁
export const customerErrorInfo = (params: customerErrorInfoReqType) => postAction('/customer/errorInfo', params) // 企业数据纠错

// company
export const companyDetailSurveyV2 = (params: surveyReqType) => getAction<surveyResType>('/company/companyDetail/survey/v2', params) // 公司详情-概况V2
export const companyDetailGroup = (params: companyGroupReqType) => getAction<companyGroupResType>(`/company/companyDetail/group`, params) // 公司集团
export const companyCompanyDetailPortraitShowTags = (params: companyCompanyDetailPortraitShowTagsReqType) =>
  getAction<companyCompanyDetailPortraitShowTagsResType>(`/company/companyDetail/portrait/showTags`, params) // 企业画像->展示标签
export const companyCompanyDetailRelationOrganizationShowTypeNumbers = (params: { companyId: string }) =>
  getAction(`/company/companyDetail/relationOrganization/showTypeNumbers`, params) // 关联组织->类型类型数量
export const companyCompanyDetailNewsBusiness = (params: newsBusinessReqType) =>
  postAction<paginationBaseResponse<newsBusinessResType>>(`/company/companyDetail/news/business`, params) // 商情
export const companyCompanyDetailRecommendScore = (params: recommendScoreReqType) =>
  getAction<recommendScoreResType[]>(`/company/companyDetail/recommendScore`, params) // 推荐评分
export const companyDetailBaseInfo = (params: companyBaseInfoReqType) =>
  getAction<companyBaseInfoResType>('/company/companyList/baseInfo', params) // 公司详情-获取公司基本信息
export const companySearchTip = (params: CompanySearchTipReqType, cancelToken?: AbortController) =>
  postAction<CompanySearchTipResType[]>('/company/searchTip', params, cancelToken) // 全局搜索

// contact
export const contactV2Type = (params: contactTypeReqType) => getAction<contactTypeResType[]>(`/contact/v2/type`, params) // 公开联系人-联系方式类型
export const contactV2CompanyInfo = (params: contactInfoReqType) => getAction<contactInfoResType>(`/contact/v2/company/info`, params) // 公开联系人-联系方式类型的数量
export const contactV2CompanyList = (params: contactListReqType) =>
  getAction<paginationBaseResponse<contactListResType>>(`/contact/v2/company/list`, params) // 公开联系人-获取公司联系人列表

// marketingVideo
export const marketingVideoPage = (params: MarketingVideoPageRequest) =>
  getAction<paginationBaseResponse<MarketingVideoPageResponse>>(`/marketingVideo/page`, params) // 获取视频列表
export const marketingVideoGetById = (params: MarketingVideoGetByIdRequest) =>
  getAction<MarketingVideoGetByIdResponse>(`/marketingVideo/getById`, params) // 获取视频-单个

// datastore
export const datastoreDetailRecommend = (params: paginationBaseRequest) =>
  postAction<paginationBaseResponse<datastorePageResponse>>(`/datastore/detail/recommend`, params) // 数据商店详情-推荐数据
export const datastoreDetailShow = (params: { dataStoreId: string }) => postAction<dataPackageItemType>(`/datastore/detail/show`, params) // 数据商店详情
export const datastorePage = (params: paginationBaseRequest) =>
  postAction<paginationBaseResponse<datastorePageResponse>>(`/datastore/page`, params) // 数据商店分页

// executiveComments 高管说
export const executiveSaidList = (params: executiveSaidListReqType) =>
  getAction<paginationBaseResponse<executiveSaidListResType>>('/executiveSaid/list', params) // 高管说列表
export const executiveSaidListActiveExecutive = (params: listActiveExecutiveReqType) =>
  getAction<listActiveExecutiveResType[]>('/executiveSaid/listActiveExecutive', params) // 活跃高管
export const executiveSaidListActiveCompany = (params: listActiveCompanyReqType) =>
  getAction<listActiveCompanyResType[]>('/executiveSaid/listActiveCompany', params) // 高管活跃企业
export const executiveSaidExecutiveDetail = (params: executiveDetailReqType) =>
  getAction<executiveDetailResType[]>('/executiveSaid/executiveDetail', params) // 高管详情
export const executiveSaidListCompanyExecutive = (params: listCompanyExecutiveReqType) =>
  getAction<paginationBaseResponse<listCompanyExecutiveResType>>('/executiveSaid/listCompanyExecutive', params) // 公司高管
export const executiveSaidListSameCompanyExecutive = (params: listSameCompanyExecutiveReqType) =>
  getAction<paginationBaseResponse<listSameCompanyExecutiveResType>>('/executiveSaid/listSameCompanyExecutive', params) // 同公司高管
export const executiveRelationList = (params: executiveRelationListReqType) =>
  getAction<paginationBaseResponse<executiveRelationListResType>>('/executive/relation/list', params) // 获取关注高管列表
export const executiveRelationAdd = (params: { executiveId: string }) => postAction<boolean>('/executive/relation/add', params) // 添加关注高管
export const executiveRelationRemove = (params: { executiveIds: string[] }) => postAction<boolean>('/executive/relation/remove', params) // 删除关注高管
export const executiveIsRelation = (params: { executiveId: string }) => getAction<boolean>('/executive/isRelation', params) // 删除关注高管
export const executiveRelationCount = () => getAction<number>('/executive/relation/count') // 当前关注高管的总数
export const executiveHot = () => getAction<executiveHotResType[]>('/executive/hot') // 热门高管
// 高管说解读
export const executeSaidExplainHistory = (params: executeSaidExplainHistoryReqType) =>
  getAction<paginationBaseResponse<executeSaidExplainHistoryResType>>(`/executeSaidExplain/history`, params) // 高管说解读历史
export const executeSaidExplainChatClose = (params: { executiveId: string }) => getAction(`/executeSaidExplain/chatClose`, params) // 高管说解读终止
export const executeSaidExplainCheckLimit = () => getAction(`/executeSaidExplain/checkLimit`) // 高管说解读检查是否每日请求限制
export const executeSaidExplainTips = (params: { executiveId: string }) => getAction<string[]>(`/executeSaidExplain/tips`, params) // 高管说解读提示
export const executeSaidExplainLast = () => getAction(`/executeSaidExplain/last`) // 高管说解读提示
export const executeSaidExplainChatStream = async (params: { executiveId: string; problem: string }) =>
  getSSEAction(`/executeSaidExplain/chatStream`, params) // 高管说解读

// 高级搜索
export const companyCreateSearchCondition = (params: CreateSearchConditionReqType) =>
  postAction<CreateSearchConditionResType>('/company/createSearchCondition', params) // 新建搜索条件
export const companyShowSearchConditionV2 = (params: ShowSearchConditionV2ReqType) =>
  getAction<ShowSearchConditionV2ResType>(`/company/showSearchCondition/v2`, params) // 获取保存的搜索条件 - v2
export const companySearchEnums = () => getAction<SearchEnumsResType>(`/company/search/enums`) // 获取枚举值
export const companyRemoveSearchCondition = (params: RemoveSearchConditionReqType) =>
  postAction<RemoveSearchConditionResType>('/company/removeSearchCondition', params) // 删除搜索条件
export const industryClassListV2 = () => getAction<ClassListV2ResType>(`/industry/class/list/v2`) // 获取所有商瞳行业V2
export const companySearchV2 = (params: SearchV2ReqType) => postAction<SearchV2ResType>(`/company/search/v2`, params) // 公司搜索 - v2
export const companySearchCondition = () => getAction<SearchConditionResType>(`/company/search/condition`) // 获取筛选条件
export const companySearchField = () => getAction<SearchFieldResType>(`/company/search/field`) // 获取搜索字段
export const companySearchFieldAffix = () => getAction<SearchFieldAffixResType>(`/company/search/fieldAffix`) // 获取字段词缀

// 会员x优惠券
export const couponInfo = (params: couponInfoReqType) => getAction<couponInfoResType[]>(`/coupon/info`, params) // 获取优惠券列表
export const couponExchange = (params: { exchangeCode: string }) => getAction<Boolean>(`/coupon/exchange`, params) // 优惠券兑换
export const userMemberInfo = () => getAction<userMemberInfoResType>(`/user/member/info`) // 获取用户会员信息
export const userMemberPayment = (params: userMemberPaymentReqType) => postAction<userMemberPaymentResType>(`/user/member/payment`, params) // 获取用户会员支付信息
export const userMemberPrices = () => getAction<userMemberPricesResType[]>(`/user/member/prices`) // 获取会员价格
export const paymentGetPayStatus = (params: paymentGetPayStatusReqType) =>
  getAction<paymentGetPayStatusResType>('/payment/getPayStatus', params) // 支付状态查询
export const cancelUserAllVipWechatQrCode = () => postAction(`/user/member/cancelUserAllVipWechatQrCode`, {}) // 关闭所有未支付的订单
export const customerCheckReceive = () => postAction<boolean>(`/customer/checkReceive`, {}) // 领取客户检测

export const companyCompanyDetailHead = (params: CompanyDetailHeadReqType) =>
  getAction<CompanyDetailHeadResType>('/company/companyDetail/head', params) // 公司详情-总公司(无分页)
export const companyCompanyDetailBranch = (params: CompanyDetailBranchReqType) =>
  getAction<CompanyDetailBranchResType>('/company/companyDetail/branch', params) // 公司详情-分支机构
export const companyCompanyDetailSupplier = (params: CompanyDetailSupplierReqType) =>
  getAction<CompanyDetailSupplierResType>('/company/companyDetail/supplier', params) // 公司详情-供应商
export const companyCompanyDetailClient = (params: CompanyDetailClientReqType) =>
  getAction<CompanyDetailClientResType>('/company/companyDetail/client', params) // 公司详情-客户
export const companyCompanyDetailBidding = (params: CompanyDetailBiddingReqType) =>
  getAction<CompanyDetailBiddingResType>('/company/companyDetail/bidding', params) // 公司详情-招投标
export const companyCompanyDetailIpo = (params: CompanyDetailIpoReqType) =>
  getAction<CompanyDetailIpoResType>('/company/companyDetail/ipo', params) // 公司详情-上市信息（无分页）
export const companyCompanyDetailApp = (params: CompanyDetailAppReqType) =>
  getAction<CompanyDetailAppResType>('/company/companyDetail/app', params) // 公司详情-app信息
export const companyCompanyDetailJob = (params: CompanyDetailJobReqType) =>
  getAction<CompanyDetailJobResType>('/company/companyDetail/job', params) // 公司详情-招聘信息
export const companyCompanyDetailWechat = (params: CompanyDetailWechatReqType) =>
  getAction<CompanyDetailWechatResType>('/company/companyDetail/wechat', params) // 公司详情-微信公众号
export const companyCompanyDetailWebsite = (params: CompanyDetailWebsiteReqType) =>
  getAction<CompanyDetailWebsiteResType>('/company/companyDetail/website', params) // 公司详情-备案网站
export const companyDetailBusiness = (params: CompanyDetailBusinessReqType) =>
  getAction<CompanyDetailBusinessResType>(`/company/companyDetail/business`, params) // 公司业务

export const companyDetailProduct = (params: CompanyDetailProductReqType) =>
  getAction<CompanyDetailProductResType>('/company/companyDetail/product', params) // 公司详情-公司产品信息
export const companyDetailFinancing = (params: CompanyDetailFinancingReqType) =>
  getAction<CompanyDetailFinancingResType>('/company/companyDetail/financing', params) // 公司详情-公司融资信息
export const companyDetailShareholder = (params: CompanyDetailShareholderReqType) =>
  getAction<CompanyDetailShareholderResType>('/company/companyDetail/shareholder', params) // 公司详情-公司详情-公司股东信息
export const companyDetailOutInvest = (params: CompanyDetailOutInvestReqType) =>
  getAction<CompanyDetailOutInvestResType>('/company/companyDetail/outInvest', params) // 公司详情-公司对外投资信息
export const companyDetailChange = (params: CompanyDetailChangeReqType) =>
  getAction<CompanyDetailChangeResType>('/company/companyDetail/change', params) // 公司详情-公司变更信息
export const companyDetailTrademark = (params: CompanyDetailTrademarkReqType) =>
  getAction<CompanyDetailTrademarkResType>('/company/companyDetail/trademark', params) // 公司详情-公司商标
export const companyDetailPatent = (params: CompanyDetailPatentReqType) =>
  getAction<CompanyDetailPatentResType>('/company/companyDetail/patent', params) // 公司详情-公司专利信息
export const companyDetailSoftwareBook = (params: CompanyDetailSoftwareBookReqType) =>
  getAction<CompanyDetailSoftwareBookResType>('/company/companyDetail/softwareBook', params) // 公司详情-公司软件信息
export const companyDetailCertificate = (params: CompanyDetailCertificateReqType) =>
  getAction<CompanyDetailCertificateResType>('/company/companyDetail/certificate', params) // 公司详情-公司证书信息
export const companyDetailLitigation = (params: CompanyDetailLitigationReqType) =>
  getAction<CompanyDetailLitigationResType>('/company/companyDetail/litigation', params) // 公司详情-法律诉讼（风险页面)
export const companyCompanyDetailPagePreview = (params: CompanyDetailPagePreviewReqType) =>
  getAction<CompanyDetailPagePreviewResType>(`/company/companyDetail/page/preview`, params) // 公司详情分页预览

// 用户分享
export const userInviteRegisterCode = () => getAction(`/user/invite/register/code`) // 获取分享码
export const userInviteRegisterStatistics = () => getAction<userInviteRegisterStatisticsResType>(`/user/invite/register/statistics`) // 统计金额，会员月数
export const userInviteRegisterRewardList = (params: paginationBaseRequest) =>
  getAction<paginationBaseResponse<userInviteRegisterRewardListResType>>(`/user/invite/register/reward/list`, params) // 奖励记录
export const userInviteRegisterRecordList = (params: paginationBaseRequest) =>
  getAction<paginationBaseResponse<userInviteRegisterInvitationListResType>>(`/user/invite/register/record/list`, params) // 邀请记录
export const userInviteRegisterRewardReceive = (params: { ids: number[] }) =>
  postAction<boolean>(`/user/invite/register/reward/receive`, params) // 领取
export const trackPage = (params: IndexLogReqType) => postAction<boolean>(`/index/log`, params) // 页面pv埋点

//
export const companyRemoveRecommendIgnore = (params: RemoveRecommendIgnoreReqType) =>
  postAction<boolean>(`/company/removeRecommendIgnore`, params) // 删除推荐忽略公司
export const companyListRecommendV3 = (params: ListRecommendV3ReqType) =>
  postAction<ListRecommendV3ResType>('/company/listRecommendV3', params) // 推荐搜索
export const companyListRecommendIgnore = (params: ListRecommendIgnoreReqType) =>
  postAction<ListRecommendIgnoreResType>('/company/listRecommendIgnore', params) // 忽略推荐公司列表
export const companyAddRecommendIgnore = (params: AddRecommendIgnoreReqType) => postAction<boolean>(`/company/addRecommendIgnore`, params) // 添加推荐忽略公司
export const companyUpdateSearchCondition = (params: UpdateSearchConditionReqType) =>
  postAction<UpdateSearchConditionResType>('/company/updateSearchCondition', params) // 更新搜索条件
export const layerSummaryShowSummaryChart = params => postAction(`/layer/summary/showSummaryChart`, params) // 概况-图形基础信息
export const industryClassTypeV3 = params => getAction('/industry/classType/v3', params) // 获取行业研究-行业分类所有一级v3
export const layerSummaryShowRecommendLayerChart = params => postAction(`/layer/summary/showRecommendLayerChart`, params) // 概况-推荐补充的圈层
export const layerSummaryShowReceiveLayerChart = params => postAction(`/layer/summary/showReceiveLayerChart`, params) // 概况-已维护圈层export const layerDetailShowDetail = params =>
export const layerDetailShowDetail = params => postAction<layerDetailShowDetailResType>(`/layer/detail/showDetail`, params) // 圈层详情展示
export const layerDetailShowDetailCompany = params => postAction(`/layer/detail/showDetailCompany`, params) // 圈层详情公司展示
export const layerReceiveLayer = params => postAction(`/layer/receiveLayer`, params) // 领取圈层
export const layerUnReceiveLayer = params => postAction(`/layer/unReceiveLayer`, params) // 取消领取圈层
