/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-07-30 14:45:33
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-01 18:32:03
 * @FilePath: /corp-elf-web-consumer/src/api/mockRequest.ts
 * @Description:
 */
import type { ErrorMessageType, ResponseData } from '~/types/response'
import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { Modal, notification } from 'ant-design-vue'
import { useUserStore } from './../store/modules/user'
import { getToken } from '@/utils/auth/token'

// 创建axios实例
// console.log('import.meta.env: ', import.meta.env)
const service: AxiosInstance = axios.create({
  baseURL: '/mock',
  timeout: 1000 * 60 // 请求超时时间
})

// axios请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken() as string

    // 设置Header
    // 将签名和时间戳，添加在请求接口 Header
    // const sign = signMd5Utils.getSign(config.url, config.data)
    const mergeHeader: { 'X-Access-Token'?: string } = {} //{ 'X-Sign': sign, 'X-TIMESTAMP': signMd5Utils.getDateTimeToString() }
    if (token) {
      mergeHeader['X-Access-Token'] = token
    }
    config.headers = Object.assign(config.headers, mergeHeader)

    // get请求添加上时间戳
    if (config.method == 'get') {
      config.params = {
        _t: new Date().valueOf(),
        ...config.params
      }
    }

    return config
  },
  (error: AxiosError) => {
    notification.error({ message: '系统提示', description: error.message })
    return Promise.reject(error)
  }
)

// axios响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<any, ResponseData>) => {
    const userStore = useUserStore()
    const token = userStore.token
    const res = response.data
    const whiteCode = [0, 200,'200', 'SUCCESS', 'WAITING']

    console.log('res: ', res)
    console.log('mock res: ' + '\n' + response.request.custom.url + '\n', res)

    // 根据自定义错误码判断请求是否成功
    if (!whiteCode.includes(res.code)) {
      if (token && res.code === 'NON_AUTH') {
        // 登录已过期
        Modal.error({
          title: '登录已过期',
          content: res.message,
          okText: '重新登录',
          mask: false,
          onOk: () => {
            userStore.logout()
          }
        })
      } else if (token && res.code === 'PAGE_AUTH_ERROR') {
        // 无翻页权限
        notification.warning({ message: '系统提示', description: res.message || res.msg })
      } else {
        // 错误上报
        // Sentry.captureException(res)
        // 普通报错提示
        notification.error({ message: '系统提示', description: res.message || res.msg })
      }
      return Promise.reject(new Error(res.message || res.msg || 'Error'))
    }
    return res
  },
  (err: AxiosError) => {
    // 处理 HTTP 网络错误
    const errorMessage: ErrorMessageType = {
      config: err,
      requestUrl: err.config?.url || '',
      response: undefined,
      message: ''
    }

    if (err.response) {
      const userStore = useUserStore()
      const token = userStore.token
      const data = err.response.data as ResponseData

      // 定义错误返回数据
      errorMessage.response = data
      errorMessage.message = data.message

      switch (err.response.status) {
        case 403:
          notification.error({ message: '系统提示', description: '拒绝访问', duration: 4 })
          break
        case 500:
          // notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})
          if (token && data.code === 'NON_AUTH') {
            Modal.error({
              title: '登录已过期',
              content: data.message,
              okText: '重新登录',
              mask: false,
              onOk: () => {
                userStore.logout()
              }
            })
          }
          break
        case 404:
          notification.error({ message: '系统提示', description: '很抱歉，资源未找到!', duration: 4 })
          break
        case 504:
          notification.error({ message: '系统提示', description: '网络超时' })
          break
        case 401:
          notification.error({ message: '系统提示', description: '未授权，请重新登录', duration: 4 })
          if (token) {
            userStore.logout()
          }
          break
        default:
          // 错误上报
          // Sentry.captureException(err)
          notification.error({
            message: '系统提示',
            description: data.message,
            duration: 4
          })
          break
      }
    }

    return Promise.reject(errorMessage)
  }
)

export default service
