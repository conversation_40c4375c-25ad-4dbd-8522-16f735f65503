import Mock from 'mockjs'

const Random = Mock.Random

Mock.setup({
  timeout: '10-100' //表示响应时间介于 200 和 600 毫秒之间。默认值是'10-100'。
})

// 行业列表
Mock.mock(/\/industry\/list/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    'data|10': [
      {
        id: '@id',
        name: '@cword(4,8)'
      }
    ]
  }
})

// 新闻类型 全部类型、IPO上市、对外投资、企业融资、企业破产、亏损、高管变动、公司裁员、新品发布、战略合作、办会参会、新机构成立、考察拜访、高管发声、出海、奖励
Mock.mock(/\/news\/typeList/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: [
    '全部类型',
    'IPO上市',
    '对外投资',
    '企业融资',
    '企业破产',
    '亏损',
    '高管变动',
    '公司裁员',
    '新品发布',
    '战略合作',
    '办会参会',
    '新机构成立',
    '考察拜访',
    '高管发声',
    '出海',
    '奖励'
  ]
})

// 新闻列表
Mock.mock(/\/news\/hotList/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    'data|10': [
      {
        id: '@id',
        title: '@cword(4,8)',
        type: '@cword(4,8)'
      }
    ]
  }
})

//

// 企业名录
Mock.mock(/\/directory\/list/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  'result|10': [
    {
      id: '@id',
      name: '@cword(4,8)'
    }
  ]
})
// 企业名录企业列表
Mock.mock(/\/directory\/by\/id/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    currentPageNum: 1,
    currentPageSize: 30,
    'data|10': [
      {
        id: '@id',
        keywords: () => {
          const temp = [Random.pick(['客户', '竞对', undefined])]
          return temp.includes(undefined) ? [] : temp
        },
        companyName: () =>
          Random.pick([
            '杭州娃哈哈集团有限公司',
            '维维集团股份有限公司',
            '内蒙古伊利实业集团股份有限公司',
            '光明乳业股份有限公司',
            '康师傅饮品投资（中国）有限公司',
            '内蒙古蒙牛乳业集团股份有限公司',
            '冠生园（集团）有限公司',
            '雀巢(中国)有限公司',
            '中粮可口可乐饮料(中国)投资有限公司',
            '北京汇源饮料食品集团有限公司'
          ]),
        companyId: '',
        dynamics: () => {
          const temp = Random.pick(['客户', '战略合作', '上市', '出海', undefined])
          return temp.includes(undefined) ? [] : temp
        }
      }
    ],
    total: 20
  }
})
// 行业活动
Mock.mock(/\/activity\/list/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  'result|10': [
    {
      eventDate: '@date',
      eventName: '@ctitle'
    }
  ]
})
// 行业圈子
Mock.mock(/\/circle\/list/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  'result|10': [
    {
      name: () =>
        Random.pick([
          '中国信息通信研究院',
          '中国上市公司协会',
          '中国企业联合会',
          '中国证券业协会',
          '中国物流与采购联合会',
          '中国轻工业联合会',
          '中国高等教育学会',
          '中国汽车工业协会'
        ]),
      'activityLevel|1-100': 100,
      'relatedConcernedEnterprises|1-500': 1000
    }
  ]
})

// 行业圈子
Mock.mock(/\/marketingVideo\/page/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    'records|10': [
      {
        id: '@id',
        title: '@ctitle(4,10)',
        content: '@cparagraph',
        videoUrl: 'https://bengine-video-1258659963.cos.ap-guangzhou.myqcloud.com/video1.mov',
        coverImageUrl: 'http://iph.href.lu/1920x1080',
        videoTime: 343,
        sort: 1,
        createTime: '2024年04月15日 15:05:53',
        updateTime: '2024年04月15日 15:05:53',
        isDeleted: 0
      }
    ],
    total: 100
  }
})

// 行业圈子
Mock.mock(/\/marketingVideo\/getById/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    id: '@id',
    title: '@ctitle(4,10)',
    content: '@cparagraph',
    videoUrl: 'https://bengine-video-1258659963.cos.ap-guangzhou.myqcloud.com/video1.mov',
    coverImageUrl: 'http://iph.href.lu/1920x1080',
    videoTime: 343,
    sort: 1,
    createTime: '2024年04月15日 15:05:53',
    updateTime: '2024年04月15日 15:05:53',
    isDeleted: 0
  }
})

// 高管说
Mock.mock(/\/executiveComments\/comments/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    'records|10': [
      {
        newsId: '@id', // 新闻id
        url: '@domain', // 新闻地址
        title: '@ctitle', // 新闻标题
        siteName: '@ctitle(4,8)', // 新闻站点
        publishTime: "@date('T')", // 新闻发布时间
        executiveComment: '@cparagraph', // 高管言论
        industry: '人工智能', // 行业
        companyName: '@ctitle(4,8)', // 公司名
        companyUniId: '@id', // 公司id
        executiveId: '@id', // 高管id
        executiveName: '@cname', // 高管名
        post: '@ctitle(4,10)' // 岗位名称
      }
    ],
    total: 100
  }
})
Mock.mock(/\/executiveComments\/activeExecutives/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    'records|10': [
      {
        id: '@id',
        companyName: '@ctitle(4,10)', // 公司名
        companyUniId: '@id', // 公司id
        executiveId: '@id', // 高管id
        executiveName: '@cname', // 高管名
        post: '@ctitle(4,10)', // 岗位名称
        num: '@integer(1,2500)' // 言论数量
      }
    ],
    total: 100
  }
})
Mock.mock(/\/executiveComments\/executivesEnterprises/, 'get', {
  success: true,
  timestamp: 1705980887629,
  code: 'SUCCESS',
  message: '操作成功！',
  result: {
    'records|10': [
      {
        id: '@id',
        companyName: '@ctitle(4,10)', // 公司名
        companyUniId: '@id', // 公司id
        num: '@integer(1,2500)' // 言论数量
      }
    ],
    total: 100
  }
})

// // 行业圈子
// Mock.mock(/\/marketingVideo\/getById/, 'get', {
//   success: true,
//   timestamp: 1705980887629,
//   code: 'SUCCESS',
//   message: '操作成功！',
//   result: {
//     id: '@id',
//     title: '@ctitle(4,10)',
//     content: '@cparagraph',
//     videoUrl: 'https://bengine-video-1258659963.cos.ap-guangzhou.myqcloud.com/video1.mov',
//     coverImageUrl: 'http://iph.href.lu/1920x1080',
//     videoTime: 343,
//     sort: 1,
//     createTime: '2024年04月15日 15:05:53',
//     updateTime: '2024年04月15日 15:05:53',
//     isDeleted: 0
//   }
// })
