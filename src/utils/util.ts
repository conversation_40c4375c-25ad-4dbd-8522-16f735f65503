import { message } from 'ant-design-vue'
import { isBoolean, isEmpty, isNull, isString, toNumber } from 'lodash-es'
import areaDict from '@/assets/json/pac.json'
import annualRevenueMap from '@/assets/json/annualRevenues.json'
import { ResponseData } from '~/types/response'
import { UseRequestPlugin } from 'vue-hooks-plus/lib/useRequest/types'
import { RouteRecordRaw } from 'vue-router'

// const modules = {
//   ...import.meta.glob(['../views/**/**.vue']),
//   ...import.meta.glob('../components/layouts/**.vue')
// }

export function timeFix() {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 20 ? '下午好' : '晚上好'
}

// 生成首页路由
export function generateIndexRouter(data: RouteRecordRaw[]): RouteRecordRaw[] {
  const indexRouter: RouteRecordRaw[] = [
    {
      path: '/',
      name: 'main',
      component: () => import('@/components/layouts/index.vue'),
      // component: resolve => require(['@/components/layouts/index.vue'], resolve),
      meta: { title: '首页' },
      redirect: '/overview',
      children: [...generateChildRouters(data)]
    }
  ]
  console.log('generateChildRouters(data): ', generateChildRouters(data))
  return indexRouter
}

// 生成嵌套路由（子路由）
function generateChildRouters(data: RouteRecordRaw[]): RouteRecordRaw[] {
  const routers: RouteRecordRaw[] = []
  for (const item of data) {
    const menu: RouteRecordRaw = {
      path: item.path,
      name: item.name,
      redirect: !isEmpty(item.redirect) ? item.redirect : !isEmpty(item.children) ? item.children?.[0].path : undefined,
      component: item.component, //  isString(item.component) ? modules[`../${item.component.replace('.vue', '')}.vue`] : item.component,
      meta: {
        title: item.meta?.title || '',
        icon: item.meta?.icon || '',
        // url: item.meta.url,
        style: item.meta?.style || {},
        // permissionList: item.meta?.permissionList,
        // keepAlive: isBoolean(item.meta?.keepAlive) ? item.meta?.keepAlive : item.meta?.keepAlive === 1,
        hidden: item.meta?.hidden || false,
        polymerization: item.meta?.polymerization || false, // 聚合路由，只显示最高一层的信息
        name: item.meta?.name || '' // 自定义缓存名称
      },
      children: [],
      beforeEnter: item.beforeEnter
    }
    if (item.children && item.children.length > 0) {
      menu.children = [...generateChildRouters(item.children)]
    }
    routers.push(menu)
  }
  return routers
}

/**
 * 随机生成数字
 *
 * 示例：生成长度为 12 的随机数：randomNumber(12)
 * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)
 *
 * @param1 最小值 | 长度
 * @param2 最大值
 * @return int 生成后的数字
 */
export function randomNumber(_minLength?: number, _maxLength?: number): number {
  // 生成 最小值 到 最大值 区间的随机数
  const random = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1) + min)
  }
  if (arguments.length === 1) {
    const [length] = arguments
    // 生成指定长度的随机数字，首位一定不是 0
    const nums = [...Array(length).keys()].map(i => (i > 0 ? random(0, 9) : random(1, 9)))
    return parseInt(nums.join(''))
  }
  if (arguments.length >= 2) {
    const [min, max] = arguments
    return random(min, max)
  }
  return Number.NaN
}

/**
 * 随机生成字符串
 * @param length 字符串的长度
 * @param chats 可选字符串区间（只会生成传入的字符串中的字符）
 * @return string 生成的字符串
 */
export function randomString(length: number, chats: string) {
  if (!length) length = 1
  if (!chats) chats = '0123456789qwertyuioplkjhgfdsazxcvbnm'
  let str = ''
  for (let i = 0; i < length; i++) {
    const num = randomNumber(0, chats.length - 1)
    str += chats[num]
  }
  return str
}

/**
 * 随机生成uuid
 * @return string 生成的uuid
 */
export function randomUUID() {
  const chats = '0123456789abcdef'
  return randomString(32, chats)
}

/**
 * 下划线转驼峰
 * @param string
 * @returns {*}
 */
export function underLine2CamelCase(string: string): string {
  return string.replace(/_([a-z])/g, function (_all, letter) {
    return letter.toUpperCase()
  })
}

/**
 * @description: 保存文件
 * @param {*} blobData 二进制文件流
 * @param {*} fileName 文件名称
 * @return {*}
 */
export function saveFile(blobData: Blob, fileName: string): void {
  if (!blobData || blobData.size === 0) {
    message.warning('文件下载失败')
    return
  }
  // if (typeof window.navigator.msSaveBlob !== 'undefined') {
  //   window.navigator.msSaveBlob(new Blob([blobData]), fileName)
  // } else
  //  {
  const url = window.URL.createObjectURL(new Blob([blobData]))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link) // 下载完成移除元素
  window.URL.revokeObjectURL(url) // 释放掉blob对象
  // }
}

/**
 * @description: 转换企业地址显示格式
 * @param {object} companyInfo
 * @return {*}
 */
export function transformLocation(companyInfo: { province: string; city?: string; area?: string }): string {
  let { city = '', province = '' } = companyInfo
  if (province) {
    province = province
      .replace(/古自治区/g, '')
      .replace(/壮族自治区/g, '')
      .replace(/回族自治区/g, '')
      .replace(/维吾尔自治区/g, '')
      .replace(/特别行政区/g, '')
      .replace(/自治区/g, '')
      .replace(/省/g, '')
      .replace(/市/g, '')
  }

  if (city) {
    city = city
      .replace(/自治区直辖县级行政区划/g, '')
      .replace(/地区/g, '')
      .replace(/市/g, '')
      .replace(/-/g, '')
  }
  if (province) {
    province = province.replace(/-/g, '')
  }

  let text = '-'
  if ((province || city) && province !== city) {
    text = `${province}${city ? '·' : ''}${city}`
  } else if ((province || city) && province === city) {
    text = `${province}`
  } else {
    text = '-'
  }

  return text
}

/**
 * @description: 转换网址地址显示格式
 * @param {*} website
 * @return {*}
 */
export function transformWebsite(website: string): string | undefined {
  if (website) {
    let url = website
    if (!(url.indexOf('http://') > -1 || url.indexOf('https://') > -1)) {
      url = `http://${url}`
    }
    return url
  }
}
/**
 * @description: 转换营收规模显示格式
 * @param {object} item
 * @return {*}
 */
export function transformAnnualRevenue(item: { annualRevenue: number | string }): string {
  let text = '-'
  const annualRevenue = toNumber(item.annualRevenue)
  const mapList = annualRevenueMap
  for (let index = 0; index < mapList.length; index++) {
    const { label, value } = mapList[index]
    const range = value.split('-').map(item => toNumber(item))
    if (range[0] < annualRevenue && annualRevenue < range[1]) {
      text = label
    }
  }
  return text
}

interface treeList {
  label: string
  value: string
  children?: treeList[]
}
/**
 * @description: 准备省市区的层级
 * @param {number} level
 * @return {treeList[]}
 */
export function initAreaData(level: number): treeList[] {
  let treeList: treeList[] = []

  if (level === 1) {
    treeList = Object.keys(areaDict).map(key => ({ label: key, value: key }))
  } else if (level === 2) {
    treeList = Object.keys(areaDict).map(key => {
      const level2List = areaDict[key]
      return {
        label: key,
        value: key,
        children: Object.keys(level2List).map(level2Key => ({ label: level2Key, value: level2Key }))
      }
    })
  } else if (level === 3) {
    treeList = Object.keys(areaDict).map(key => {
      const level2List = areaDict[key]
      return {
        label: key,
        value: key,
        children: Object.keys(level2List).map(level2Key => {
          const level3List = level2List[level2Key]
          return {
            label: level2Key,
            value: level2Key,
            children: level3List.map(level3Item => ({
              label: level3Item,
              value: level3Item
            }))
          }
        })
      }
    })
  }

  return treeList
}

/**
 * @description: 使用vuehookplus时候，需要修改返回值的格式的插件
 * @param {*} fetchInstance
 * @return {*}
 */
export const useFormatterPlugin: UseRequestPlugin<ResponseData, any, any> = (fetchInstance, {}) => {
  return {
    onSuccess: () => {
      fetchInstance.setFetchState(fetchInstance.state.data?.result, 'data')
    }
  }
}

/**
 * @description:
 * @param {HTMLElement} element
 * @param {string} animation
 * @param {string} prefix
 * @return {*}
 */
export const animateCSS = (element: HTMLElement | string, animation: string, prefix: string = 'animate__') =>
  new Promise((resolve, reject) => {
    if (isEmpty(element)) {
      return reject(new Error('element不能为空'))
    }
    const animationName = `${prefix}${animation}`

    const node = isString(element) ? document.querySelector<HTMLElement>(element) : element
    if (isNull(node)) {
      return reject(new Error('element获取空'))
    }

    node.classList.add(`${prefix}animated`, animationName)

    function handleAnimationEnd(event: Event) {
      event.stopPropagation()
      node!.classList.remove(`${prefix}animated`, animationName)
      resolve('Animation ended')
    }

    node.addEventListener('animationend', handleAnimationEnd, { once: true })
  })
