/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-08 16:07:42
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-03-15 10:26:27
 * @FilePath: /corp-elf-web-consumer/src/utils/auth/token.ts
 * @Description:
 */
import { getLocal, getLocalExpire, removeLocal, setLocal } from '@/utils/storage'
// import api from '@/api/api'

const TOKEN_CODE = 'access_token'
/** token过期时间：30小时 */
const DURATION = 60 * 60 * 24 * 30

export function getToken() {
  return getLocal<string>(TOKEN_CODE)
}

export function setToken(token: string) {
  setLocal(TOKEN_CODE, token, DURATION)
}

export function removeToken() {
  removeLocal(TOKEN_CODE)
}

export async function refreshAccessToken() {
  const expire: number | null = getLocalExpire(TOKEN_CODE)

  // * token没有过期时间或者token离过期时间超过30分钟则不执行刷新
  if (!expire || expire - new Date().getTime() > 1000 * 60 * 30) return

  console.log('token没有过期时间或者token离过期时间超过30分钟则不执行刷新')

  // try {
  //   if (res.code === 0) setToken(res.data.token)
  // } catch {
  //   // 无感刷新，有异常也不提示
  // }
}
