/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-29 16:13:07
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-06 11:36:19
 * @FilePath: /corp-elf-web-consumer/src/hooks/useRequest.ts
 * @Description:
 */
import { isBoolean, isEmpty } from 'lodash-es'
import { Ref, isRef, onMounted, ref } from 'vue'
import { ResponseData } from '~/types/response'

interface configType<resType> {
  /** 手动执行请求 */
  immediateReqData?: boolean
  /** 对 result 进行额外处理 */
  transformResult?: (result: resType) => resType
}

const useRequest = <reqType, resType>(
  api: (params: reqType) => Promise<ResponseData<resType>>,
  reqParams?: Ref<reqType> | reqType | {},
  config?: configType<resType>
): {
  loading: Ref<Boolean>
  dataList: Ref<resType | undefined>
  getData(): Promise<resType>
} => {
  const immediateReqData = isBoolean(config?.immediateReqData) ? config?.immediateReqData : true
  const transformResult = config?.transformResult // 获取外部传入的 transformResult 函数

  const loading = ref(false)
  const dataList = ref<resType>()

  async function getData() {
    try {
      const effectiveParams = (!isEmpty(reqParams) ? (isRef(reqParams) ? reqParams.value : reqParams) : {}) as reqType
      loading.value = true
      const { result } = await api(effectiveParams)

      // 如果传入了 transformResult 函数，则对 result 进行处理
      dataList.value = transformResult ? transformResult(result) : result

      loading.value = false
      return Promise.resolve(result)
    } catch (error) {
      loading.value = false
      console.error(error)
      return Promise.reject(error)
    }
  }

  onMounted(() => {
    if (immediateReqData) {
      getData()
    }
  })

  return { loading, dataList, getData }
}

export default useRequest
