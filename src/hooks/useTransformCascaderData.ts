/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-24 17:32:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-06 11:38:31
 * @FilePath: /corp-elf-web-consumer/src/hooks/useTransformCascaderData.ts
 * @Description: 获取搜索选中的节点,递归将子级全部取出来
 */
import { CascaderProps } from 'ant-design-vue'
import { CascaderOptionType } from 'ant-design-vue/lib/cascader'
import { isEmpty, cloneDeep } from 'lodash-es'

/** 获取搜索选中的节点,递归将子级全部取出来 */
function useTransformCascaderData(value: string[][] | undefined, selectedOptions: CascaderProps['options']): CascaderOptionType[] | [] {
  if (isEmpty(value)) {
    return []
  }
  console.log('value: ', value)
  console.log('selectedOptions: ', selectedOptions)
  // 递归将子级的value全部取出来
  const deepFor = (array: CascaderProps['options']) => {
    const childrenData: Array<CascaderOptionType> = []
    array?.forEach(item => {
      childrenData.push(item)
      if (!isEmpty(item.children)) {
        childrenData.push(...deepFor(item.children))
      }
    })
    return childrenData
  }

  // 找到value当前的层级
  const findDataByPath = (valueList: string[]) => {
    let _selectedOptions = cloneDeep(selectedOptions)
    const findchildren = (key: string, treeList: CascaderProps['options'] = []) => {
      let returnData: CascaderOptionType = []
      for (let index = 0; index < treeList.length; index++) {
        const element = treeList[index]
        if (element.value === key) {
          _selectedOptions = element.children
          returnData = element
        }
      }
      return returnData
    }

    return valueList.map(key => findchildren(key, _selectedOptions))
  }

  const allNode: Array<CascaderOptionType> = []
  value!.forEach(item => {
    const tempData = findDataByPath(item)
    const lastItem = tempData[item.length - 1]
    allNode.push(lastItem)
    if (!isEmpty(lastItem.children)) {
      allNode.push(...deepFor(lastItem.children))
    }
  })

  console.log('allNode: ', allNode)

  return allNode

  // switch (key) {
  //   case 'tagsAll':
  //     searchForm.value.tagsAll = allNode.map(item => item.label)
  //     break

  //   default:
  //     searchForm.value[key] = allNode.map(item => item.value)
  //     break
  // }
}

export { useTransformCascaderData }
