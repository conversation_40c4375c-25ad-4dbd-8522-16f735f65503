/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-14 14:53:48
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-10 11:32:52
 * @FilePath: /corp-elf-web-consumer/src/hooks/useInfiniteLoading.ts
 * @Description: 无限滚动
 */
import { computed, ComputedRef, isRef, onMounted, Ref, ref } from 'vue'
import { isBoolean, isEmpty } from 'lodash-es'
import { ResponseData } from '~/types/response'
import { paginationBaseResponse } from '~/types/common/pagination'
import { PaginationConfig } from 'ant-design-vue/es/pagination'

interface paginationParamsType extends PaginationConfig {
  maxPages: number
}

interface configType {
  pageParams?: PaginationConfig
  immediateReqData?: boolean
}

const useInfiniteLoading = <reqType, resType>(
  api: (params: reqType) => Promise<ResponseData<paginationBaseResponse<resType>>>,
  otherParams?: Ref<reqType> | reqType,
  config?: configType
): {
  loading: Ref<Boolean>
  dataList: Ref<resType[]>
  noMore: ComputedRef<Boolean>
  pageParams: Ref<paginationParamsType>
  getData(): Promise<void>
  onLoadMore(): void
  refresh(): void
} => {
  const immediateReqData = isBoolean(config?.immediateReqData) ? config?.immediateReqData : true
  const pageParams = config?.pageParams || {}

  const observerIndex = ref(0)
  const loading = ref(false)
  const dataList = ref<resType[]>([]) as Ref<resType[]>
  // const _otherParams = ref(cloneDeep(isEmpty(otherParams) ? {} : otherParams))
  const lastFetchId = ref(0)

  const paginationParams = ref<paginationParamsType>({
    current: 1, // 当前页数
    pageSize: 10, // 每页显示条目个数
    total: 0, // 总条目数
    maxPages: 0, // 最大页
    ...pageParams
  })

  // 翻页
  function handlerCurrentChange() {
    paginationParams.value.current!++
    getData()
  }

  const noMore = computed(() => !loading.value && paginationParams.value.current! >= paginationParams.value.maxPages)

  // 请求参数
  const params = computed<reqType>(() => {
    const tempOrderParams = isRef(otherParams) ? otherParams.value || {} : otherParams || {}
    return {
      pageNo: paginationParams.value.current,
      pageSize: paginationParams.value.pageSize,
      ...tempOrderParams
    } as reqType
  })

  // 获取数据
  async function getData() {
    try {
      loading.value = true
      lastFetchId.value += 1
      const fetchId = lastFetchId.value
      const { result } = await api(params.value)
      loading.value = false
      if (fetchId !== lastFetchId.value) {
        return
      }
      if (isEmpty(result)) {
        dataList.value = []
        paginationParams.value.total = 0
        paginationParams.value.maxPages = 0
        return
      }
      const { records, total, pages } = result
      dataList.value = dataList.value.concat(records)
      paginationParams.value.total = total
      paginationParams.value.maxPages = pages
    } catch (error) {
      console.error(error)
      paginationParams.value.current!--
      loading.value = false
    }
  }

  function onLoadMore() {
    if (!loading.value && !noMore.value) {
      handlerCurrentChange() // 获取第一页以外数据
      observerIndex.value++
    }
  }

  function refresh() {
    paginationParams.value.current = 1
    paginationParams.value.total = 0
    dataList.value = []

    // _otherParams.value = cloneDeep(otherParams)
    // paginationParams.value = {
    //   ...paginationParams.value,
    //   ...pageParams
    // }
    getData()
  }

  onMounted(() => {
    if (immediateReqData) {
      getData()
    }
  })

  return { onLoadMore, refresh, loading, dataList, noMore, getData, pageParams: paginationParams }
}

export default useInfiniteLoading
