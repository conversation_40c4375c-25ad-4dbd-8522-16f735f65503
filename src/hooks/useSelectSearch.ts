/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-07 15:36:57
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-18 11:37:45
 * @FilePath: /corp-elf-web-consumer/src/hooks/useSelectSearch.ts
 * @Description:
 */
import { Ref, ref } from 'vue'
import { debounce, isEmpty } from 'lodash-es'
import { ResponseData } from '~/types/response'

const useSelectSearch = <reqType, resType>(
  api: (params: reqType, controller?: AbortController) => Promise<ResponseData<resType[]>>,
  searchKey: string,
  otherParams: any = {},
  wait = 300
  // config?: configType
) => {
  let controller: AbortController | null = null
  let lastFetchId = 0
  const state = ref<{ data: resType[]; fetching: boolean }>({ data: [], fetching: false }) as Ref<{
    data: resType[]
    fetching: boolean
  }>
  /**
   * @description: 请求数据
   * @param {*} value 接口需要的参数
   * @return {*}
   */
  const fetchData = debounce(value => {
    console.log('fetching', value)
    if (isEmpty(value)) {
      return
    }
    // 取消之前的请求
    if (controller) {
      controller.abort()
    }
    controller = new AbortController()
    lastFetchId += 1
    const fetchId = lastFetchId
    state.value.data = []
    state.value.fetching = true
    api({ [searchKey]: value, ...otherParams }, controller).then(({ result }) => {
      console.log('result: ', result)
      if (fetchId !== lastFetchId) {
        // for fetch callback order
        return
      }
      const data = result
      state.value.data = data
      state.value.fetching = false
      console.log('state: ', state)
    })
  }, wait)

  return { state, fetchData }
}

export default useSelectSearch
