<template>
  <div class="stringInput">
    <!-- <a-select
      placeholder="请选择筛条件"
      style="width: 120px"
      :value="value.conditionType"
      @change="handlerConditionChange"
      :fieldNames="{ label: 'conditionName', value: 'conditionType' }"
      :options="props.conditionList"
      class="condition"
    >
    </a-select> -->

    <!-- 枚举值 -->
    <div class="tagInput">
      <!-- 枚举值 -->

      <template v-for="tag in props.value.values" :key="tag">
        <template v-if="tag.length > 20">
          <a-tooltip :title="tag" :getPopupContainer="getPopupContainer">
            <a-tag closable @close="handleClose(tag)">{{ `${tag.slice(0, 20)}...` }}</a-tag>
          </a-tooltip>
        </template>
        <template v-else>
          <a-tag closable @close="handleClose(tag)">{{ tag }}</a-tag>
        </template>
      </template>

      <template v-if="state.inputVisible"
        ><a-input
          ref="inputRef"
          v-model:value="state.inputValue"
          type="text"
          size="small"
          :style="{ width: '78px' }"
          @blur="handleInputConfirm"
          @keyup.enter="handleInputConfirm"
        />
      </template>
      <template v-else>
        <a-tag style="background: #fff; border-style: dashed" @click="showInput" class="addTag">
          <iconfontIcon icon="icon-add" />
          关键字
        </a-tag>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts" name="stringInput">
import { Form, message } from 'ant-design-vue'
import { searchFormItemConditionType, searchFormItemType, searchFormItemData } from '~/types/common/searchForm'
import { isEmpty } from 'lodash-es'
import { reactive, ref, nextTick } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'

const props = defineProps<{
  value: searchFormItemType
  conditionList: Array<searchFormItemConditionType>
}>()

const getPopupContainer = triggerNode => document.body

const formItemContext = Form.useInjectFormItemContext()
const emit = defineEmits(['update:value', 'change'])
function triggerChange(changedValue: { condition?: searchFormItemConditionType; data?: searchFormItemData }) {
  const emitData = {
    ...props.value,
    ...changedValue.condition,
    ...changedValue.data
  }

  emit('update:value', emitData)
  emit('change', emitData)
  formItemContext.onFieldChange()
}
// 筛选条件变动
function handlerConditionChange(e) {
  let conditionRes = null
  for (let index = 0; index < props.conditionList.length; index++) {
    const element = props.conditionList[index]
    if (element.conditionType === e) {
      conditionRes = element
    }
  }
  if (!isEmpty(conditionRes)) {
    triggerChange({ condition: conditionRes })
  }
}

const inputRef = ref()
const state = reactive({
  inputVisible: false,
  inputValue: ''
})
// 删除tag
function handleClose(removedTag: string) {
  const tags = props.value.values.filter(tag => tag !== removedTag)

  triggerChange({ data: { ...props.value, values: tags } })
}
// 显示tag输入框
function showInput() {
  state.inputVisible = true
  nextTick(() => {
    inputRef.value.focus()
  })
}
// tag输入框确定
function handleInputConfirm() {
  const inputValue = state.inputValue
  let tags = props.value.values

  if (isEmpty(inputValue)) {
    Object.assign(state, { inputVisible: false, inputValue: '' })
    return
  } else if (tags.includes(inputValue)) {
    message.warning('已经存在该关键字')
    return
  } else {
    if (inputValue && tags.indexOf(inputValue) === -1) {
      tags = [...tags, inputValue]
    }

    Object.assign(state, { inputVisible: false, inputValue: '' })

    triggerChange({ data: { ...props.value, values: tags } })
  }
}
</script>

<style lang="less" scoped>
.stringInput {
  display: flex;
  align-items: flex-start;

  .tagInput {
    flex: 1;
    // display: flex;
    flex-wrap: wrap;

    position: relative;
    display: inline-flex;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    line-height: 1.5714285714285714;
    background-color: #ffffff;
    background-image: none;
    border-width: 1px;
    border-style: solid;
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #d9d9d9;
      border-right-width: 1px !important;
    }

    // .ant-tag {
    //   margin-top: 2px;
    //   margin-bottom: 2px;
    // }

    .addTag {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
    }
  }
}
</style>
