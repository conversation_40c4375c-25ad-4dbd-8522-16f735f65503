<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-03 11:12:26
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-09-04 15:03:53
 * @FilePath: /corp-elf-web/src/components/searchBox/inputComps/number.vue
 * @Description: 
-->
<template>
  <div class="numInput">
    <!-- 条件 -->
    <!-- <a-select
      placeholder="请选择筛条件"
      style="width: 120px"
      :value="value.conditionType"
      @change="handlerConditionChange"
      :fieldNames="{ label: 'conditionName', value: 'conditionType' }"
      :options="props.conditionList"
      class="condition"
    >
    </a-select> -->

    <!-- 枚举值 -->
    <template v-if="isBetween">
      <a-input-number :value="props.value.nums[0]" @change="e => handlerNumChange(e, 0)" v-bind="numberInputAttr" />
      <span style="margin: 0 8px">~</span>
      <a-input-number :value="props.value.nums[1]" @change="e => handlerNumChange(e, 1)" v-bind="numberInputAttr" />
    </template>
    <template v-else>
      <a-input-number :value="props.value.nums[0]" @change="e => handlerNumChange(e, 0)" v-bind="numberInputAttr" />
    </template>
    <span style="margin-left: 10px; color: #333; line-height: 32px">
      {{ value.unitName }}
    </span>
  </div>
</template>

<script setup lang="ts" name="numInput">
import { Form } from 'ant-design-vue'
import { searchFormItemConditionType, searchFormItemType, searchFormItemData } from '~/types/common/searchForm'
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'

const props = defineProps<{
  value: searchFormItemType
  conditionList: Array<searchFormItemConditionType>
}>()

const isBetween = computed(() => props.value.conditionType === 'NBET' || props.value.conditionType === 'BET')

const numberInputAttr = computed(() => {
  const attrParams = {
    style: 'width: 180px',
    precision: 0,
    min: 1,
    max: Infinity,
    placeholder: '请输入'
  }

  if (props.value.fieldType === 'NUM_1') {
    attrParams.precision = 0
  } else if (props.value.fieldType === 'NUM_2') {
    attrParams.precision = 2
  } else if (props.value.fieldType === 'NUM_3') {
    attrParams.max = 100
    attrParams.precision = 0
  }
  return attrParams
})

const formItemContext = Form.useInjectFormItemContext()
const emit = defineEmits(['update:value', 'change'])
function triggerChange(changedValue: { condition?: searchFormItemConditionType; data?: searchFormItemData }) {
  const emitData = {
    ...props.value,
    ...changedValue.condition,
    ...changedValue.data
  }

  emit('update:value', emitData)
  emit('change', emitData)
  formItemContext.onFieldChange()
}

// 当筛选条件变动为介于的时候需要重置nums
// watch(
//   () => isBetween.value,
//   newVal => {
//     triggerChange({ data: { ...props.value.data, nums: [] } })
//   }
// )

// 筛选条件变动
function handlerConditionChange(e) {
  let conditionRes = null
  for (let index = 0; index < props.conditionList.length; index++) {
    const element = props.conditionList[index]
    if (element.conditionType === e) {
      conditionRes = element
    }
  }
  if (!isEmpty(conditionRes)) {
    triggerChange({ condition: conditionRes })
  }
}
// 输入的数值变动
function handlerNumChange(e, index) {
  const numsRes = props.value.nums
  numsRes[index] = e
  triggerChange({ data: { ...props.value, nums: numsRes } })
}
</script>

<style lang="less" scoped>
.numInput {
  display: flex;
  align-items: flex-start;
}
</style>
