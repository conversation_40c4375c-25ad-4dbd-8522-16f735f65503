<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-03 11:13:55
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 14:27:06
 * @FilePath: /corp-elf-web-consumer/src/components/searchCriteria/inputComps/boolean.vue
 * @Description: 
-->
<template>
  <div class="booleanInput">
    <a-select
      placeholder="请选择"
      style="width: 120px"
      :value="value.conditionType"
      @change="handlerConditionChange"
      :fieldNames="{ label: 'conditionName', value: 'conditionType' }"
      :options="props.conditionList"
      :class="['condition', `${props.value.fieldIndex}_input`]"
    >
    </a-select>
  </div>
</template>

<script setup lang="ts" name="booleanInput">
import { Form } from 'ant-design-vue'
import { SelectValue } from 'ant-design-vue/es/select'
import { isEmpty } from 'lodash-es'
import { searchFormItemConditionType, searchFormItemType, searchFormItemData } from '~/types/common/searchForm'

const props = defineProps<{ value: searchFormItemType; conditionList: Array<searchFormItemConditionType> }>()

const formItemContext = Form.useInjectFormItemContext()
const emit = defineEmits(['update:value', 'change'])
function triggerChange(changedValue: {
  condition?: searchFormItemConditionType
  data?: searchFormItemData
  // conditionType?: string | undefined
  // conditionValue?: boolean
  // dates?: Array<string>
  // nums?: Array<number>
  // values?: Array<string>
}) {
  const emitData = {
    ...props.value,
    ...changedValue.condition,
    ...changedValue.data
  }
  emit('update:value', emitData)
  emit('change', emitData)
  formItemContext.onFieldChange()
}

function handlerConditionChange(e: SelectValue) {
  console.log('e: ', e)
  let conditionRes = null
  for (let index = 0; index < props.conditionList.length; index++) {
    const element = props.conditionList[index]
    if (element.conditionType === e) {
      conditionRes = element
    }
  }
  if (!isEmpty(conditionRes)) {
    triggerChange({ condition: conditionRes })
  }
}
</script>

<style lang="less" scoped>
.booleanInput {
  display: flex;
  align-items: flex-start;
}
</style>
