<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-04 15:21:04
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-03-25 10:19:13
 * @FilePath: /corp-elf-web/src/components/searchBox/inputComps/id.vue
 * @Description: 
-->
<template>
  <div class="idInput">
    <!-- 枚举值 -->
    <a-input v-model:value="props.value.values[0]" style="width: 100%" placeholder="请输入" />
  </div>
</template>

<script setup lang="ts" name="idInput">
import { searchFormItemType } from '~/types/common/searchForm'
const props = defineProps<{ value: searchFormItemType }>()

// const formItemContext = Form.useInjectFormItemContext()
// const emit = defineEmits(['update:value', 'change'])
// function triggerChange(changedValue: { condition?: searchFormItemConditionType; data?: searchFormItemData }) {
//   emit('update:value', { ...props.value, ...changedValue })
//   formItemContext.onFieldChange()
// }
</script>

<style lang="less" scoped>
.idInput {
  display: flex;
  align-items: flex-start;
}
</style>
