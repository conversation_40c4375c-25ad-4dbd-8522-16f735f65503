<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-03 11:13:32
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-04 10:53:17
 * @FilePath: /corp-elf-web/src/components/searchBox/inputComps/enum.vue
 * @Description: 
-->
<template>
  <div class="enumInput">
    <!-- <a-select
      placeholder="请选择筛条件"
      style="width: 120px"
      :value="value.conditionType"
      @change="handlerConditionChange"
      :fieldNames="{ label: 'conditionName', value: 'conditionType' }"
      :options="props.conditionList"
      class="condition"
    >
    </a-select> -->

    <template v-if="isCascader">
      <a-cascader
        allowClear
        placeholder="请选择"
        :class="`${props.value.fieldIndex}_input`"
        :value="props.value.values"
        :options="props.enumsList"
        @change="handlerDataValuesChange"
        style="flex: 1; width: 100%"
        :maxTagCount="10"
        multiple
      >
      </a-cascader>
      <!-- :getPopupContainer="getPopupContainer" -->
    </template>
    <template v-else>
      <a-select
        allowClear
        placeholder="请选择"
        :class="`${props.value.fieldIndex}_input`"
        :value="props.value.values"
        :options="props.enumsList"
        @change="handlerDataValuesChange"
        style="flex: 1; width: 100%"
        mode="multiple"
        :virtual="false"
      >
        <!-- :getPopupContainer="getPopupContainer" -->
      </a-select>
    </template>
  </div>
</template>

<script setup lang="ts" name="enumInput">
import { Form } from 'ant-design-vue'
import { searchFormItemConditionType, searchFormItemType, searchFormItemData } from '~/types/common/searchForm'
import { isEmpty } from 'lodash-es'
import { CascaderOptionType } from 'ant-design-vue/lib/cascader'
import { computed } from 'vue'

const props = defineProps<{
  value: searchFormItemType
  conditionList: Array<searchFormItemConditionType>
  enumsList: Array<CascaderOptionType>
}>()

const formItemContext = Form.useInjectFormItemContext()
const emit = defineEmits(['update:value', 'change'])
function triggerChange(changedValue: { condition?: searchFormItemConditionType; data?: searchFormItemData }) {
  const emitData = {
    ...props.value,
    ...changedValue.condition,
    ...changedValue.data
  }

  emit('update:value', emitData)
  emit('change', emitData)
  formItemContext.onFieldChange()
}

const isCascader = computed(() => {
  if (isEmpty(props.enumsList)) {
    return false
  }
  return !isEmpty(props.enumsList[0].children)
}) // 判断是不是级联组件

// 筛选条件变动
function handlerConditionChange(e) {
  let conditionRes: searchFormItemConditionType
  for (let index = 0; index < props.conditionList.length; index++) {
    const element = props.conditionList[index]
    if (element.conditionType === e) {
      conditionRes = element
    }
  }
  if (!isEmpty(conditionRes)) {
    triggerChange({ condition: conditionRes })
  }
}
// 条件值变动
function handlerDataValuesChange(val) {
  const data: searchFormItemData = { ...props.value, values: val }
  triggerChange({ data })
}
//
function getPopupContainer(triggerNode) {
  // triggerNode.parentNode
  return document.body
}
</script>

<style lang="less" scoped>
.enumInput {
  display: flex;
  align-items: flex-start;
  .industry_tags_input {
    :deep(.ant-cascader-menu) {
      height: 392px;
    }
    :deep(.ant-select-selection-item) {
      max-width: 200px;
    }
  }
}
</style>
