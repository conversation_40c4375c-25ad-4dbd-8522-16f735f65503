<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-08-24 11:10:38
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-09-25 10:44:08
 * @FilePath: /corp-elf-web/src/components/searchBox/inputComps/date.vue
 * @Description: 
-->
<template>
  <div class="dateInput">
    <!-- 条件 -->
    <!-- <a-select
      placeholder="请选择筛条件"
      style="width: 120px"
      :value="value.conditionType"
      @change="handlerConditionChange"
      :fieldNames="{ label: 'conditionName', value: 'conditionType' }"
      :options="props.conditionList"
      class="condition"
    >
    </a-select> -->
    <!-- 枚举值 -->
    <template v-if="isBetween">
      <a-range-picker
        valueFormat="YYYY-MM-DD"
        style="width: 260px"
        :class="`${props.value.fieldIndex}_input`"
        @change="handlerDateChange"
        :value="props.value.dates"
        :placeholder="['请选择', '请选择']"
      ></a-range-picker>
    </template>
    <template v-else>
      <a-date-picker
        valueFormat="YYYY-MM-DD"
        style="width: 260px"
        :class="`${props.value.fieldIndex}_input`"
        @change="handlerDateChange"
        :value="props.value.dates[0]"
        placeholder="请选择"
      ></a-date-picker>
    </template>
    <!-- <a-range-picker
      valueFormat="YYYY-MM-DD"
      style="width: 260px"
    />
    <a-date-picker
      valueFormat="YYYY-MM-DD"
      style="width: 260px"
    /> -->
  </div>
</template>

<script setup lang="ts" name="dateInput">
import { Form } from 'ant-design-vue'
import { searchFormItemConditionType, searchFormItemType } from '~/types/common/searchForm'
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'

const props = defineProps<{
  value: searchFormItemType
  conditionList: Array<searchFormItemConditionType>
}>()

const formItemContext = Form.useInjectFormItemContext()
const emit = defineEmits(['update:value', 'change'])
function triggerChange(changedValue: { conditionType?: string | undefined; conditionValue?: boolean; dates?: Array<string> }) {
  console.log('changedValue: ', changedValue)
  const emitData = {
    ...props.value,
    ...changedValue
  }

  console.log('emitData: ', emitData)
  emit('update:value', emitData)
  emit('change', emitData)
  formItemContext.onFieldChange()
}

const isBetween = computed(() => props.value.conditionType === 'NBET' || props.value.conditionType === 'BET')

// 当筛选条件变动为介于的时候需要重置nums
// watch(
//   () => isBetween.value,
//   newVal => {
//     triggerChange({ data: { ...props.value, dates: [] } })
//   }
// )

// 筛选条件变动
function handlerConditionChange(e, options) {
  if (!isEmpty(options)) {
    const dates = !(props.value.conditionType === 'NBET' || props.value.conditionType === 'BET')
      ? props.value.dates.slice(0, 1)
      : props.value.dates
    triggerChange({ ...options, dates })
  }
}
function handlerDateChange(e: string | string[]) {
  const datesRes: string[] = isBetween.value ? (e as string[]) : [e as string]
  triggerChange({ dates: datesRes })
}
</script>

<style lang="less" scoped>
.dateInput {
  display: flex;
  align-items: flex-start;
}
</style>
