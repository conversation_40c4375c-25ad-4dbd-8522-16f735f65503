<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-30 14:09:31
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2022-11-03 11:49:45
 * @FilePath: /corp-elf-web/src/components/companyCard/scoreDetail/index.vue
 * @Description: 
-->
<template>
  <a-modal title="得分详情" width="600px" v-model:open="visible" @cancel="handleClose" :footer="null">
    <div class="scoreDetail">
      <p class="title">{{ title }}</p>

      <ul>
        <li v-for="(item, index) in scoreList" :key="index" :class="item.isActive ? '' : 'disabledScoreItem'">
          <iconfontIcon
            :icon="item.isActive ? 'icon-check-circle' : 'icon-close-circle'"
            :style="{ marginRight: '8px' }"
            :class="item.isActive ? 'color-#6553ee' : ''"
          />

          <span class="active">{{ item.description }}</span>
        </li>
      </ul>
    </div>
  </a-modal>
</template>

<script langt="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { defineComponent } from 'vue'

export default defineComponent({
  components: { iconfontIcon },
  data() {
    return {
      visible: false,
      title: '',
      score: '',
      scoreList: []
    }
  },
  methods: {
    onOpen({ title, score, popupDescDtos: scoreList }) {
      this.title = title
      this.score = score
      this.scoreList = scoreList
      this.visible = true
    },
    handleClose() {
      this.visible = false
    }
  }
})
</script>

<style lang="less" scoped>
.scoreDetail {
  color: #303133;
  .title {
    margin-bottom: 10px;
  }
  li {
    line-height: 2;
  }
  .disabledScoreItem {
    color: #828282;
  }
}
</style>
