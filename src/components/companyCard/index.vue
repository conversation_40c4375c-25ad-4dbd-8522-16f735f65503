<template>
  <div class="company_item">
    <slot name="left"></slot>

    <div class="logo_box">
      <a-image
        :width="80"
        :height="80"
        :src="companyInfo.entLogo ? companyInfo.entLogo : companyDefaultLogo"
        :fallback="companyDefaultLogo"
        :preview="false"
      />
    </div>

    <template v-if="companyInfo.cid">
      <div class="detail">
        <div style="flex: 1">
          <div class="companyNameBox">
            <a-space>
              <p @click.stop="openCompanyInfo" class="hoverPrimaryColor">
                <template v-if="companyInfo.companyClass === 'B'"> {{ companyInfo.entName }} ({{ companyInfo.openStatus }}) </template>
                <template v-else>
                  {{ companyInfo.entName }}
                </template>
              </p>

              <iconfont-icon icon="icon-copy" class="copyBtn hoverPrimaryColor" @click.stop="copyCompanyName(companyInfo.entName)" />

              <template v-if="companyInfo.website && companyInfo.website !== '-'">
                <a @click.stop="openWebsite(companyInfo.website)" style="display: flex; align-items: center">
                  <iconfont-icon icon="icon-link" style="font-size: 18px" />
                  官网
                </a>
              </template>
            </a-space>
          </div>

          <div class="tagBox">
            <a-tag v-for="(item, index) in companyInfo.showTags" :key="index">{{ item }}</a-tag>
            <!-- color="#EAE7FB" -->
          </div>

          <div class="bottom">
            <span class="descriptionsContent">
              <a-tooltip :getPopupContainer="getPopupContainer">
                <template #title>注册地址</template>
                <iconfont-icon icon="icon-location" style="font-size: 16px" />
                {{ transformLocation({ province: companyInfo.province || '', city: companyInfo.city || '' }) }}
              </a-tooltip>
            </span>
            <span class="descriptionsContent">
              <a-tooltip :getPopupContainer="getPopupContainer">
                <template #title>成立日期</template>
                <iconfont-icon icon="icon-calendar" style="font-size: 16px" />
                {{ companyInfo.startDate || '-' }}
              </a-tooltip>
            </span>
            <span class="descriptionsContent">
              <a-tooltip :getPopupContainer="getPopupContainer">
                <template #title>实力指数</template>
                <iconfont-icon icon="icon-paihangbang_paiming" style="font-size: 16px" />
                {{ companyInfo.powerfulRankScore || '-' }}
              </a-tooltip>
            </span>
            <span class="descriptionsContent">
              <a-tooltip :getPopupContainer="getPopupContainer">
                <template #title>营收规模</template>
                <iconfont-icon icon="icon-money-circle" style="font-size: 16px" />
                {{ transformAnnualRevenue(companyInfo) }}
              </a-tooltip>
            </span>
            <span class="descriptionsContent">
              <a-tooltip :getPopupContainer="getPopupContainer">
                <template #title>人员规模</template>
                <iconfont-icon icon="icon-usergroup" style="font-size: 16px" />
                {{ companyInfo.membersStr || '-' }}
              </a-tooltip>
            </span>
          </div>
        </div>

        <div class="matching" v-if="cardType === 'matching'">
          <template v-if="!(companyInfo.companyClass === 'A' || companyInfo.companyClass === 'B')">
            <p class="number">{{ companyInfo.matchingDegree }}</p>
            <p>匹配度</p>
          </template>
        </div>
        <div class="score" v-else-if="cardType === 'score'">
          <p class="number" @click="scoreDetailRef.onOpen(companyInfo.popupDto)">
            {{ companyInfo.popupScore }}
            <iconfont-icon icon="icon-caret-down-small"></iconfont-icon>
          </p>
          <p>自定义得分</p>
        </div>

        <div class="tags" v-else-if="cardType === 'org'">
          <a-tag
            v-for="(item, index) in companyInfo.relationOrgTypeTags"
            :key="index"
            :style="{
              backgroundColor: item.backgroundColor,
              color: item.fontColor
            }"
          >
            {{ item.tagLabel }}
          </a-tag>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="detail">
        <div style="flex: 1">
          <div class="companyNameBox">
            <p @click="openCompanyInfo" class="hoverPrimaryColor">
              {{ companyInfo.entName }}
            </p>

            <iconfont-icon icon="icon-copy" class="copyBtn hoverPrimaryColor" @click.stop="copyCompanyName(companyInfo.entName)" />
          </div>
        </div>

        <div class="tags" v-if="cardType === 'org'">
          <a-tag
            v-for="(item, index) in companyInfo.relationOrgTypeTags"
            :key="index"
            :style="{
              backgroundColor: item.backgroundColor,
              color: item.fontColor
            }"
          >
            {{ item.tagLabel }}
          </a-tag>
        </div>
      </div>
    </template>

    <ScoreDetail ref="scoreDetailRef" />
  </div>
</template>

<script setup lang="ts" name="CompanyCard">
import companyDefaultLogo from '@/assets/icon/companyDefaultLogo.svg'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { companyCardType } from '~/types/common/companyCardType'
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import ScoreDetail from './scoreDetail/index.vue'
import { transformLocation, transformAnnualRevenue } from '@/utils/util'
import { useClipboard } from '@vueuse/core'

const emit = defineEmits(['openCompanyInfo'])
const props = withDefaults(defineProps<{ companyInfo: companyCardType; cardType?: 'matching' | 'score' | 'org' }>(), {
  cardType: 'matching'
})
const getPopupContainer = (_triggerNode: HTMLElement) => document.body

// ref
const scoreDetailRef = ref()

// 处理点击企业名称
function openCompanyInfo() {
  console.log('props.companyInfo: ', props.companyInfo)
  emit('openCompanyInfo', props.companyInfo)
}
function copyCompanyName(name: string) {
  const { copy } = useClipboard({ legacy: true })
  copy(name)
    .then(() => {
      message.success('复制成功')
    })
    .catch(err => {
      console.error(err)
      message.error('复制失败')
    })
}
// 点击打开企业官网
function openWebsite(website: string) {
  console.log('website: ', website)
  if (website) {
    let url = website
    if (!(url.includes('https://') || url.includes('http://'))) {
      url = `http://${url}`
    }
    window.open(url, '_blank')
  }
}
</script>

<style lang="less" scope>
.company_item {
  padding: 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e7e7e7;
  .back_btn {
    i {
      font-size: 22px;
    }
  }

  .logo_box {
    margin: 0 16px;
    border-radius: 4px;
    overflow: hidden;
  }
  .detail {
    display: flex;
    align-items: center;
    flex: 1;

    .companyNameBox {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      p {
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.85);
      }

      .copyBtn {
        color: #777777;
        font-size: 18px;
      }
    }

    .tagBox {
      margin-bottom: 10px;
    }

    .bottom {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      .descriptionsContent {
        + .descriptionsContent {
          margin-left: 8px;
        }
        width: calc(20% - 10px);
        display: inline-block;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.5);
      }
    }

    .matching,
    .score {
      min-width: 66px;
      text-align: center;
      padding: 8px 12px;
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: var(--g-primary-color);
      p {
        font-size: 14px;
      }

      .number {
        font-style: normal;
        font-weight: 400;
        font-size: 24px;
        line-height: 20px;

        cursor: pointer;
        margin-bottom: 8px;
        i {
          font-size: 14px;
        }
      }
    }

    .matching {
      .number {
        cursor: inherit;
      }
    }

    .tags {
      width: 100px;
      .ant-tag {
        margin: 0 8px 8px 0;
      }
    }
  }
}
</style>
