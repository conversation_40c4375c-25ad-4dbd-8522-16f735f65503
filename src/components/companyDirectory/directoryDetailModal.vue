<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-06 16:04:53
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-12 16:15:36
 * @FilePath: /corp-elf-web-consumer/src/components/companyDirectory/directoryDetailModal.vue
 * @Description: 
-->
<template>
  <a-modal v-model:open="visible" :footer="null" :title="props.title" :width="800" @cancel="handleClose">
    <div class="mb16px flex items-center justify-between">
      <!-- <a-space> -->
      <a-input placeholder="搜索企业" v-model:value.trim="searchValue" style="width: 300px" @change="handlerSearchChange"></a-input>
      <!-- <a-button @click="followCompany('all')">一键全部关注</a-button> -->
      <a-button @click="followCompany()">关注</a-button>
      <!-- </a-space> -->
    </div>

    <a-table
      rowKey="companyUniId"
      size="small"
      :dataSource="dataList"
      :columns="columns"
      :loading="loading"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :pagination="{
        ...pageParams,
        onChange: handlerSizeChange
      }"
      :scroll="{ y: tableHeight }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'companyName'">
          <div class="flex">
            <span class="ellipsis hoverPrimaryColor" @click="handlerCompanyNameClick(record)">{{ text }}</span>
            <div v-if="!isEmpty(record['keywords'])" class="flex-1 pl-4px">
              <a-tag> {{ record['keywords'] }}</a-tag>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'address'">
          {{ transformLocation(record) }}
        </template>

        <!-- <template v-if="column.dataIndex === 'events'">
          <a-typography-text type="secondary" :ellipsis="{ tooltip: text }" :content="text" />
        </template> -->
      </template>
    </a-table>
  </a-modal>

  <AddFollowModal ref="addFollowModalRef" @refresh="handlerRefresh" />
</template>

<script setup lang="ts" name="directoryDetailModal">
import { rankIndustryRankDetail } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { IndustryRankDetailRequest, IndustryRankDetailResponse } from '~/types/api/rank/industryRankDetail'
import { message } from 'ant-design-vue'
import { debounce, isEmpty } from 'lodash-es'
import { transformLocation } from '@/utils/util'
import { useWindowSize } from '@vueuse/core'
import AddFollowModal from '@comp/addFollowModal/index.vue'

const props = defineProps<{ detailRequestParams: IndustryRankDetailRequest; title: string | undefined }>()
const router = useRouter()
const visible = ref(false)
const selectedRowKeys = ref<string[]>([])
const selectedRow = ref<IndustryRankDetailResponse[]>([])
const addFollowModalRef = ref<InstanceType<typeof AddFollowModal>>()
const columns = [
  { title: '企业名称', dataIndex: 'companyName' },
  // { title: '近期动态', dataIndex: 'events', width: 240, ellipsis: true }
  { title: '地区', dataIndex: 'address', slotName: 'address', width: '120px', ellipsis: true }
]
const searchValue = ref(undefined)
const handlerSearchChange = debounce(_val => refresh(), 300)
const params = computed(() => ({ companyName: searchValue.value, ...props.detailRequestParams }))
const { dataList, loading, pageParams, getData, refresh } = useListLoading(rankIndustryRankDetail, params, {
  immediateReqData: false,
  pageParams: { pageSizeOptions: ['10', '20', '30'], pageSize: 20 }
})

const { height } = useWindowSize()
const tableHeight = computed(() => {
  const temp = height.value - 450
  return temp < 500 ? 500 : temp
})

/**
 * @description: 分页改变
 * @param {*} pageNo
 * @param {*} pageSize
 * @return {*}
 */
function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  selectedRowKeys.value = []
  getData()
}

/**
 * @description: 打开企业详情
 * @return {*} companyItem
 */
function handlerCompanyNameClick(companyItem: IndustryRankDetailResponse) {
  console.log('companyItem: ', companyItem)
  visible.value = false
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: companyItem.companyUniId,
      companyName: companyItem.companyName
    }
  })
}

const onSelectChange = (_selectedRowKeys: string[], _selectedRow: IndustryRankDetailResponse[]) => {
  // console.log('_selectedRow: ', _selectedRow)
  // console.log('selectedRowKeys changed: ', _selectedRowKeys)
  selectedRow.value = _selectedRow
  selectedRowKeys.value = _selectedRowKeys
}

// 关注企业
function followCompany() {
  if (selectedRow.value.length === 0) {
    message.warning('请选择企业')
    return
  }

  const companyList = selectedRow.value.map(item => ({ companyName: item.companyName, companyId: item.companyUniId }))
  console.log('companyList: ', companyList)
  addFollowModalRef.value?.show(companyList)
}

function handlerRefresh() {
  selectedRow.value = []
  selectedRowKeys.value = []
  refresh()
}

function onOpen() {
  visible.value = true
  getData()
}

function handleClose() {
  visible.value = false
  selectedRowKeys.value = []
}

defineExpose({ onOpen })
</script>

<style scoped></style>
