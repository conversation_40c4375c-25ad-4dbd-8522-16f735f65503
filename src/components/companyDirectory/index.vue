<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-06 15:26:17
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-09 17:37:47
 * @FilePath: /corp-elf-web-consumer/src/components/companyDirectory/index.vue
 * @Description: 企业名录企业列表
-->
<template>
  <a-spin :spinning="loading">
    <a-table rowKey="companyUniId" size="small" :dataSource="dataList" :columns="columns" :pagination="false">
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'companyName'">
          <div class="flex">
            <span class="ellipsis hoverPrimaryColor" @click="handlerCompanyNameClick(record)">{{ text }}</span>

            <div v-if="!isEmpty(record['keywords'])" class="flex-1 pl-4px">
              <a-tag> {{ record['keywords'] }}</a-tag>
            </div>
          </div>
        </template>
        <!-- <template v-if="column.dataIndex === 'events'">
          <template v-if="text.split('、').length > 1">
            <a-tooltip :getPopupContainer="getPopupContainer">
              <template #title>{{ text }}</template>
              <span class="fs-14 secondaryText">{{ text.split('、')[0] }}...</span>
            </a-tooltip>
          </template>
          <template v-else>
            <span class="fs-14 secondaryText">{{ text }}</span>
          </template>
        </template> -->
        <template v-if="column.dataIndex === 'address'">
          {{ transformLocation(record) }}
        </template>
      </template>
    </a-table>
    <div class="mt16px text-center">
      <a-typography-text v-if="pageParams.total! > 10" type="secondary" class="hoverPrimaryColor fs-16px" @click="openDetailModal">
        查看全部
      </a-typography-text>
    </div>
  </a-spin>

  <detailModal ref="detailModalRef" :detail-request-params="params" :title="props.rankTitle" />
</template>

<script setup lang="ts" name="companyDirectoryTable">
import { rankIndustryRankDetail } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { router } from '@/router'
import { isEmpty } from 'lodash-es'
import { ref, toRef, watch } from 'vue'
import { IndustryRankDetailRequest, IndustryRankDetailResponse } from '~/types/api/rank/industryRankDetail'
import detailModal from './directoryDetailModal.vue'
import { transformLocation } from '@/utils/util'
import { useUserStore } from '@/store'
import { useEventBus } from '@vueuse/core'

const props = defineProps<{ rankTitle: string | undefined; reqParams: IndustryRankDetailRequest }>()
// const detailModalRef = ref()
const detailModalRef = ref<InstanceType<typeof detailModal>>()

const columns = [
  { title: '企业名称', dataIndex: 'companyName' },
  // { title: '近期动态', dataIndex: 'events', width: '120px', ellipsis: true }
  { title: '地区', dataIndex: 'address', slotName: 'address', width: '120px', ellipsis: true }
]
// const params = computed<IndustryRankDetailRequest>(() => ({ rankIdList: props.rankIdList, industryId: props.industryId })) // 请求参数
const params = toRef(props, 'reqParams')
const { dataList, loading, pageParams, getData } = useListLoading(rankIndustryRankDetail, params, {
  immediateReqData: false
})

// 监听参数变动，发起请求
watch(
  () => params.value.industryId,
  () => (dataList.value = [])
)
watch(
  () => params.value.rankIdList,
  newVal => {
    if (newVal.length !== 0) {
      getData()
    }
  },
  { deep: true }
)

/**
 * @description: 打开企业详情
 * @return {*} companyItem
 */
function handlerCompanyNameClick(companyItem: IndustryRankDetailResponse) {
  console.log('companyItem: ', companyItem)
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: companyItem.companyUniId,
      companyName: companyItem.companyName
    }
  })
}

const userStore = useUserStore()
const { emit: openVipModal } = useEventBus('openVipModal')

function openDetailModal() {
  if (!userStore.isVip) {
    openVipModal()
    return
  }
  detailModalRef.value?.onOpen()
}
</script>

<style scoped></style>
