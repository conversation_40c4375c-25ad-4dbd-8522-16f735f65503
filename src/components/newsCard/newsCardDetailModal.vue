<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-06 14:18:37
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 14:59:02
 * @FilePath: /corp-elf-web-consumer/src/components/newsCard/newsCardDetailModal.vue
 * @Description: 
-->
<template>
  <a-modal width="1200px" v-model:open="visible" @cancel="handlerCancel" :footer="null" destroyOnClose>
    <template #title>
      <div class="pr1em fs-18px fw500">{{ newsData?.newsTitle }}</div>
    </template>
    <div class="newsCardDetailModal mb8px">
      <!-- <a-popover v-for="item in newsData?.customerCollectDetails" :key="item.collectId">
        <a-tag class="mr8px !fs-12px">
          {{ item.collectName }}
          <CaretRightOutlined />
        </a-tag>
        <template #content>
          <a-tag
            v-for="companyTag in item.relations"
            :key="companyTag.companyId"
            class="hoverPrimaryColor"
            @click="openCompanyInfo(companyTag)"
            >{{ companyTag.companyName }}</a-tag
          >
        </template>
      </a-popover> -->

      <EllipsisTag
        v-for="(item, index) in customerTagList"
        :key="index"
        :max="1"
        :tagList="item.relations"
        :startExtraText="item.collectName"
        @tagClick="openCompanyInfo"
      />

      <!-- <EllipsisTag :max="1" :tagList="industriesTagList" /> -->
      <EllipsisTag :max="1" :tagList="eventsTagList" />
    </div>

    <a-space class="mb8px">
      <!-- 发布人、来源 -->
      <a-typography-text type="secondary" :content="newsData?.newsDataFrom" />
      <!-- 时间 -->
      <a-typography-text type="secondary" :content="newsData?.publishDate" />
      <!-- 原文链接 -->
      <a :href="oriUrl" target="_blank">查看原文</a>
    </a-space>

    <a-row :gutter="16">
      <!-- 文章 -->
      <a-col :span="!isEmpty(relationsCompany) ? 18 : 24">
        <p
          ref="newsContent"
          style="white-space: pre-wrap"
          class="h600px overflow-auto wrap font-size-18px"
          v-html="newsData?.summary"
        ></p>
      </a-col>
      <a-col v-if="!isEmpty(relationsCompany)" :span="6">
        <p>关联企业名称</p>
        <a-table size="small" :dataSource="relationsCompany" :columns="columns" :pagination="false" :scroll="{ y: 500 }">
          <template #bodyCell="{ text, record, column }">
            <template v-if="column.dataIndex === 'companyName'">
              <div class="flex">
                <p class="ellipsis hoverPrimaryColor mr8px" @click="openCompanyInfo(record)">
                  {{ text }}
                </p>
                <div class="flex-1" v-if="record.tag">
                  <a-tag> {{ record.tag }}</a-tag>
                </div>
              </div>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script setup lang="ts" name="NewsCardDetailModal">
import { isEmpty } from 'lodash-es'
import { computed, ref } from 'vue'
import { HotNewsResponse, CompanyList } from '~/types/api/industry/hotNews'
import { useScroll } from '@vueuse/core'
import EllipsisTag from '@/components/ellipsisTag/index.vue'
import { useRouter } from 'vue-router'
import { tagType } from '~/types/common/tagType'

const props = defineProps<{ collectId?: string }>()
const router = useRouter()
const visible = ref(false)
const newsData = ref<HotNewsResponse | undefined>()
function show(_newsData: HotNewsResponse) {
  newsData.value = _newsData
  visible.value = true
}

// 监听文章滚动
const newsContent = ref<HTMLElement | null>(null)
const { y } = useScroll(newsContent)

// 关联企业数据
const relationsCompany = computed<CompanyList[]>(() => newsData.value?.companyList || [])
const columns = [{ title: '企业名称', dataIndex: 'companyName', ellipsis: true }]

// 用户自定义标签
const customerTagList = computed<{ collectName: string; relations: tagType[] }[]>(() => {
  const customerCollectDetails = newsData.value?.customerCollectDetails || []
  if (isEmpty(customerCollectDetails)) {
    return []
  }
  const temp = customerCollectDetails
    .map(item => ({
      ...item,
      relations: item.relations.map(relationItem => ({ tagValue: relationItem.companyId, tagLabel: relationItem.companyName }))
    }))
    .sort(a => (a.collectId === props.collectId ? -1 : 0))

  return temp
})
// 行业tag
// const industriesTagList = computed<tagType[]>(() => {
//   const industries = newsData.value?.industries
//   console.log('industries: ', industries)
//   if (isEmpty(industries)) {
//     return []
//   }
//   return industries!.map(item => ({
//     tagValue: item,
//     tagLabel: item
//   }))
// })
// 事件tag
const eventsTagList = computed<tagType[]>(() => {
  const events = newsData.value?.events
  console.log('events: ', events)
  if (isEmpty(events)) {
    return []
  }
  return events!.map(item => ({
    tagValue: item,
    tagLabel: item
    // fontColor: 'red', backgroundColor: '#fff'
  }))
})

// 外链url，判断有误http头
const oriUrl = computed(() => {
  let url = newsData.value?.oriUrl || ''
  if (!(url.includes('https://') || url.includes('http://'))) {
    url = `http://${url}`
  }
  return url
})

function handlerCancel() {
  visible.value = false
  y.value = 0 // 文章内容回到顶部
}

function openCompanyInfo(item: tagType) {
  console.log('item: ', item)
  router
    .push({
      name: 'companyInfo-index',
      path: '/companyInfo/index',
      query: {
        companyId: item.tagValue,
        companyName: item.tagLabel
      }
    })
    .then(() => {
      handlerCancel()
    })
}

defineExpose({ show })
</script>

<style lang="less"></style>
