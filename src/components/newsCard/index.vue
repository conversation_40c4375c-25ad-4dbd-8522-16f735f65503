<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-19 22:01:57
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-13 11:23:07
 * @FilePath: /corp-elf-web-consumer/src/components/newsCard/index.vue
 * @Description: 新闻卡片
-->
<template>
  <div class="newsCard" @click="openNewsDetail">
    <a class="block newsTitle fs-16px mb8px font-600 lh-1.5em ellipsis">{{ newsData.newsTitle }}</a>
    <!-- <div class="fs-16px mb8px ellipsis-8 colorTextSecondary">{{ newsData.summary.trim() }}</div> -->
    <text-clamp :max-lines="mainContentMaxLines" :text="newsData.summary.trim()" class="fs-16px mb8px colorTextSecondary"> </text-clamp>

    <div class="mb8px">
      <EllipsisTag
        v-for="(item, index) in customerTagList"
        :key="index"
        :max="1"
        :tagList="item.relations"
        :startExtraText="item.collectName"
        @tagClick="openCompanyInfo"
      />
      <EllipsisTag :max="1" :tagList="eventsTagList" />
      <template v-if="!isEmpty(newsData.rankList)">
        <a class="fs-14px">相关企业名录{{ newsData.rankList.length }}个</a>
      </template>
    </div>

    <a-space>
      <!-- 发布人、来源 -->
      <div class="fs-14px colorTextSecondary">{{ newsData.newsDataFrom }}</div>
      <!-- 时间 -->
      <div class="fs-14px colorTextSecondary">{{ newsData.publishDate }}</div>
    </a-space>
  </div>
</template>

<script setup lang="ts" name="newsCard">
import { computed, toRefs } from 'vue'
import { isEmpty } from 'lodash-es'
import EllipsisTag from '@/components/ellipsisTag/index.vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/store'
import { HotNewsResponse } from '~/types/api/industry/hotNews'
import { theme } from 'ant-design-vue'
import { tagType } from '~/types/common/tagType'

const { getColorPrimary } = useThemeStore()
const { useToken } = theme
const { token: themeToken } = useToken()
const emit = defineEmits(['click'])

const props = withDefaults(defineProps<{ newsData: HotNewsResponse; collectId?: string; mainContentMaxLines?: number }>(), {
  mainContentMaxLines: 8
})
const { newsData } = toRefs(props)

// 用户自定义标签
const customerTagList = computed<{ collectName: string; relations: tagType[] }[]>(() => {
  const { customerCollectDetails } = newsData.value
  if (isEmpty(customerCollectDetails)) {
    return []
  }
  const temp = customerCollectDetails
    .map(item => ({
      ...item,
      relations: item.relations.map(relationItem => ({ tagValue: relationItem.companyId, tagLabel: relationItem.companyName }))
    }))
    .sort(a => (a.collectId === props.collectId ? -1 : 0))
  return temp
})

// 行业tag
// const industriesTagList = computed<tagType[]>(() => {
//   const { industries } = newsData.value
//   if (isEmpty(industries)) {
//     return []
//   }
//   return industries.map(item => ({
//     tagValue: item,
//     tagLabel: item
//   }))
// })
// 事件tag
const eventsTagList = computed<tagType[]>(() => {
  const { newsTopics } = newsData.value
  if (isEmpty(newsTopics)) {
    return []
  }
  return newsTopics.map(item => ({
    tagValue: item,
    tagLabel: item
    // fontColor: 'red', backgroundColor: '#fff'
  }))
})

// 打开新闻原文连接

function openNewsDetail() {
  emit('click', newsData.value)
}

const router = useRouter()
function openCompanyInfo(item: tagType) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.tagValue,
      companyName: item.tagLabel
    }
  })
}
</script>

<style lang="less" scoped>
.newsCard {
  padding: 16px;
  &:hover {
    cursor: pointer;
    background-color: #f4f0ff;

    .newsTitle {
      color: v-bind(getColorPrimary);
    }
  }

  + .newsCard {
    border-top: 1px solid #e7e7e7;
  }

  .newsTitle {
    color: v-bind('themeToken.colorText');
    &:hover {
      color: v-bind('themeToken.colorLinkHover');
    }
  }

  .colorTextSecondary {
    color: v-bind('themeToken.colorTextTertiary');
  }
}
</style>
