<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-25 18:13:13
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-04-24 15:48:24
 * @FilePath: /corp-elf-web-consumer/src/components/methodologyModal/index.vue
 * @Description: 
-->
<template>
  <a-modal
    ref="modalRef"
    v-model:open="openModal"
    :footer="null"
    :width="1400"
    :closable="false"
    :keyboard="false"
    @cancel="handlerModalCancel"
  >
    <!-- centered -->
    <div class="methodologyModal">
      <div class="closeIcon cursor-pointer" @click="handlerModalCancel">
        <CloseCircleOutlined :style="{ fontSize: '20px' }" />
      </div>
    </div>

    <Methodology />

    <template #modalRender="{ originVNode }">
      <div class="mt64px">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { CloseCircleOutlined } from '@ant-design/icons-vue'
import Methodology from './methodology.vue'
import { getLocal, setLocal } from '@/utils/storage'
import { isNull } from 'lodash-es'
import DeviceDetector from 'device-detector-js'

const emits = defineEmits(['modelClose'])

const openModal = ref(false)
const STORE_KEY = 'isShowMethodologyModal' // 缓存的key

// 检测设备
const deviceDetector = new DeviceDetector()
const userAgent = window.navigator.userAgent
const device = deviceDetector.parse(userAgent)

onMounted(() => {
  const isShowMethodologyModal = getLocal<boolean>(STORE_KEY)
  if ((isNull(isShowMethodologyModal) || isShowMethodologyModal === false) && device.device?.type === 'desktop') {
    setLocal(STORE_KEY, true)
    openModal.value = true
  }
})

function handlerModalCancel() {
  openModal.value = false
  emits('modelClose')
}
</script>

<style lang="less" scoped>
.methodologyModal {
  overflow: hidden;
  padding: 16px;
  border-radius: 8px;
  background: rgb(255, 255, 255);

  // 关闭按钮的样式
  .closeIcon {
    position: absolute;
    top: -20px;
    right: -20px;
    color: #fff;
  }
}
</style>
