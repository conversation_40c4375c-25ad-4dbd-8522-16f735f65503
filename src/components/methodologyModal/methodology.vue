<template>
  <div class="methodologyContent">
    <p class="titleText">数字情报驱动营销决策</p>

    <div class="flex items-start justify-center">
      <div class="borderWrapper">
        <div class="borderTitle">
          <p>市场洞察</p>
        </div>

        <div class="methodologyContent_item">
          <div>
            <img
              src="@/assets/images/methodology/看市场.png"
              srcset="
                @/assets/images/methodology/看市场.png,
                @/assets/images/methodology/看市场@2x.png 2x,
                @/assets/images/methodology/看市场@3x.png 3x
              "
            />
            <a-button
              v-if="props.isShowLinkButton"
              class="m-auto mt28px flex items-center"
              style="border-color: rgb(101, 83, 238); color: rgb(101, 83, 238)"
              @click="goPage('行业洞察')"
            >
              行业洞察
              <iconfontIcon icon="icon-arrow-right" :extra-common-props="{ class: ['!fs-16px'] }" />
            </a-button>
          </div>
          <img
            src="@/assets/images/methodology/看市场箭头.png"
            srcset="
              @/assets/images/methodology/看市场箭头.png,
              @/assets/images/methodology/看市场箭头@2x.png 2x,
              @/assets/images/methodology/看市场箭头@3x.png 3x
            "
            class="mt20px max-w72px"
          />
        </div>

        <div class="methodologyContent_item">
          <div>
            <img
              src="@/assets/images/methodology/看客户.png"
              srcset="
                @/assets/images/methodology/看客户.png,
                @/assets/images/methodology/看客户@2x.png 2x,
                @/assets/images/methodology/看客户@3x.png 3x
              "
            />
            <a-button
              v-if="props.isShowLinkButton"
              class="m-auto mt28px flex items-center"
              style="border-color: rgba(16, 147, 161, 0.72); color: rgba(16, 147, 161, 0.72)"
              @click="goPage('我的客户')"
            >
              我的客户
              <iconfontIcon icon="icon-arrow-right" :extra-common-props="{ class: ['!fs-16px'] }" />
            </a-button>
          </div>
        </div>
      </div>

      <img
        src="@/assets/images/methodology/看客户箭头.png"
        srcset="
          @/assets/images/methodology/看客户箭头.png,
          @/assets/images/methodology/看客户箭头@2x.png 2x,
          @/assets/images/methodology/看客户箭头@3x.png 3x
        "
        class="max-w72px mt86px"
      />

      <div class="borderWrapper">
        <div class="borderTitle">
          <p>市场转化</p>
        </div>

        <div class="methodologyContent_item">
          <div>
            <img
              src="@/assets/images/methodology/看机会.png"
              srcset="
                @/assets/images/methodology/看机会.png,
                @/assets/images/methodology/看机会@2x.png 2x,
                @/assets/images/methodology/看机会@3x.png 3x
              "
            />
            <a-button
              v-if="props.isShowLinkButton"
              class="m-auto mt28px flex items-center"
              style="border-color: rgb(67, 133, 236); color: rgb(67, 133, 236)"
              @click="goPage('客户动态')"
            >
              客户动态
              <iconfontIcon icon="icon-arrow-right" :extra-common-props="{ class: ['!fs-16px'] }" />
            </a-button>
          </div>
          <img
            src="@/assets/images/methodology/看机会箭头.png"
            srcset="
              @/assets/images/methodology/看机会箭头.png,
              @/assets/images/methodology/看机会箭头@2x.png 2x,
              @/assets/images/methodology/看机会箭头@3x.png 3x
            "
            class="mt20px max-w72px"
          />
        </div>

        <div class="methodologyContent_item">
          <div>
            <img
              src="@/assets/images/methodology/看伙伴.png"
              srcset="
                @/assets/images/methodology/看伙伴.png,
                @/assets/images/methodology/看伙伴@2x.png 2x,
                @/assets/images/methodology/看伙伴@3x.png 3x
              "
            />
            <a-button
              v-if="props.isShowLinkButton"
              class="m-auto mt28px flex items-center"
              style="border-color: rgb(236, 154, 58); color: rgb(236, 154, 58)"
              @click="goPage('客户圈子')"
            >
              客户圈子
              <iconfontIcon icon="icon-arrow-right" :extra-common-props="{ class: ['!fs-16px'] }" />
            </a-button>
          </div>
          <img
            src="@/assets/images/methodology/看伙伴箭头.png"
            srcset="
              @/assets/images/methodology/看伙伴箭头.png,
              @/assets/images/methodology/看伙伴箭头@2x.png 2x,
              @/assets/images/methodology/看伙伴箭头@3x.png 3x
            "
            class="mt20px max-w72px"
          />
        </div>

        <div class="methodologyContent_item">
          <div>
            <img
              src="@/assets/images/methodology/办活动.png"
              srcset="
                @/assets/images/methodology/办活动.png,
                @/assets/images/methodology/办活动@2x.png 2x,
                @/assets/images/methodology/办活动@3x.png 3x
              "
            />
            <a-button
              v-if="props.isShowLinkButton"
              class="m-auto mt28px flex items-center"
              style="border-color: rgb(243, 109, 119); color: rgb(243, 109, 119)"
            >
              期待更多
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '../tools/iconfontIcon'
import { useRouter } from 'vue-router'

const router = useRouter()
const props = withDefaults(defineProps<{ isShowLinkButton?: boolean }>(), { isShowLinkButton: false })

function goPage(type: string) {
  console.log('type: ', type)
  switch (type) {
    case '行业洞察':
      router.push({ path: '/industryInsights' })
      break
    case '我的客户':
      router.push({ path: '/dynamics/index' })
      break
    case '客户动态':
      router.push({ path: '/dynamics/index' })
      break
    case '客户圈子':
      router.push({ path: '/dynamics/index' })
      break
    default:
      break
  }
}
</script>

<style lang="less" scoped>
.methodologyContent {
  margin-bottom: 20px;
  .titleText {
    padding: 0px;
    margin: 0px 10px 46px;
    color: rgb(127, 107, 199);
    font-size: 36px;
    letter-spacing: 0px;
    text-align: center;
  }

  .borderWrapper {
    border: 2px #5e5e5e33 dashed;
    border-radius: 8px;
    position: relative;
    display: flex;
    align-items: flex-start;
    padding: 64px 4px 40px;

    .borderTitle {
      border-radius: 8px;
      background: #ffffff;
      position: absolute;
      text-align: center;
      padding: 6px 14px;
      top: -24px;
      left: calc(50% - 60px);

      p {
        color: rgba(84, 78, 78, 0.9);
        font-size: 22px;
      }
    }

    .methodologyContent_item {
      display: flex;
      align-items: flex-start;
      div {
        max-width: 207px;
      }
    }
  }
}
</style>
