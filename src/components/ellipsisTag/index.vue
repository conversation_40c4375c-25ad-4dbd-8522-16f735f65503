<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-28 19:02:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 10:38:19
 * @FilePath: /corp-elf-web-consumer/src/components/ellipsisTag/index.vue
 * @Description: 
-->
<template>
  <div class="ellipsisTag">
    <template v-if="props.type === 'simple'">
      <!-- 有tag是省略状态 -->
      <template v-if="isShowEllipsisTagList">
        <a-tag v-for="(item, index) in dropRight(showTagList)" :key="index">{{ item }}</a-tag>
        <a-popover v-if="isShowEllipsisTagList" :getPopupContainer="getPopupContainer" placement="bottom">
          <a-tag v-for="(item, index) in [last(showTagList)]" :key="index">{{ item }}</a-tag>
          <template #content>
            <div class="ellipsisTagList">
              <a-tag v-for="(item, index) in ellipsisTagList" :key="index">{{ item }}</a-tag>
            </div>
          </template>
        </a-popover>
      </template>
      <!-- 没有tag是省略状态 -->
      <template v-else>
        <a-tag v-for="(item, index) in showTagList" :key="index">{{ item }}</a-tag>
      </template>
    </template>

    <template v-else>
      <!-- 有tag是省略状态 -->
      <template v-if="isShowEllipsisTagList">
        <a-tag
          v-for="item in dropRight(showTagList)"
          :key="item.tagValue"
          @click.stop="clickTag(item)"
          :color="item.backgroundColor"
          :style="{ color: item.fontColor }"
        >
          {{ props.startExtraText }}
          {{ item.tagLabel }}
          {{ props.endExtraText }}
        </a-tag>

        <a-popover :getPopupContainer="getPopupContainer" placement="bottom" v-if="showTagList.length !== 0">
          <a-tag
            v-for="item in [last(showTagList)]"
            :key="item?.tagValue"
            @click.stop="clickTag(item!)"
            :color="item?.backgroundColor"
            :style="{ color: item?.fontColor }"
          >
            {{ props.startExtraText }}
            {{ item?.tagLabel }}
            {{ props.endExtraText }}

            <iconfontIcon icon="icon-chevron-right" :extraCommonProps="{ style: { fontSize: '12px' } }" />
          </a-tag>
          <template #content>
            <div class="ellipsisTagList">
              <a-tag
                v-for="item in ellipsisTagList"
                :key="item.tagValue"
                @click.stop="clickTag(item)"
                :color="item.backgroundColor"
                :style="{ color: item.fontColor }"
              >
                {{ props.startExtraText }}
                {{ item.tagLabel }}
                {{ props.endExtraText }}
              </a-tag>
            </div>
          </template>
        </a-popover>
      </template>
      <!-- 没有tag是省略状态 -->
      <template v-else>
        <a-tag
          v-for="item in showTagList"
          :key="item.tagValue"
          @click.stop="clickTag(item)"
          :color="item.backgroundColor"
          :style="{ color: item.fontColor }"
        >
          {{ props.startExtraText }}
          {{ item.tagLabel }}
          {{ props.endExtraText }}
        </a-tag>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import { VNode, computed, onMounted, ref } from 'vue'
import iconfontIcon from '../tools/iconfontIcon'
import { dropRight, last } from 'lodash-es'
import { tagType } from '~/types/common/tagType'

const emit = defineEmits(['tagClick'])
const props = withDefaults(
  defineProps<{
    max?: number
    tagList: tagType[]
    startExtraText?: string | VNode
    endExtraText?: string | VNode
    type?: 'simple' | 'default'
  }>(),
  { max: 3, type: 'default' }
)
const getPopupContainer = (_triggerNode: HTMLElement) => document.body

const showTagList = ref<tagType[]>([])
const ellipsisTagList = ref<tagType[]>([])

const isShowEllipsisTagList = computed(() => ellipsisTagList.value.length !== 0)

function clickTag(tagItem: tagType) {
  emit('tagClick', tagItem)
}

onMounted(() => {
  if (props.tagList && props.tagList.length !== 0) {
    const chunkArr = [props.tagList.slice(0, props.max), props.tagList.slice(props.max)]
    showTagList.value = chunkArr[0] || []
    ellipsisTagList.value = chunkArr[1] || []
  }
})
</script>

<style lang="less" scoped>
.ellipsisTag {
  display: inline;

  .ant-tag {
    cursor: pointer;
    font-size: 12px !important;
  }
}
.ellipsisTagList {
  max-width: 500px;
  .ant-tag {
    cursor: pointer;
    margin: 4px 8px 4px 0;
  }
}
</style>
