<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-10 15:59:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-19 14:59:27
 * @FilePath: /corp-elf-web-consumer/src/components/actionIcon/more.vue
 * @Description: 
-->
<template>
  <div class="moreIcon transition-all">
    <!-- placement="bottomRight"
     -->
    <a-dropdown
      :trigger="['click']"
      :overlayStyle="{
        minWidth: '90px'
      }"
      :getPopupContainer="getPopupContainer"
      placement="bottomRight"
      :arrow="{ pointAtCenter: true }"
    >
      <!-- :open="true" -->
      <iconfontIcon icon="icon-ellipsis" />
      <template #overlay>
        <a-menu @click="onClick">
          <a-menu-item v-for="item in menuList" :key="item.key" :disabled="item.disabled">
            <span class="hoverPrimaryColor" :style="item.style">
              {{ item.title }}
            </span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts" name="moreIcon">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useVModel } from '@vueuse/core'
import { MenuProps } from 'ant-design-vue'
import { CSSProperties } from 'vue'

export interface MenuItem {
  title: string
  key: string
  disabled?: boolean
  icon?: string
  style?: CSSProperties
}

const getPopupContainer = (_triggerNode: HTMLElement) => document.body //_triggerNode.parentNode
const props = defineProps<{ menuList: MenuItem[] }>()
const menuList = useVModel(props, 'menuList')
const emits = defineEmits<{
  (e: 'click', menuItem: MenuItem): void
}>()
// const visible = ref(false)

const onClick: MenuProps['onClick'] = menuItem => {
  const clickItem = props.menuList.find(item => item.key === menuItem.key)!
  emits('click', clickItem)
}

// function handlerVisibleChange(_visible: boolean) {
//   visible.value = _visible
//   console.log('visible.value: ', visible.value)
// }
</script>

<style lang="less" scoped>
.moreIcon {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  // border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  margin: 0 auto;
  border-radius: 4px;
  &:hover {
    background-color: rgba(13, 13, 13, 0.06);
  }
}
</style>
