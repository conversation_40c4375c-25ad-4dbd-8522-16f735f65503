<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-09-06 16:33:40
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 11:54:50
 * @FilePath: /corp-elf-web-consumer/src/components/hoverTooltipText/index.vue
 * @Description: 
-->
<template>
  <div class="hoverTooltipText" @mouseleave="handlerTooltipMouseover">
    <a-tooltip :open="visible" @mouseenter="handlerTooltipMouseenter">
      <template #title>{{ props.text }}</template>
      <text-clamp
        ref="textClampRefs"
        autoResize
        :text="props.text"
        :maxLines="props.maxLine"
        @clampChange="handlerClampChange"
      ></text-clamp>
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = withDefaults(defineProps<{ text: string; maxLine?: number }>(), { maxLine: 1 })

const textClampRefs = ref()
const visible = ref(false)
const isVisible = ref(false)

function handlerClampChange(val) {
  isVisible.value = val
}

function handlerTooltipMouseenter() {
  if (isVisible.value === true) {
    visible.value = true
  }
}
function handlerTooltipMouseover() {
  if (isVisible.value === true) {
    visible.value = false
  }
}
</script>

<style scoped>
.hoverTooltipText {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
