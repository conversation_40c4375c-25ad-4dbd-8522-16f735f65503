<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-10-12 15:43:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-05 14:57:46
 * @FilePath: /corp-elf-web-consumer/src/components/addFollowModal/index.vue
 * @Description: 
-->
<template>
  <a-modal title="选择分组" v-model:open="visible" width="500px" @cancel="hide" @ok="submit" :confirmLoading="confirmLoading">
    <a-spin :spinning="loading">
      <a-form ref="formRef" :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }" :model="form" autocomplete="off" :rules="rules">
        <a-form-item label="分组" name="collectId">
          <a-select placeholder="请选择" :options="customerData" v-model:value="form.collectId" allowClear></a-select>
        </a-form-item>

        <a-form-item v-if="props.modalType === '1'" label="企业名称" name="company">
          <a-select
            ref="selectRef"
            :value="form.company.length !== 0 ? form.company[0].companyId : undefined"
            :not-found-content="state.fetching ? undefined : null"
            :options="state.data"
            @search="companySearch"
            @blur="companyBlur"
            @select="companySelect"
            placeholder="请输入"
            style="width: 100%"
            show-search
            :filter-option="false"
            :fieldNames="{ label: 'entName', value: 'cid' }"
            allowClear
          >
            <template #notFoundContent>
              <a-spin v-if="state.fetching" size="small" />
            </template>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { companySearchTip, customerCheckReceive, customerReceive, getCustomerCollectGroup } from '@/api/api'
import { message, notification } from 'ant-design-vue'
import { FormExpose } from 'ant-design-vue/es/form/Form'
import { cloneDeep, isArray, isEmpty } from 'lodash-es'
import { computed, nextTick, ref } from 'vue'
import { receiveRequestType } from '~/types/api/customer/receive'
import useRequest from '@/hooks/useRequest'
import useSelectSearch from '@/hooks/useSelectSearch'
import { CompanySearchTipResType } from '~/types/api/company/searchTip'

interface receiveReqType extends Omit<receiveRequestType, 'collectId'> {
  collectId: string | undefined
}
interface companyType {
  companyName: string
  companyId: string
}

const props = withDefaults(
  defineProps<{
    /** 默认选中的分组id */
    collectId?: string
    /** 1 全部 2 分组  */
    modalType?: '1' | '2'
  }>(),
  {
    modalType: '2'
  }
)
const emits = defineEmits(['refresh'])

const formRef = ref<FormExpose>()
const visible = ref(false)
const confirmLoading = ref(false)

const form = ref<receiveReqType>({
  collectId: undefined,
  company: [],
  appType: 'LITE'
})
const rules = {
  collectId: [{ required: true, type: 'string', message: '请选择' }],
  company: [{ required: true, message: '请选择', type: 'array' }]
}

// 搜索企业
const { fetchData, state } = useSelectSearch(companySearchTip, 'searchContent')
function companySearch(val: string) {
  form.value.company = []
  fetchData(val)
}
function companyBlur() {
  // 处理tab键与失焦冲突，添加300ms定时器
  setTimeout(async () => {
    await nextTick()
    if (form.value.company.length === 0 && !isEmpty(state.value.data)) {
      const temp = cloneDeep({ companyName: state.value.data[0].entName, companyId: state.value.data[0].cid })
      form.value.company = [temp]
      console.log(11111)
    }
    formRef.value?.validateFields('company')
  }, 300)
}
function companySelect(val: CompanySearchTipResType['cid'], opt: CompanySearchTipResType) {
  console.log('val,opt: ', val, opt)
  const temp = cloneDeep({ companyName: opt.entName, companyId: opt.cid })
  form.value.company = [temp]
  formRef.value?.validateFields('company')
}

// 获取分组列表
const { dataList, loading, getData: getCustomerData } = useRequest(getCustomerCollectGroup, { appType: 'LITE' })
const customerData = computed<Array<{ label: string; value: string }>>(() =>
  dataList.value!.filter(item => item.collectId !== '-1').map(item => ({ label: item.collectName, value: item.collectId }))
)

// 设置默认分组
function setDefaultCollectId() {
  if (props.collectId === '-1' || isEmpty(props.collectId)) {
    form.value.collectId = customerData.value[0].value
  } else {
    form.value.collectId = props.collectId
  }
}

async function show(companyList: companyType | companyType[] = []) {
  form.value.company = isArray(companyList) ? companyList : [companyList]
  const { result } = await customerCheckReceive()
  if (!result) {
    notification.warning({
      message: '关注失败',
      description: '普通用户最多关注5个企业，开通会员关注更多'
    })
    return false
  }
  await getCustomerData()
  setDefaultCollectId()
  visible.value = true
}

function hide() {
  visible.value = false
  confirmLoading.value = false
  form.value = { company: [], collectId: undefined, appType: 'LITE' }
  formRef.value?.resetFields()
}

async function submit() {
  try {
    console.log('form.value: ', form.value)
    await formRef.value?.validate()
    confirmLoading.value = true
    const params = {
      ...cloneDeep(form.value),
      collectId: form.value.collectId as string
    }
    console.log('params: ', params)
    const { message: _message } = await customerReceive(params)
    message.success(_message)
    confirmLoading.value = false
    emits('refresh')
    hide()
  } catch (error) {
    console.error(error)
    confirmLoading.value = false
  }
}

defineExpose({ show })
</script>

<style scoped lang="less"></style>
