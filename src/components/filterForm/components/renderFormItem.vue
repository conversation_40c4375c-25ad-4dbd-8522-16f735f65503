<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-24 13:55:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 14:44:42
 * @FilePath: /corp-elf-web-consumer/src/components/filterForm/components/renderFormItem.vue
 * @Description: 
-->
<template>
  <div>
    <!-- input:  -->
    <template v-if="formItem.formItemType === 'input'">
      <a-input v-bind="bindAttrs" v-model:value="stateValue" @change="formItemChange" />
    </template>
    <!-- numberInput:  -->
    <template v-else-if="formItem.formItemType === 'numberInput'">
      <a-input-number v-bind="bindAttrs" v-model:value="stateValue" :min="1" :precision="0" @change="formItemChange" />
    </template>
    <!-- cascader:  -->
    <template v-else-if="formItem.formItemType === 'cascader'">
      <a-cascader
        v-bind="bindAttrs"
        v-model:value="stateValue"
        :multiple="formItem.mode === 'multiple'"
        :maxTagCount="10"
        :options="optionsData"
        @change="formItemChange"
      />
    </template>
    <!-- select:  -->
    <template v-else-if="formItem.formItemType === 'select'">
      <a-select
        v-bind="bindAttrs"
        v-model:value="stateValue"
        :options="optionsData"
        :mode="formItem.mode === 'multiple' ? 'multiple' : ''"
        @change="formItemChange"
      />
    </template>
    <!--  radio: -->
    <template v-else-if="formItem.formItemType === 'radio'">
      <a-radio-group v-model:value="stateValue" :options="optionsData" @change="formItemChange" />
    </template>

    <!-- rangeInputNumber -->
    <template v-else-if="formItem.formItemType === 'rangeInputNumber'">
      <rangeInputNumber
        :min="formItem.min || 0"
        :max="formItem.max || 100"
        :precision="0"
        v-model:value="stateValue"
        @change="formItemChange"
        :max-placeholder="formItem.placeholder?.[1]"
        :min-placeholder="formItem.placeholder?.[0]"
      />
    </template>

    <!-- datePicker -->
    <template v-else-if="formItem.formItemType === 'datePicker'">
      <a-date-picker v-model:value="stateValue" @change="formItemChange" valueFormat="YYYY-MM-DD" style="width: 100%" />
    </template>
    <!-- rangePicker -->
    <template v-else-if="formItem.formItemType === 'rangePicker'">
      <a-range-picker v-model:value="stateValue" @change="formItemChange" valueFormat="YYYY-MM-DD" style="width: 100%" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { Form } from 'ant-design-vue'
import { isEmpty } from 'lodash-es'
import { onMounted, ref, watch } from 'vue'
import { filterFromItemType } from '../index.vue'
import rangeInputNumber from './rangeInputNumber/index'

const formItemContext = Form.useInjectFormItemContext()
const emit = defineEmits(['update:value'])

const props = defineProps<{ value: any; formItem: filterFromItemType }>()
const { formItem } = props
const baseAttr = {
  allowClear: true,
  placeholder: !isEmpty(formItem.placeholder) ? formItem.placeholder : formItem.label
}

const bindAttrs = {
  ...baseAttr,
  ...(formItem.attrs || {})
}

const stateValue = ref(props.value)

watch(
  () => props.value,
  val => {
    stateValue.value = val
  },
  {
    deep: true
  }
)

const optionsData = ref<any[]>([])

function fetchOptionsData() {
  return new Promise(resolve => {
    if (typeof formItem.optionsData === 'function') {
      const result = formItem.optionsData.call()
      resolve(result)
    } else {
      resolve(formItem.optionsData)
    }
  }).then(content => {
    return content
  })
}

onMounted(async () => {
  optionsData.value = await fetchOptionsData()
})

function formItemChange() {
  console.log('stateValue.value: ', formItem.label, stateValue.value)
  emit('update:value', stateValue.value)
  formItemContext.onFieldChange()
}

// watchEffect(() => {
// console.log('stateValue.value: ', formItem.label, stateValue.value)
// if (!isUndefined(stateValue.value)) {
// }
// })
</script>

<style scoped></style>
