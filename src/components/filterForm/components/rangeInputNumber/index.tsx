/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-08 15:01:33
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-13 16:55:18
 * @FilePath: /corp-elf-web-consumer/src/components/filterForm/components/rangeInputNumber/index.tsx
 * @Description:
 */
import { initDefaultProps } from 'ant-design-vue/es/_util/props-util'
import PropTypes from 'ant-design-vue/es/_util/vue-types'
import { defineComponent } from 'vue'
import './index.less'
import { isEmpty, isNull, toNumber } from 'lodash-es'
import { InputNumber } from 'ant-design-vue'
// InputNumber

export const RangeInputNumberProps = {
  min: PropTypes.number, // 输入框能输入的最小值
  max: PropTypes.number, // 输入框能输入的最大值
  value: PropTypes.array,
  step: PropTypes.oneOfType([PropTypes.number, PropTypes.string]), // 每次改变步数，可以为小数
  disabled: PropTypes.bool,
  size: PropTypes.oneOf(['large', 'small', 'default']),
  formatter: PropTypes.func, // 指定输入框展示值的格式
  parser: PropTypes.func, // 指定从 formatter 里转换回数字的方式，和 formatter 搭配使用
  decimalSeparator: PropTypes.string, // 小数点
  symbol: PropTypes.string, // 中间符号
  minPlaceholder: PropTypes.string, // 最小值提示语
  maxPlaceholder: PropTypes.string, // 最大值提示语
  precision: PropTypes.number // 数值精度
}

export default defineComponent({
  name: 'RangeInputNumber',
  props: initDefaultProps(RangeInputNumberProps, {
    step: 1,
    symbol: '～',
    minPlaceholder: '最小值',
    maxPlaceholder: '最大值'
  }),
  model: {
    prop: 'value',
    event: 'change'
  },
  component: { InputNumber },
  data() {
    return {
      range: {
        min: !isEmpty(this.value) ? toNumber(this.value[0]) : null,
        max: !isEmpty(this.value) ? toNumber(this.value[1]) : null
      }
    }
  },
  watch: {
    value: {
      deep: true,
      handler(newVal) {
        this.range.min = !isEmpty(this.value) ? newVal[0] : null
        this.range.max = !isEmpty(this.value) ? newVal[1] : null
      }
    }
  },
  methods: {
    // focus() {
    //   this.$refs.inputNumberRef.focus()
    // },
    // blur() {
    //   this.$refs.inputNumberRef.blur()
    // },

    // handlerMinChange(value) {
    //   this.range.min = value
    //   this.$emit('change', [this.range.min, this.range.max])
    // },
    // handlerMaxChange(value) {
    //   this.range.max = value
    //   this.$emit('change', [this.range.min, this.range.max])
    // },
    handlerBlur() {
      if (!isNull(this.range.min) && !isNull(this.range.max) && (this.range.min as number) > (this.range.max as number)) {
        const temp = this.range.max
        this.range.max = this.range.min
        this.range.min = temp
      }

      this.$emit('update:value', [this.range.min, this.range.max])
      this.$emit('change', [this.range.min, this.range.max])
    }
  },
  render() {
    // 通用的props配置
    const inputNumberBaseProps = {
      min: this.min, // 输入框能输入的最小值
      max: this.max, // 输入框能输入的最大值
      step: this.step, // 每次改变步数，可以为小数
      disabled: this.disabled,
      size: this.size,
      formatter: this.formatter, // 指定输入框展示值的格式
      parser: this.parser, // 指定从 formatter 里转换回数字的方式，和 formatter 搭配使用
      decimalSeparator: this.decimalSeparator, // 小数点
      precision: this.precision // 数值精度
    }
    // 最小值输入框props
    const minInputNumberProps = {
      placeholder: this.minPlaceholder,
      // value: this.range.min,
      ...inputNumberBaseProps,
      ref: 'minInputNumberRef'
    }
    // 最大值输入框props
    const maxInputNumberProps = {
      placeholder: this.maxPlaceholder,
      // value: this.range.max,
      ...inputNumberBaseProps,
      ref: 'maxInputNumberRef'
    }

    return (
      <div class="rangeInputNumber">
        <InputNumber
          {...minInputNumberProps}
          v-model={[this.range.min, 'value']}
          // onChange={this.handlerMinChange}
          onBlur={this.handlerBlur}
        />
        <div class="symbol">{this.symbol}</div>
        <InputNumber
          {...maxInputNumberProps}
          v-model={[this.range.max, 'value']}
          // onChange={this.handlerMaxChange}
          onBlur={this.handlerBlur}
        />
      </div>
    )
  }
})
