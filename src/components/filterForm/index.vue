<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-24 13:35:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 14:23:42
 * @FilePath: /corp-elf-web-consumer/src/components/filterForm/index.vue
 * @Description: 过滤组件
-->
<template>
  <div class="filterForm" v-on-click-outside="hideFilterForm">
    <a-dropdown trigger="click" :open="visibility" placement="bottomRight">
      <a-badge
        :dot="isHaveSearchCondition"
        @click="showFilterForm"
        :class="{
          disabled: props.disabled,
          'color-#6553ee': isHaveSearchCondition
        }"
      >
        <iconfont-icon icon="icon-filter" class="hoverPrimaryColor" style="font-size: 24px" />
      </a-badge>

      <template #overlay>
        <div>
          <a-card class="filterFormCard">
            <!-- v-show="visibility" -->
            <a-form ref="formRef" :model="formState" :label-col="{ span: props.labelCol }" autocomplete="off">
              <template v-for="item in props.config" :key="item.key">
                <a-form-item :label="item.label" :name="item.key" class="formItem">
                  <renderFormItem :form-item="item" v-model:value="formState[item.key]"></renderFormItem>
                </a-form-item>
              </template>
            </a-form>

            <template #actions>
              <div class="bottomBtn">
                <a-space @click="refresh">
                  <iconfont-icon icon="icon-refresh" />
                  <span>重置</span>
                </a-space>

                <a-space>
                  <a-button @click="hideFilterForm">取消</a-button>
                  <a-button type="primary" @click="onSubmit">筛选</a-button>
                </a-space>
              </div>
            </template>
          </a-card>
        </div>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts" name="filterForm">
import { computed, ref, watch } from 'vue'
import { vOnClickOutside } from '@vueuse/components'
import { isEmpty } from 'lodash-es'
import renderFormItem from './components/renderFormItem.vue'
import iconfontIcon from '../tools/iconfontIcon'

export interface filterFromItemType {
  label: string
  key: string
  formItemType: 'input' | 'numberInput' | 'cascader' | 'select' | 'radio' | 'rangeInputNumber' | 'datePicker' | 'rangePicker'
  optionsData?: any
  required?: boolean
  mode?: 'single' | 'multiple'
  min?: number
  max?: number
  placeholder?: string | string[] // 数组只有rangeInputNumber会用到
  attrs?: any // 组件其他的参数
}

export interface filterFromProps {
  config: filterFromItemType[]
  value: Record<string, any>
  disabled?: boolean
  labelCol?: number
}

const props = withDefaults(defineProps<filterFromProps>(), {
  labelCol: 5
})
const emits = defineEmits<{
  // []
  (e: 'reset'): void
  (e: 'cancel'): void
  (e: 'ok', value: typeof props.value): void
  (e: 'update:value', value: typeof props.value): void
}>()

const visibility = ref(false)
const formRef = ref({})
const formState = ref(props.value)

// const { value } = toRefs(props)
watch(
  () => props.value,
  val => {
    formState.value = val
  },
  {
    deep: true
  }
)

// 计算有没有条件
const isHaveSearchCondition = computed(() => {
  if (props.disabled) {
    return false
  }

  return (
    Object.keys(formState.value).filter(key => {
      const item = formState.value[key]
      if (['isHidCustomer'].includes(key)) {
        return false
      } else if (typeof item === 'string' || typeof item === 'number') {
        return !!item
      } else if (typeof item === 'boolean') {
        return true
      } else {
        return !isEmpty(item)
      }
    }).length !== 0
  )

  // return Object.values(formState.value).filter(item => !isUndefined(item)).length !== 0
})

function showFilterForm() {
  if (props.disabled) {
    return
  }
  visibility.value = !visibility.value
}
function hideFilterForm() {
  visibility.value = false
  emits('cancel')
}
function refresh() {
  console.log('refresh')
  emits('reset')
}
function onSubmit() {
  console.log(formState.value)
  emits('update:value', formState.value)
  emits('ok', formState.value)
  visibility.value = false
}
</script>

<style lang="less" scoped>
.filterForm {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .disabled {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }
  .iconfont {
    font-size: 20px;
  }
  .filterFormCard {
    // z-index: 50;
    width: 460px;
    // position: absolute;
    // top: 30px;
    // right: 5px;
    box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.05), 0px 4px 5px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
    .formItem {
      font-weight: 400;
      :deep(.ant-form-item-label) {
        font-weight: 400 !important;
      }
    }
  }

  .bottomBtn {
    // border-top: 1px solid #e4e7ed;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // i {
    //   font-size: 16px;
    // }
  }

  .rangeNumberInput {
    display: flex;
    align-items: center;
    .ant-input-number {
      flex: 1;
    }
    span {
      margin: 0 6px;
    }
  }
}
</style>
