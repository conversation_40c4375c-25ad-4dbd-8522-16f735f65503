<template>
  <div class="loginWarpBox">
    <h1 class="title"><span class="titleStrong">账户密码</span>登录</h1>
    <a-form ref="formRef" class="formWarp" name="login" autocomplete="off" :model="loginForm" :rules="rules" @finish="onFinish">
      <a-form-item name="username">
        <a-input v-model:value="loginForm.username" placeholder="用户名">
          <template #prefix>
            <iconfontIcon icon="icon-user" class="site-form-item-icon"></iconfontIcon>
          </template>
        </a-input>
      </a-form-item>

      <a-form-item name="password">
        <a-input-password v-model:value="loginForm.password" class="password" placeholder="密码">
          <template #prefix>
            <iconfontIcon icon="icon-lock-on" class="site-form-item-icon"></iconfontIcon>
          </template>
        </a-input-password>
      </a-form-item>

      <a-form-item class="loginBtn">
        <a-button size="large" type="primary" block html-type="submit" :loading="loginBtnLoading"> 登录 </a-button>
      </a-form-item>
      <div><a @click="emits('switchQrCodeLogin')">扫码注册</a></div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store'
import { timeFix } from '@/utils/util'
import { FormInstance, notification } from 'ant-design-vue'
import { ref } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { login } from '@/api/api'

const userStore = useUserStore()
const emits = defineEmits(['loginSuccess', 'switchQrCodeLogin'])

// ===================== 账号密码登录 =====================
const formRef = ref<FormInstance>()
const loginBtnLoading = ref(false)
const loginForm = ref({
  username: '',
  password: '',
  remember_me: false
})
const rules: Record<string, Rule[]> = {
  username: [{ required: true, message: '请输入' }],
  password: [{ required: true, message: '请输入' }]
}

// 登录成功
async function onFinish() {
  try {
    loginBtnLoading.value = true
    const loginParams = { ...loginForm.value, checkKey: new Date().getTime() }
    const { result } = await login(loginParams)
    emits('loginSuccess', result)
  } catch (error) {
    console.error(error)
    loginBtnLoading.value = false
  }
}
</script>

<style lang="less" scoped></style>
