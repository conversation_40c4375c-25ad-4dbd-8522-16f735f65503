import { Modal } from 'ant-design-vue'
import loginModal from './index.vue'
import { h } from 'vue'

function showLoginModal() {
  const modal = Modal.confirm({
    width: 480,
    wrapClassName: 'loginModal',
    bodyStyle: { padding: '0px', overflow: 'hidden' },
    closable: false,
    keyboard: false,
    maskClosable: false,
    onCancel() {},
    footer: null,
    icon: null,
    content: () => h(loginModal)
  })
}

export default showLoginModal

// modal.destroy
