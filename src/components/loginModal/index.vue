<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-04 14:39:12
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-08 10:40:19
 * @FilePath: /corp-elf-web-consumer/src/components/loginModal/index.vue
 * @Description: 
-->
<template>
  <!-- 关闭按钮 -->
  <div class="closeIcon cursor-pointer" @click="handlerModalCancel">
    <CloseCircleOutlined :style="{ fontSize: '20px' }" />
  </div>

  <!-- 切换登录方式 -->
  <div class="switchLoginBox">
    <a-tooltip title="微信扫码登录" placement="left" v-if="loginType === 'password'" :getPopupContainer="getPopupContainer">
      <div class="switchLoginItem" @click="switchLoginModel('wechat')">
        <div class="switchLoginItemIcon">
          <iconfontIcon icon="icon-qrcode" class="icon"></iconfontIcon>
        </div>
      </div>
    </a-tooltip>
    <a-tooltip title="账号密码登录" placement="left" v-if="loginType === 'wechat'" :getPopupContainer="getPopupContainer">
      <div class="switchLoginItem" @click="switchLoginModel('password')">
        <div class="switchLoginItemIcon">
          <iconfontIcon icon="icon-lock-on" class="icon"></iconfontIcon>
        </div>
      </div>
    </a-tooltip>
  </div>

  <template v-if="loginType === 'wechat'">
    <qrCodeComp ref="qrCodeRef" @loginSuccess="handlerLoginSuccess" />
  </template>
  <template v-if="loginType === 'password'">
    <passwordComp ref="passwordRef" @loginSuccess="handlerLoginSuccess" @switchQrCodeLogin="switchLoginModel('wechat')" />
  </template>

  <slot name="footer">
    <div class="footerBox text-center line-height-4em px16px bg-#f6f7f9 color-#999 border-radius-bl-8px border-radius-br-8px">
      您登录即同意
      <a href="/term/userAgreement" target="_blank"> 商瞳用户协议 </a>
      和
      <a href="/term/privacyPolicy" target="_blank"> 商瞳隐私政策 </a>
    </div>
  </slot>
</template>

<script setup lang="ts">
import { CloseCircleOutlined } from '@ant-design/icons-vue'
import { nextTick, ref } from 'vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import passwordComp from './password.vue'
import qrCodeComp from './qrCode.vue'
import { Modal, notification } from 'ant-design-vue'
import { useUserStore } from '@/store'
import { timeFix } from '@/utils/util'
import { loginBaseResponseType } from '~/types/common/loginUser'

type loginType = 'wechat' | 'password'

const passwordRef = ref<InstanceType<typeof passwordComp>>()
const qrCodeRef = ref<InstanceType<typeof qrCodeComp>>()
// const openModal = ref(false)
const loginType = ref<loginType>('wechat')
const getPopupContainer = (_triggerNode: HTMLElement) => document.body

// async function handlerOpenModal() {
//   openModal.value = true
//   console.log(1212)
//   await nextTick()
//   // qrCodeRef.value?.getQrCodeTicket()
// }

function handlerModalCancel() {
  // qrCodeRef.value?.stop()
  // qrCodeRef.value?.slideTo(0)
  // openModal.value = false
  Modal.destroyAll()
}

async function switchLoginModel(type: loginType) {
  loginType.value = type
  if (loginType.value === 'wechat') {
    console.log(1212)
    await nextTick()
    qrCodeRef.value?.getQrCodeTicket()
  } else {
    qrCodeRef.value?.stop()
    qrCodeRef.value?.slideTo(0)
  }
}

const userStore = useUserStore()
function handlerLoginSuccess(result: loginBaseResponseType) {
  notification.success({ message: '欢迎', description: `${timeFix()}，欢迎回来` })
  userStore.loginSuccess(result)
  handlerModalCancel()
}

// defineExpose({ handlerOpenModal })
</script>

<style lang="less">
// 关闭按钮的样式
.closeIcon {
  position: absolute;
  top: -20px;
  right: -20px;
  color: #fff;
}

.loginModal {
  position: relative;
  // 修改弹窗样式
  .ant-modal {
    &-content {
      padding: 0;
      // overflow: hidden;
    }
  }

  // 切换登录方式的样式
  .switchLoginBox {
    position: absolute;
    top: 0;
    right: 0;
    width: 62px;
    height: 62px;
    line-height: 62px;
    background: #f4f0ff;
    cursor: pointer;
    border-radius: 8px;
    // overflow: hidden;
    .switchLoginItem {
      // position: relative;

      .switchLoginItemIcon {
        text-align: right;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        height: 62px;
        padding: 4px 4px 0 0;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          display: block;
          content: ' ';
          border: 31px solid;
          border-color: transparent transparent #fff #fff;
        }

        .icon {
          font-size: 40px !important;
          color: var(--g-primary-color);
        }
      }

      .switchTips {
        color: var(--g-primary-color);
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 5px;
        text-align: center;
        // color: #f4f0ff;
        font-size: 14px;
        line-height: 32px;
      }
    }

    // position: relative;
    // top: 0;
    // left: 0;
    // z-index: 9999;
    // display: flex;
    // align-items: center;
    // justify-content: center;
  }

  .loginWarpBox {
    padding: 62px 0 32px;
    width: 480px;

    .title {
      font-size: 20px;
      line-height: 1.3;
      font-weight: 600;
      margin-bottom: 12px;
      text-align: center;
      .titleStrong {
        color: var(--g-primary-color);
      }
    }

    .tips {
      color: #777;
      text-align: center;
    }

    .formWarp {
      width: 280px;
      margin: 24px auto 0;

      .ant-input-affix-wrapper {
        height: 42px;
        width: 100%;
        color: #333;
        font-size: 14px;
        font-weight: normal;
        box-shadow: 0px 3px 4px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        box-sizing: border-box;
        border: 1px solid #d5d5d5;
        background-color: #fff;

        input {
          &:-webkit-autofill {
            -webkit-box-shadow: 0 0 0 1000px #fff inset;
          }

          &:-internal-autofill-selected {
            transition: background-color 5000s ease-in-out 0s !important;
          }
        }
      }

      .password {
        .ant-input {
          letter-spacing: 4px;
        }
      }

      .ant-form-item {
        margin-bottom: 12px;
        // mt18px
      }

      .loginBtn {
        margin-top: 18px;
        .ant-input {
          width: 100%;
          height: 42px;
          font-size: 16px;
          padding: 0px 20px;
          color: #fff;

          border-radius: 8px;
          letter-spacing: 2px;
          text-align: center;
        }
      }
    }
  }

  // .footerBox {
  //   text-align: center;
  //   margin-top: 32px;
  // }
}
</style>
