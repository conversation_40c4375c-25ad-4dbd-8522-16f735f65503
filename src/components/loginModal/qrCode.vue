<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-21 10:11:50
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-11-19 15:45:05
 * @FilePath: /corp-elf-web-consumer/src/components/loginModal/qrCode.vue
 * @Description: 
-->
<template>
  <div class="loginWarpBox">
    <Swiper :allowTouchMove="false" @swiper="onSwiper">
      <swiper-slide class="swiper-slide">
        <h1 class="title"><span class="titleStrong">微信扫码</span>一键登录</h1>
        <div class="tips">未注册的微信号将自动创建商瞳账号</div>
        <a-spin :spinning="qrCodeLoading">
          <div class="qrCodeWarp">
            <img
              v-if="!isEmpty(qrCodeTicket)"
              @load="handlerQrCodeLoad"
              :src="`https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${qrCodeTicket}`"
            />

            <div v-show="!refreshQrCodeIsActive && !isEmpty(qrCodeTicket)" class="disabledMask">
              <span>请 <a @click="getQrCodeTicket">刷新</a> 二维码</span>
            </div>
          </div>
        </a-spin>

        <!-- <a-button @click="slideTo(1)">slideTo2</a-button> -->
      </swiper-slide>

      <swiper-slide class="swiper-slide">
        <h1 class="title"><span class="titleStrong">手机号</span>绑定</h1>
        <a-form
          ref="formRef"
          layout="vertical"
          :label-col="{ span: 0 }"
          :wrapper-col="{ span: 24 }"
          class="formWarp"
          name="login"
          :model="bindMpForm"
          :rules="rules"
          @finish="onFinish"
          :autocomplete="false"
        >
          <a-form-item name="phone">
            <a-input v-model:value="bindMpForm.phone" placeholder="手机号" inputmode="numeric" autocomplete="tel">
              <template #prefix>
                <iconfontIcon icon="icon-mobile" class="site-form-item-icon"></iconfontIcon>
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="password">
            <a-input-password v-model:value="bindMpForm.password" class="password" placeholder="密码">
              <template #prefix>
                <iconfontIcon icon="icon-lock-on" class="site-form-item-icon"></iconfontIcon>
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item name="code">
            <a-input v-model:value="bindMpForm.code" placeholder="验证码" inputmode="numeric" autocomplete="falseone-time-code">
              <template #prefix>
                <iconfontIcon icon="icon-mail1" class="site-form-item-icon"></iconfontIcon>
              </template>
              <template #suffix>
                <template v-if="!refreshMsgCodeIsActive">
                  <LoadingOutlined v-if="getMsgCodeLoading" />
                  <a v-else @click="getMsgCode">获取验证码 </a>
                </template>
                <span class="color-#ccc" v-else>{{ msgCodeCountdown }}</span>
              </template>
            </a-input>
          </a-form-item>

          <a-form-item class="loginBtn">
            <a-button size="large" type="primary" html-type="submit" class="loginBtn" block :loading="bindMpBtnLoading"> 确定 </a-button>
            <!-- <a-button size="large" type="primary" block :loading="bindMpBtnLoading" @click="slideTo(0)"> 确定 </a-button> -->
          </a-form-item>
        </a-form>
      </swiper-slide>
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from 'ant-design-vue'
import { nextTick, ref, watch, onMounted, onUnmounted } from 'vue'
import { authCheckMpSubscribe, authBindMp, authLoginMpTicket, commonPhoneCode } from '@/api/api'
import { useRequest } from 'vue-hooks-plus'
import { useIntervalFn } from '@vueuse/core'
import { has, isEmpty, isNull } from 'lodash-es'
import type { Swiper as SwiperClass } from 'swiper/types'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import { Rule } from 'ant-design-vue/es/form'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { bindMpRequestType } from '~/types/api/auth/bindMp'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { loginBaseResponseType } from '~/types/common/loginUser'

const emits = defineEmits(['loginSuccess'])

let params = new URLSearchParams(location.search)
const registerCode = params.get('registerCode')
console.log('registerCode: ', registerCode)

let swiperRef: SwiperClass
const onSwiper = (swiper: SwiperClass) => (swiperRef = swiper)
const activeIndex = ref(0)
/**
 * @description: 切换显示
 * @param {*} index
 * @return {*}
 */
function slideTo(index: number) {
  activeIndex.value = index
  swiperRef.slideTo(activeIndex.value)
}

// ===================== 微信二维码登录 =====================
const qrCodeLoading = ref(false)
const refreshQrCodeCountdown = ref(120) // 刷新二维码倒计时
const qrCodeTicket = ref('') // 二维码Ticket
// gQFB8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyRDEtMnRPYUxmeEcxTzhaQjFCMXgAAgRcPOVlAwQsAQAA

/**
 * @description: 请求二维码ticket
 * @return {*}
 */
async function getQrCodeTicket() {
  try {
    qrCodeLoading.value = true
    const { result } = await authLoginMpTicket()
    qrCodeTicket.value = result.ticket
    refreshQrCodeCountdown.value = result.expireSeconds // 设置倒计时
  } catch (error) {
    console.error(error)
    qrCodeLoading.value = false
  }
}

/**
 * @description: 设置计时器
 * @return {*}
 */
const {
  resume: countdownStart, // 启动方法
  pause: countdownStop, // 暂停方法
  isActive: refreshQrCodeIsActive // 是否激活
} = useIntervalFn(() => (refreshQrCodeCountdown.value -= 1), 1000, { immediate: false })

/**
 * @description: 轮询二维码绑定情况
 * @return {*}
 */
const {
  // loading: pollingLoading,
  runAsync: pollingStart, // 启动方法
  cancel: pollingStop // 暂停方法
} = useRequest(() => authCheckMpSubscribe({ ticket: qrCodeTicket.value }), {
  manual: true,
  pollingInterval: 1000,
  onSuccess: ({ result, code }) => {
    console.log('result, code : ', result, code)
    // 扫码成功，跳转首页
    if (code === 'SUCCESS') {
      loginSuccess(result)
      stop()
    } else if (code === 'NON_BIDDING') {
      // 扫码成功，但没有绑定手机号
      slideTo(1)
      stop()
    }
  },
  onError: e => {
    console.error(e)
    stop()
  }
})

/**
 * @description: 二维码加载完成回调，开启二维码刷新定时器
 * @return {*}
 */
async function handlerQrCodeLoad() {
  qrCodeLoading.value = false // 取消二维码loading状态
  await nextTick()
  start()
}

/**
 * @description: 开始倒计时、轮询
 * @return {*}
 */
function start() {
  pollingStart() // 开始轮询二维码绑定情况
  countdownStart() // 开始倒计时
}

/**
 * @description: 停止倒计时、轮询
 * @return {*}
 */
function stop() {
  pollingStop() // 停止轮询二维码绑定情况
  countdownStop() // 停止倒计时
}

/**
 * @description: 监听二维码刷新倒计时状态
 * @return {*}
 */
watch(
  () => refreshQrCodeCountdown.value,
  () => {
    if (refreshQrCodeCountdown.value === 0) {
      console.warn('二维码过期')
      stop()
      // getQrCodeTicket() // 请求新二维码
    }
  }
)

// ====================== 手机号绑定 =======================
const formRef = ref<FormInstance>()
const bindMpBtnLoading = ref(false)
const bindMpForm = ref<{ phone?: string; password?: string; code?: string }>({
  phone: undefined,
  password: undefined,
  code: undefined
})
const rules: Record<string, Rule[]> = {
  phone: [
    { required: true, message: '请输入' },
    { message: '请输入正确的电话号码', pattern: /^(?:(?:\+|00)86)?1\d{10}$/g }
  ],
  password: [{ required: true, message: '请输入' }],
  code: [
    { required: true, message: '请输入' },
    { message: '请输入正确的验证码', pattern: /^[0-9]*$/g }
    // {
    //   required: true,
    //   validator: (_rule, value: string) => {
    //     if (isEmpty(value)) {
    //       return Promise.reject('请输入')
    //     } else if (!/^[0-9]*$/g.test(value)) {
    //       return Promise.reject('验证码有误')
    //     } else {
    //       return Promise.resolve()
    //     }
    //   }
    // }
  ]
}

const msgCodeCountdown = ref(60)
const getMsgCodeLoading = ref(false)
/**
 * @description: 短信验证码计时器
 * @return {*}
 */
const {
  resume: msgCodeCountdownStart, // 启动方法
  pause: msgCodeCountdownStop, // 暂停方法
  isActive: refreshMsgCodeIsActive // 是否激活
} = useIntervalFn(() => (msgCodeCountdown.value -= 1), 1000, { immediate: false })
/**
 * @description: 发送短信验证码
 * @return {*}
 */
async function getMsgCode() {
  try {
    await formRef.value?.validateFields(['phone'])
    getMsgCodeLoading.value = true
    await commonPhoneCode({ type: 'REGISTER_MP', phone: bindMpForm.value.phone as string })
    msgCodeCountdown.value = 60
    msgCodeCountdownStart()
    getMsgCodeLoading.value = false
  } catch (error) {
    console.error(error)
  }
}
/**
 * @description: 监听短信验证码倒计时状态
 * @return {*}
 */
watch(
  () => msgCodeCountdown.value,
  () => {
    if (msgCodeCountdown.value === 0) {
      console.warn('短信验证码过期')
      msgCodeCountdownStop()
    }
  }
)

/**
 * @description: 绑定手机提交
 * @return {*}
 */
async function onFinish() {
  try {
    bindMpBtnLoading.value = true
    const params: bindMpRequestType = {
      provider: 'wechatMp',
      code: qrCodeTicket.value as string,
      phone: bindMpForm.value.phone as string,
      phoneCode: bindMpForm.value.code as string,
      password: bindMpForm.value.password as string,
      inviteCode: !isNull(registerCode) ? registerCode : undefined
    }
    const { result } = await authBindMp(params)
    loginSuccess(result)
  } catch (error) {
    console.error(error)
  } finally {
    bindMpBtnLoading.value = false
  }
}

/**
 * @description: 登录成功调用方法
 * @param {*} result
 * @return {*}
 */
function loginSuccess(result: loginBaseResponseType) {
  stop()
  emits('loginSuccess', result)
}

onMounted(() => {
  getQrCodeTicket()
  slideTo(0)
})

onUnmounted(() => {
  stop()
})

defineExpose({ getQrCodeTicket, start, stop, slideTo })
</script>

<style lang="less" scoped>
.qrCodeWarp {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 16px auto 0;
  overflow: hidden;
  border-radius: 12px;
  line-height: 0;
  border: 1px solid rgba(0, 0, 0, 0.08);

  .disabledMask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fffffff2;
    font-size: 16px;
  }
}
</style>
