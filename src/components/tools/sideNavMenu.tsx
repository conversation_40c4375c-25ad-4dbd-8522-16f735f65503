/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-17 14:55:56
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-18 11:08:56
 * @FilePath: /corp-elf-web-consumer/src/components/tools/sideNavMenu.tsx
 * @Description:
 */
import { defineComponent, PropType, SlotsType } from 'vue'
import IconfontIcon from './iconfontIcon'
import { Card } from 'ant-design-vue'

/** 基础导航项接口 */
export interface BaseNavItem {
  /** 导航项显示的文本 */
  label: string
  /** 导航项的唯一标识 */
  key: string
  /** 导航项的图标名称 */
  icon?: string
  /** 导航项的路由路径 */
  path?: string
  [property: string]: any
}

/** 侧边导航子项接口 */
interface SideNavItemChildren extends BaseNavItem {}

/** 侧边导航项接口 */
interface SideNavItem extends BaseNavItem {
  children?: SideNavItemChildren[]
}

export default defineComponent({
  name: 'sideNavMenu',
  components: { IconfontIcon },
  props: {
    navMenu: {
      type: Array as PropType<SideNavItem[]>,
      required: true
    },
    selectedKeys: {
      type: String,
      required: false
    }
  },
  slots: Object as SlotsType<{
    /** 标题图标插槽 */
    titleIcon: SideNavItem
    /** 标题文本插槽 */
    titleText: SideNavItem
    /** 子项图标插槽 */
    childrenIcon: SideNavItemChildren
    /** 子项文本插槽 */
    childrenText: SideNavItemChildren
  }>,
  emits: {
    click: (item: SideNavItem | SideNavItemChildren) => item
  },
  setup(props, { slots, emit }) {
    // menu的渲染方法
    const renderNavItemTitle = (navItem: SideNavItem) => (
      <div class={['flex items-center', navItem.children?.length ? 'mb8px' : '']} key={navItem.key}>
        {slots.titleIcon?.(navItem) ||
          (navItem.icon && (
            <div class="w30px h30px flex-center-center">
              {<IconfontIcon icon={navItem.icon} extraCommonProps={{ class: ['!fs-24px mr-8px'] }} />}
            </div>
          ))}

        <h5
          class={[props.selectedKeys === navItem.key ? 'color-#6553ee' : '', 'flex-1 !mb0 color-#000000e0 fw600 fs-16px lh-1.5em']}
          onClick={() => emit('click', navItem)}
        >
          {slots.titleText?.(navItem) || navItem.label}
        </h5>
      </div>
    )

    const renderNavItemChildren = (navItem: SideNavItemChildren) => (
      <li key={navItem.key} class="fs-16px flex items-start">
        {slots.childrenIcon?.(navItem) ||
          (navItem.icon && (
            <div class="w30px h30px flex-center-center">
              {<iconfontIcon icon={navItem.icon} extraCommonProps={{ class: ['!fs-12px'] }} />}
            </div>
          ))}

        <div
          class={[props.selectedKeys === navItem.key ? 'color-#6553ee' : '', 'flex-1 ellipsis lh-30px hoverPrimaryColor']}
          onClick={() => emit('click', navItem)}
        >
          {slots.childrenText?.(navItem) || navItem.label}
        </div>
      </li>
    )

    return () => (
      <Card bordered={false}>
        {props.navMenu?.map((item, index) => (
          <div class={index !== 0 ? 'mt16px' : ''}>
            {renderNavItemTitle(item)}
            <ul class="lh-2em overflow-hidden">{item.children && item.children.map(renderNavItemChildren)}</ul>
          </div>
        ))}
      </Card>
    )
  }
})
