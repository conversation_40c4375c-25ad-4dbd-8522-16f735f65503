/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-12 11:33:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-15 18:17:59
 * @FilePath: /corp-elf-web-consumer/src/components/tools/iconfontIcon.tsx
 * @Description:
 */
import { createFromIconfontCN } from '@ant-design/icons-vue'
import { defineComponent } from 'vue'

// https://www.iconfont.cn/manage/index?spm=a313x.search_index.i3.22.104f3a81X7Wm2B&manage_type=myprojects&projectId=3584494
const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3584494_6peqbt0gd3d.js'
})

// 如果需要修改icon的样式，查看下面链接extraCommonProps参数
// https://antdv.com/components/icon-cn#api

// 例如
// <iconfontIcon
//   icon="icon-info-circle"
//   :extra-common-props="{
//     class:[]
//     style: {
//       color: '#ccc',
//       fontSize: '16px'
//     }
//   }"
// />
export default defineComponent({
  name: 'iconfontIcon',
  props: ['icon', 'extraCommonProps'],
  components: { IconFont },
  setup(props) {
    return () => <IconFont type={props.icon} style={{ fontSize: '20px' }} class={`iconfontIcon`} {...props.extraCommonProps} />
  }
})
