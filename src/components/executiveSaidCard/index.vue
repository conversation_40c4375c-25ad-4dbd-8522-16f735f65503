<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-11 15:22:42
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-11-11 16:42:38
 * @FilePath: /corp-elf-web-consumer/src/components/executiveSaidCard/index.vue
 * @Description: 
-->
<template>
  <div class="executiveSaidCard p16px bg-#fff flex items-start overflow-hidden w100%">
    <div class="w40px"><iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px mr-8px'] }" /></div>
    <div class="flex-1 overflow-hidden">
      <div
        v-if="props.showField.includes('name') || props.showField.includes('company') || props.showField.includes('post')"
        class="titleContent h30px line-height-30px overflow-hidden"
      >
        <!-- flex items-center -->
        <p
          v-if="props.showField.includes('name')"
          :level="5"
          class="companyName hoverPrimaryColor m0! white-space-nowrap"
          @click="
            executiveDetail({
              executiveName: item.companyExecutive.executiveName,
              executiveId: item.companyExecutive.executiveId
            })
          "
        >
          {{ item.companyExecutive.executiveName || '' }}
        </p>
        <span
          v-if="props.showField.includes('company')"
          class="color-#7F7F7F hoverPrimaryColor ellipsis"
          @click="
            openCompanyInfo({
              companyId: item.companyExecutive.companyUniId,
              companyName: item.companyExecutive.companyName
            })
          "
        >
          {{ item.companyExecutive.companyName }}
        </span>
        <span class="color-#00000073 flex-1 ellipsis" v-if="props.showField.includes('post')">
          {{ item.companyExecutive.postList.map(item => item.post).join('、') }}
        </span>
      </div>

      <div v-if="props.showField.includes('publicDate')" class="time h30px line-height-30px">
        <a-typography-text type="secondary">{{ getPublishDate(toNumber(item.publishTime)) }}</a-typography-text>
      </div>

      <div class="mainContent mb8px" v-if="props.showField.includes('content')">
        <text-clamp :max-lines="mainContentMaxLines" :text="item.executiveComment" class="content color-#7F7F7F fs-16px">
          <template #after="{ clamped, expanded, toggle }">
            <a v-if="clamped || expanded" @click="toggle"> {{ clamped ? '展开' : '收起' }} </a>
          </template>
        </text-clamp>
      </div>

      <div v-if="props.showField.includes('sourceUrl')" class="secondaryContent pl2em color-#00000073 text-right">
        <text-clamp
          :max-lines="1"
          location="middle"
          :text="`—— 《${item.title.replace(/\.[^\.]+$/g, '')}》 ${item.siteName}`"
          :class="!isEmpty(item.url) ? 'hoverPrimaryColor' : ''"
          @click="openSourceUrl(item)"
        >
        </text-clamp>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import dayjs from 'dayjs'
import { isEmpty, toNumber } from 'lodash-es'
import { useRouter } from 'vue-router'
import { executiveSaidListResType } from '~/types/api/executiveSaid/executiveSaidList'

const props = withDefaults(
  defineProps<{
    item: executiveSaidListResType
    showField?: ('name' | 'company' | 'post' | 'publicDate' | 'content' | 'sourceUrl')[]
    mainContentMaxLines?: number
    dateFormat?: '年月日' | '今日'
  }>(),
  {
    mainContentMaxLines: 8,
    showField: () => ['name', 'company', 'post', 'publicDate', 'content', 'sourceUrl'],
    dateFormat: '今日'
  }
)

/** 打开源地址 */
function openSourceUrl(item: executiveSaidListResType) {
  // if (item.informationType === 'NEWS' && !isEmpty(item.sourceUrl)) {
  window.open(item.url, '_blank')
  // }
}

const router = useRouter()
function openCompanyInfo(item: { companyId: string; companyName: string }) {
  router.push({
    name: 'companyInfo-index',
    path: '/companyInfo/index',
    query: {
      companyId: item.companyId,
      companyName: item.companyName
    }
  })
}

function executiveDetail(item: { executiveId: string; executiveName: string }) {
  router.push({
    name: 'executiveComments-detail',
    path: '/executiveComments/detail',
    query: { executiveId: item.executiveId, executiveName: item.executiveName }
  })
}

/** 解析日期 */
const getPublishDate = (date: number) => {
  if (props.dateFormat === '年月日') {
    return dayjs(date).format('YYYY-MM-DD')
  } else {
    const diffDay = dayjs(date).set('h', 0).set('m', 0).set('s', 0)
    const nowDay = dayjs().set('h', 0).set('m', 0).set('s', 0)
    const diffRes = nowDay.diff(diffDay, 'd')
    if (diffRes < 1) {
      return '今日'
    } else if (diffRes === 1) {
      return '昨日'
    } else {
      return dayjs(date).format('M月D日')
    }
  }
}
</script>

<style scoped lang="less">
.executiveSaidCard {
  .titleContent {
    display: flex;
    align-items: center;
    .companyName {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      font-size: 16px;
      line-height: 1.5;
    }
    span {
      margin-left: 8px;
      &:last-child {
        min-width: fit-content;
      }
    }
  }
  + .executiveSaidCard {
    border-top: 1px solid #e7e7e7;
  }
}
</style>
