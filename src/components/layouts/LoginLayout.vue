<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-19 09:59:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-01-23 11:24:18
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/LoginLayout.vue
 * @Description: 
-->
<template>
  <div id="loginLayout" :class="['login-layout-wrapper']">
    <div class="w-full h-full relative">
      <component :is="componentId"></component>
    </div>
  </div>
</template>

<script lang="ts">
import defaultLogin from '@/views/login/index.vue'
// import tencentLogin from '@/views/login/tencentCloud/index.vue'

export default {
  name: 'UserLayout',
  data() {
    return {}
  },
  computed: {
    componentId() {
      // const { hostname } = window.location

      // let comp = defaultLogin
      // switch (hostname) {
      //   // case 'tencentcloud.eia.bengine.com.cn':
      //   //   comp = tencentLogin
      //   //   break

      //   default:
      //     comp = defaultLogin
      //     break
      // }
      return defaultLogin
    }
  }
}
</script>

<style lang="less" scoped>
#loginLayout.login-layout-wrapper {
  height: 100%;
  overflow-y: auto;

  // &.mobile {
  //   .container {
  //     .main {
  //       max-width: 368px;
  //       width: 98%;
  //     }
  //   }
  // }

  .container {
    width: 100%;
    height: 100%;
    background-size: 100%;
    position: relative;
  }
}
</style>
