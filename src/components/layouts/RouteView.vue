<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 16:54:50
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:50:05
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/RouteView.vue
 * @Description: 
-->
<template>
  <router-view v-slot="{ Component }">
    <transition name="fade-slide" mode="out-in" appear>
      <div class="main max-w1400px margin-auto mt0 mb0">
        <div class="mb16px">
          <a-alert type="warning" closable>
            <template #message>
              <a-space>
                <span>您的好友向您推荐「ToB人的客户洞察工具」，注册立即获赠1个月会员。</span>
                <a>立即注册>></a>
              </a-space>
            </template>
          </a-alert>
        </div>

        <keep-alive :include="keepAliveCache.getCachedViews">
          <component :is="wrap(includeKey, Component)" :key="includeKey" />
        </keep-alive>
      </div>
    </transition>
  </router-view>
</template>

<script setup lang="ts" name="BaseRouterView">
import { useKeepAliveCache } from '@/store'
import { computed, h, VNode, watch } from 'vue'
import { RouterView, useRoute } from 'vue-router'
import { getRouteKey, wrapperMap } from '@/store/modules/keepAliveCache'
import { isEmpty } from 'lodash-es'

const route = useRoute()
const keepAliveCache = useKeepAliveCache()

const includeKey = computed(() => {
  // 自定义缓存名称
  if (!isEmpty(route.meta.name)) {
    return route.meta.name as string
  }
  return getRouteKey(route)
})

watch(
  route,
  newVal => {
    const key = includeKey.value
    const index = keepAliveCache.getCachedViews.findIndex(item => item === key)
    // 如果没加入这个路由记录，则加入路由历史记录
    if (index === -1) {
      keepAliveCache.addCachedView(key)
    }
  },
  { immediate: true }
)

// 参考https://github.com/vuejs/core/pull/4339#issuecomment-1238984279
// 为keep-alive里的component接收的组件包上一层自定义name的壳.
function wrap(wrapperName: string, component: VNode) {
  let wrapper: { name: string; render(): VNode }
  // 重点就是这里，这个组件的名字是完全可控的，
  // 只要自己写好逻辑，每次能找到对应的外壳组件就行，完全可以写成任何自己想要的名字.
  // 这就能配合 keep-alive 的 include 属性可控地操作缓存.
  if (wrapperMap.has(wrapperName)) {
    wrapper = wrapperMap.get(wrapperName)!
  } else {
    wrapper = {
      name: wrapperName,
      render() {
        return h('div', { class: 'vaf-page-wrapper', style: { height: '100%' } }, component)
      }
    }
    wrapperMap.set(wrapperName, wrapper)
  }
  return h(wrapper)
}
</script>

<style lang="less" scoped>
.main {
  padding: 16px 0;
  height: 100%;
}
</style>
