<template>
  <a-modal v-model:open="visible" title="修改密码" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="close">
    <a-spin :spinning="confirmLoading">
      <a-form autocomplete="off" ref="formRef" :model="form" :rules="rules" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
        <a-form-item label="手机号">
          <!-- name="phone" -->
          <a-input v-model:value="form.phone" placeholder="手机号" inputmode="numeric" autocomplete="tel" disabled> </a-input>
        </a-form-item>

        <a-form-item label="短信验证码" name="code">
          <a-input v-model:value="form.code" placeholder="验证码" inputmode="numeric" autocomplete="falseone-time-code">
            <template #suffix>
              <template v-if="!refreshMsgCodeIsActive">
                <LoadingOutlined v-if="getMsgCodeLoading" />
                <a v-else @click="getMsgCode">获取验证码 </a>
              </template>
              <span class="color-#ccc" v-else>{{ msgCodeCountdown }}</span>
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="新密码" name="password">
          <a-input-password autoComplete="new-password" v-model:value="form.password" placeholder="新密码" />
        </a-form-item>
        <a-form-item label="确认密码" name="checkPassword">
          <a-input-password v-model:value="form.checkPassword" placeholder="再次确认密码" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts" name="userPassword">
import { ref } from 'vue'
import { authUserChangePassword, commonPhoneCode, authUserResetPassword } from '@/api/api'
import { Form, message } from 'ant-design-vue'
import { isEmpty } from 'lodash-es'
import { RuleObject } from 'ant-design-vue/lib/form/interface'
import { changePasswordReqType } from '~/types/api/auth/changePassword'
import { resetPasswordReqType } from '~/types/api/auth/resetPassword'
import { useIntervalFn } from '@vueuse/core'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { Rule } from 'ant-design-vue/es/form'
import { FormExpose } from 'ant-design-vue/es/form/Form'
import { useUserStore } from '@/store'

const emit = defineEmits(['ok', 'close'])

const userStore = useUserStore()

const formRef = ref<FormExpose>()
const checkPhoneFormRef = ref<FormExpose>()
const confirmLoading = ref(false)
const visible = ref(false)
const form = ref({
  phone: '',
  code: undefined,
  password: undefined,
  checkPassword: undefined
})
let userInfo: changePasswordReqType = {
  id: undefined,
  username: undefined,
  oldPassword: undefined,
  password: undefined,
  checkPassword: undefined,
  realName: undefined
}
const rules: Record<string, Rule[]> = {
  phone: [
    { required: true, message: '请输入' },
    { message: '请输入正确的电话号码', pattern: /^(?:(?:\+|00)86)?1\d{10}$/g }
  ],
  code: [
    { required: true, message: '请输入' },
    { message: '请输入正确的验证码', pattern: /^[0-9]*$/g }
  ],
  oldPassword: [{ required: true, message: '请输入' }],
  password: [
    {
      required: true,
      validator: (_rule: RuleObject, value: string) => {
        console.log('_rule: ', _rule)
        if (isEmpty(value)) {
          return Promise.reject('请输入')
        } else if (value.length < 6) {
          return Promise.reject('密码最少6位')
        } else {
          return Promise.resolve()
        }
      }
    }
  ],
  checkPassword: [
    {
      required: true,
      validator: (_rule: RuleObject, value: string) => {
        if (isEmpty(value)) {
          return Promise.reject('请输入')
        } else if (value !== form.value.password) {
          return Promise.reject('两次输入密码不一致!')
        } else {
          return Promise.resolve()
        }
      }
    }
  ]
}

function onOpen(openData: changePasswordReqType) {
  console.log('openData: ', openData)
  userInfo = openData
  form.value.phone = userStore.getUserInfo.mobile
  visible.value = true
}

async function handleOk() {
  // 触发表单验证
  try {
    await formRef.value?.validateFields()
    console.log('form.value: ', form.value)
    confirmLoading.value = true
    const { message: msg } = await authUserResetPassword({
      phone: form.value.phone,
      code: form.value.code,
      sysUser: {
        id: userInfo.id,
        username: userInfo.username,
        password: form.value.password,
        checkPassword: form.value.checkPassword,
        realName: userInfo.realName
      }
    })
    message.success(msg)
    emit('ok')
    confirmLoading.value = false
    close()
  } catch (error) {
    console.error(error)
    confirmLoading.value = false
  }
}

function close() {
  emit('close')
  visible.value = false
  formRef.value?.resetFields()
  form.value = {
    phone: undefined,
    code: undefined,
    password: undefined,
    checkPassword: undefined
  }
  userInfo = {
    id: undefined,
    username: undefined,
    oldPassword: undefined,
    password: undefined,
    checkPassword: undefined,
    realName: undefined
  }
}

// 短信验证码相关
const msgCodeCountdown = ref(60)
const getMsgCodeLoading = ref(false)
/**
 * @description: 短信验证码计时器
 * @return {*}
 */
const {
  resume: msgCodeCountdownStart, // 启动方法
  pause: msgCodeCountdownStop, // 暂停方法
  isActive: refreshMsgCodeIsActive // 是否激活
} = useIntervalFn(
  () => {
    if (msgCodeCountdown.value > 0) {
      msgCodeCountdown.value -= 1
    } else {
      console.warn('短信验证码过期')
      msgCodeCountdownStop()
    }
  },
  1000,
  { immediate: false }
)
/**
 * @description: 发送短信验证码
 * @return {*}
 */
async function getMsgCode() {
  try {
    // await formRef.value?.validateFields(['phone'])
    getMsgCodeLoading.value = true
    await commonPhoneCode({ type: 'RESET_PASSWORD' })
    msgCodeCountdown.value = 60
    msgCodeCountdownStart()
    getMsgCodeLoading.value = false
  } catch (error) {
    getMsgCodeLoading.value = false
    console.error(error)
  }
}

defineExpose({
  onOpen
})
</script>

<style lang="less" scoped>
.randomPassword {
  .ant-btn {
    border-radius: 0 4px 4px 0;
  }
}
</style>
