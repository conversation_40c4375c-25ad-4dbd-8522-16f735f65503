<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 20:40:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-09 14:58:57
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/userMenu/index.vue
 * @Description: 
-->
<template>
  <div class="user-wrapper">
    <a-dropdown
      :getPopupContainer="(triggerNode:HTMLElement) => triggerNode.parentNode"
      placement="bottomRight"
      :disabled="isEmpty(userStore.getToken)"
    >
      <a class="user-dropdown-menu" @click="authCheck()">
        <!-- {{ isEmpty(userStore.getToken) ? '未登录' : nickname }} -->
        <a-avatar shape="square" :src="touxiang" :size="36"> </a-avatar>
      </a>
      <template #overlay>
        <a-menu>
          <a-menu-item key="1" @click="updatePassword">
            <iconfontIcon icon="icon-setting" style="font-size: 16px" />
            <span style="margin-left: 4px">修改密码</span>
          </a-menu-item>
          <a-menu-item key="2" @click="handleLogout">
            <iconfontIcon icon="icon-logout" style="font-size: 16px" />
            <span style="margin-left: 4px">退出登录</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>

  <user-password ref="userPasswordRef" />
</template>

<script setup lang="ts" name="userMenu">
import userPassword from './userPassword.vue'
import { computed, h, inject, ref } from 'vue'
import { useThemeStore, useUserStore } from '@/store'
import { Modal } from 'ant-design-vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { logout } from '@/api/api'
import { isEmpty } from 'lodash-es'
import touxiang from '@/assets/images/touxiang.jpg'

const authCheck = inject('authCheck') as Function
const { getColorPrimary } = useThemeStore()
const sideFontColor = computed(() => (useThemeStore().getThemeMode !== 'dark' ? '#222' : 'rgba(256, 256, 256, 0.6)'))

const userStore = useUserStore()
const userPasswordRef = ref()

function updatePassword() {
  console.log('修改密码')
  userPasswordRef.value.onOpen(userStore.userInfo)
}

function handleLogout() {
  Modal.confirm({
    title: '提示',
    content: '真的要退出登录吗 ?',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onCancel() {},
    async onOk() {
      console.log('退出登录')
      await logout()
      userStore.logout()
    }
  })
}
</script>

<style lang="less" scoped>
.user-wrapper {
  .search-input {
    width: 180px;
    color: inherit;

    :deep(.ant-select-selection) {
      background-color: inherit;
      border: 0;
      border-bottom: 1px solid white;
      &__placeholder,
      &__field__placeholder {
        color: inherit;
      }
    }
  }
  .user-dropdown-menu {
    cursor: pointer;
    // padding: 0 14px 0;
    // margin-right: 16px;
    display: inline-block;
    transition: all 0.3s;
    height: 70%;
    // line-height: 46px;
    display: flex;
    align-items: center;
    color: v-bind(sideFontColor);
    &:hover {
      color: v-bind(getColorPrimary);
    }
  }
  .logout_title {
    color: inherit;
    text-decoration: none;
  }
}
</style>
