<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-06 17:51:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-17 10:12:57
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/searchBox.vue
 * @Description: 
-->
<template>
  <div class="searchBox">
    <a-auto-complete
      ref="autoComplete"
      v-model:value="searchContent"
      style="width: 260px"
      :options="options"
      @search="onSearch"
      :dropdownMatchSelectWidth="300"
      :open="dropdownVisible"
      @dropdownVisibleChange="dropdownVisibleChange"
      @keyup.enter="handlerEnter"
    >
      <!-- @select="onSelect" -->
      <template #notFoundContent>
        <a-spin v-if="loading" :spinning="loading" />
        <a-empty
          v-else-if="!loading && options.length === 0"
          :image="empty"
          :image-style="{
            height: '164px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }"
        >
          <template #description>
            <span class="emptyDescription">无数据</span>
          </template>
        </a-empty>
      </template>

      <template #option="item">
        <template v-if="item.companyClass === '-'">
          <div class="companyItem" @click="onSelect(item.id)">
            <!-- icon-shouye-hangye -->
            <template v-if="item.type === 'company'">
              <iconfontIcon icon="icon-qiye" class="color-#6553ee mr-8px"></iconfontIcon>
              <span>{{ item.entName }}</span>
            </template>
            <template v-else>
              <iconfontIcon icon="icon-usergroup" class="color-#6553ee mr-8px"></iconfontIcon>
              <span class="mr-8px">{{ item.personName }}</span>
              <span class="disabledItem">{{ item.entName }}</span>
            </template>
          </div>
        </template>
        <template v-else>
          <div class="companyItem" @click="handlerDisabledItemClick(item)">
            <iconfontIcon icon="icon-qiye" class="mr-8px"></iconfontIcon>
            <span class="disabledItem"> {{ item.entName }}({{ item.openStatus }}) </span>
          </div>
        </template>
      </template>

      <a-input placeholder="企业、高管、圈子" size="large">
        <template #prefix>
          <iconfontIcon icon="icon-search" />
        </template>
      </a-input>
    </a-auto-complete>
  </div>
</template>

<script setup lang="ts" name="headerSearchBox">
import { companySearchTip } from '@/api/api'
import { debounce, isEmpty, isUndefined } from 'lodash-es'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { CompanySearchTipResType } from '~/types/api/company/searchTip'
import { message } from 'ant-design-vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import empty from '@/assets/empty.svg'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'

const getPopupContainer = (_triggerNode: HTMLElement) => document.body
const router = useRouter()
const searchContent = ref('')
const autoComplete = ref<InputRef>()
const loading = ref(false)
const lastFetchId = ref(0)
const options = ref<CompanySearchTipResType[]>([])
let controller: AbortController | null = null

function filterExecution(searchText: string) {
  const tempText = searchText.trim()
  if (isEmpty(tempText) || tempText.length < 2) {
    return false
  }
  return true
}

const onSearch = (searchText: string) => {
  console.log('searchText: ', searchText)

  const tempText = searchText.trim()
  if (!filterExecution(searchText)) {
    return
  }
  // 取消之前的请求
  if (controller) {
    controller.abort()
  }
  controller = new AbortController()
  searchReq(tempText)
}

const searchReq = debounce((searchText: string) => {
  lastFetchId.value += 1
  controller = new AbortController()
  options.value = []
  loading.value = true
  const fetchId = lastFetchId.value
  companySearchTip({ searchContent: searchText }, controller)
    .then(({ result }) => {
      if (fetchId !== lastFetchId.value) {
        return
      }
      options.value = result.map(item => ({ ...item, value: item.entName }))
      loading.value = false
    })
    .catch(err => {
      loading.value = false
      console.error(err)
    })
}, 300)

function onSelect(recordId: string) {
  const optionData = options.value.find(item => item.id === recordId)
  console.log('optionData: ', optionData)

  if (!isEmpty(optionData)) {
    if (optionData.type === 'company') {
      router
        .push({
          path: '/companyInfo/index',
          name: 'companyInfo-index',
          query: {
            companyId: optionData.cid,
            companyName: optionData.entName
          }
        })
        .finally(() => {
          options.value = []
          searchContent.value = ''
        })
    } else {
      router
        .push({
          path: '/executiveComments/detail',
          name: 'executiveComments-detail',
          query: {
            executiveId: optionData.personId,
            executiveName: optionData.personName
          }
        })
        .finally(() => {
          options.value = []
          searchContent.value = ''
        })
    }
  }
}

function handlerEnter() {
  // 输入框为空处理
  if (!filterExecution(searchContent.value)) {
    return
  }

  const companyInfo = options.value.find(item => item.entName === searchContent.value)
  // 如果输入框内的文字是下拉框内数据
  if (!isUndefined(companyInfo)) {
    onSelect(companyInfo.id)
  }

  autoComplete.value?.blur()
}

const dropdownVisible = ref(false)
function dropdownVisibleChange(status: boolean) {
  dropdownVisible.value = filterExecution(searchContent.value) && status === true
}

function goSearchPage() {
  router.push({ path: '/search' })
}

function handlerDisabledItemClick(item: CompanySearchTipResType) {
  if (item.companyClass === 'A' || item.companyClass === 'B') {
    message.warning(`该组织已${item.openStatus},不再更新组织详情`)
    return
  }
  if (!item.cid) {
    message.warning('该组织详情正在准备中')
    return
  }
}
</script>

<style lang="less" scoped>
.searchBox {
  .companyItem {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .disabledItem {
    color: #bfbfbf;
    cursor: no-drop !important;
  }

  .searchBtn {
    &:hover {
      text-decoration: underline;
    }
  }

  .ant-input-affix-wrapper,
  :deep(.ant-input) {
    background-color: transparent;
  }
}
</style>
