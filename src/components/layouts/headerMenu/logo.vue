<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-04 18:33:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 15:24:29
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/logo.vue
 * @Description: logo
-->
<template>
  <div class="logo">
    <template v-if="props.isLink">
      <router-link to="/" class="flex justify-center items-center" @click.native="handleClick">
        <div class="logoBg">
          <a-image :preview="false" :width="25" :src="logo" :fallback="defaultLogo" />
        </div>
        <span class="fs-30px fw500 ml-8px primaryColor">商瞳</span>
      </router-link>
    </template>
    <template v-else>
      <div class="flex justify-center items-center">
        <div class="logoBg">
          <a-image :preview="false" :width="25" :src="logo" :fallback="defaultLogo" />
        </div>
        <span class="fs-30px fw500 ml-8px primaryColor">商瞳</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import defaultLogo from '@/assets/logo_white.svg'
import { useKeepAliveCache } from '@/store'
import { useThemeStore } from '@/store/modules/theme'
// import { useUserStore } from '@/store'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const props = withDefaults(defineProps<{ isLink?: boolean }>(), { isLink: true })

const { getColorPrimary } = useThemeStore()

const logo = computed(() => {
  // const userStore = useUserStore()
  // const websiteInfo = userStore.getWebsiteInfo
  // if (websiteInfo.title !== '商瞳') {
  //   return websiteInfo.logo1
  // }
  return defaultLogo
})

const keepAliveCacheStore = useKeepAliveCache()
const route = useRoute()
function handleClick() {
  keepAliveCacheStore.delOthersCachedViews(route)
}
</script>

<style lang="less" scoped>
.logo {
  width: 120px;
  // margin-right: 32px;

  .logoBg {
    width: 48px;
    height: 48px;
    // line-height: 48px;
    text-align: center;
    background: v-bind(getColorPrimary);
    border-radius: 16px;

    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
