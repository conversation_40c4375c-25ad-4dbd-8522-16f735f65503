<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-27 10:42:41
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 17:59:02
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/vipModal/index.vue
 * @Description: 
-->
<template>
  <a-modal
    width="1024px"
    v-model:open="visible"
    @cancel="handleClose"
    :footer="null"
    :closable="false"
    :bodyStyle="{ padding: 0, height: '680px', borderRadius: '8px', overflow: 'hidden' }"
    wrapClassName="vipModal"
  >
    <!-- 关闭按钮 -->
    <div class="closeIcon cursor-pointer" @click="handleClose">
      <CloseCircleOutlined :style="{ fontSize: '20px' }" />
    </div>

    <div
      v-if="scanRes?.result === 'SUCCESS'"
      class="absolute bg-#ffffffc4 h100% w100% flex items-center justify-center backdrop-blur-10 z-11 overflow-hidden br-8"
    >
      <a-result status="success" title="支付成功!">
        <template #extra>
          <a-button @click="handlePaySuccess">返回</a-button>
        </template>
      </a-result>
    </div>

    <div class="h100% flex items-center">
      <div class="flex-1 h100% bg-#E8DCC3 p-16px flex flex-direction-column">
        <div class="topWrap mb-16px flex items-center">
          <a-space class="userWrap flex-1">
            <a-avatar shape="square" :src="touxiang" :size="42"> </a-avatar>
            <div class="color-#6C3B0F fs-14px">
              <p>{{ userStore.getNickname }}</p>
              <span>{{
                !userStore.isVip
                  ? '会员权益尚未开通'
                  : `会员有效期：${dayjs(userStore.getMemberInfo.memberEffectiveTime).format('YYYY-MM-DD')}`
              }}</span>
            </div>
          </a-space>

          <RedeemCoupons @exchangeSuccessFull="handleExchangeSuccess" />
        </div>
        <div class="priceWrap">
          <div class="mb-16px">
            <div v-if="!userStore.isVip || primeCoupon" class="flex items-center justify-between mb-16px">
              <span v-if="!userStore.isVip" class="color-#DBA67C">开通享全部会员权益</span>
              <span v-if="primeCoupon" class="flex-1 text-right">
                {{ primeCoupon.discountParam.discountRate * 10 }}折优惠券有效期至
                <CountdownTimer :targetTimestamp="primeCoupon.effectiveTime" @end="couponEnd" />
              </span>
            </div>

            <a-row :gutter="32" class="priceList">
              <a-col :span="8" v-for="(item, index) in paymentPlan" :key="index" @click="changePayMentPlan(index)">
                <div :class="['priceItem', paymentPlanSelect === index ? 'priceItemSelected' : '']">
                  <span class="tip">
                    <!-- {{ isEmpty(primeCoupon) ? '' : `${primeCoupon.discountParam.discountRate * 10}折优惠` }} -->
                    限时优惠
                  </span>
                  <div class="priceContainer mt-16px">
                    <p class="fw-500 fs-16px">{{ item.desc }}</p>
                    <h3>
                      ¥
                      <span class="fs-32px fw-600">
                        {{ item.paymentPrice }}
                      </span>
                    </h3>
                    <h3 class="text-decoration-line-through fs-14px">¥{{ item.showTotalPrice }}</h3>
                    <!-- v-if="primeCoupon" -->
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>

          <div class="m-auto mb-16px vipLabel flex items-center justify-center">
            <span class="color-#DBA67C mx-8px">会员尊享 最新模型</span>
          </div>

          <div class="text-center permissionComparison">
            <a-row>
              <a-col class="fw-500" :span="8">权益对比</a-col>
              <a-col class="fw-550" :span="8">会员</a-col>
              <a-col class="fw-550" :span="8">非会员</a-col>
            </a-row>
            <a-row v-for="(item, index) in permissionComparison" :key="index" :class="[index % 2 === 0 ? '' : 'bg-F7F7F9']">
              <a-col class="" :span="8">{{ item.label }}</a-col>
              <a-col class="" :span="8">
                <span class="flex-center-center">
                  {{ item.vipText }}
                  <iconfontIcon v-if="item.vipIcon" :icon="item.vipIcon" style="color: #c1641b" />
                </span>
              </a-col>
              <a-col class="" :span="8">
                <span class="flex-center-center">
                  {{ item.noVipText }}
                  <iconfontIcon v-if="item.noVipIcon" :icon="item.noVipIcon" style="color: #5a5f84" />
                </span>
              </a-col>
            </a-row>
            <a-row>
              <a-col class="color-#ccc" :span="24">更多会员特权持续更新中，敬请期待</a-col>
            </a-row>
          </div>
        </div>
      </div>

      <div class="w-260px h100% bg-#F5E2C6 p-16px pt140px text-center">
        <span class="color-#00000080" v-if="primeCoupon">已使用优惠券</span>
        <div class="finalPrice">
          <h3 class="color-#ff6321 pr-16px">
            ¥
            <span class="fs-32px fw-600"> {{ paymentInfo.paymentPrice }} </span>
          </h3>
        </div>

        <div class="discountTip border-radius-20px inline-block color-#fff px-8px">
          已优惠{{ paymentInfo.showTotalPrice - paymentInfo.paymentPrice }}
        </div>
        <div class="qrWrap my-16px flex items-center justify-center">
          <a-config-provider :theme="{ token: { colorSplit: '#DBA67C' } }">
            <a-qrcode
              :value="payInfo?.paymentUrl || ''"
              bgColor="#fff"
              class="border-radius-8px"
              :status="qrCodeStatus"
              @refresh="getQrCodeTicket"
            />
          </a-config-provider>
        </div>
        <div>
          <div class="flex items-center justify-center"><WechatOutlined class="color-#1AAD19 fs-20px mr-8px" />微信扫码支付</div>
          <div class="mt-8px color-#666">
            我已阅读同意
            <a href="/term/membershipAgreement" target="_blank">《会员服务协议》</a>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { CloseCircleOutlined, WechatOutlined } from '@ant-design/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { couponInfo, userMemberPayment, userMemberPrices, cancelUserAllVipWechatQrCode, paymentGetPayStatus } from '@/api/api'
import { couponInfoResType } from '~/types/api/coupon/info'
import { isEmpty, isUndefined, minBy, omit, toNumber } from 'lodash-es'
import useRequest from '@/hooks/useRequest'
import { userMemberPaymentReqType } from '~/types/api/userMember/payment'
import { useRequest as vHooksRequest } from 'vue-hooks-plus'
import dayjs from 'dayjs'
import { useIntervalFn } from '@vueuse/core'
import { useUserStore } from '@/store'
import CountdownTimer from './countdownTimer.vue'
import touxiang from '@/assets/images/touxiang.jpg'
import RedeemCoupons from './redeemCoupons.vue'
import { inject } from 'vue'

const userStore = useUserStore()
const authCheck = inject('authCheck') as Function

interface permissionComparisonType {
  label: string
  vipText: string
  noVipText: string
  vipIcon?: string
  noVipIcon?: string
}
const permissionComparison: permissionComparisonType[] = [
  { label: '关注企业数量', vipText: '100个', noVipText: '5个' },
  { label: '关注高管数量', vipText: '100个', noVipText: '5个' },
  { label: 'AI解读', vipText: '每日30次', noVipText: '每日3次' },
  { label: '潜客模型', vipText: '2个', noVipText: '', noVipIcon: 'icon-close' },
  { label: 'ToB圈子', vipText: '', noVipText: '', vipIcon: 'icon-check', noVipIcon: 'icon-close' },
  { label: '行业标杆名录', vipText: '全部', noVipText: '10条' },
  { label: '行业高管言论', vipText: '', noVipText: '', vipIcon: 'icon-check', noVipIcon: 'icon-close' },
  // { label: '行业智库观点', vipText: '', noVipText: '', vipIcon: 'icon-check', noVipIcon: 'icon-close'  },
  { label: '企业画像', vipText: '全部', noVipText: '有限' },
  { label: '企业动态', vipText: '不限', noVipText: '2条' },
  { label: '企业高管言论', vipText: '不限', noVipText: '2条' },
  { label: '全部高管言论', vipText: '不限', noVipText: '2条' },
  { label: '企业高级搜索', vipText: '', noVipText: '', vipIcon: 'icon-check', noVipIcon: 'icon-close' }
]

const visible = ref(false)
const paymentPlanSelect = ref(0)

// 获取价格
const { dataList: pricesDataList, getData: getMemberPrices } = useRequest(userMemberPrices, {}, { immediateReqData: false })
// 取消所有订单
const { getData: cancelAllPayInfo } = useRequest(cancelUserAllVipWechatQrCode, {}, { immediateReqData: false })
// 获取优惠券列表
const { dataList: couponDataList, getData: getCouponList } = useRequest(couponInfo, {}, { immediateReqData: false })
// 计算最优优惠券
const primeCoupon = computed<couponInfoResType | undefined>(() => {
  const temp = couponDataList.value?.filter(item => item.discountParam.type === 'DISCOUNT')
  return minBy(temp, item => item.discountParam.discountRate)
})

function handleExchangeSuccess() {
  getQrCodeTicket()
}

function couponEnd() {
  getCouponList()
}

// 计算每个方案的价格
interface PaymentPlanType extends userMemberPaymentReqType {
  /** 会员描述 */
  desc: string
  /** 展示的原价 */
  showTotalPrice: number
}
const paymentPlan = computed<PaymentPlanType[]>(
  () =>
    pricesDataList.value?.map(item => {
      let temp: PaymentPlanType = {
        memberMonth: item.month,
        totalPrice: item.discountPrice,
        discountPrice: 0,
        paymentPrice: item.discountPrice,
        memberType: item.memberType,
        payType: 'WECHAT_PAY_NATIVE',
        showTotalPrice: item.price,
        desc: item.desc
      }
      if (!isUndefined(primeCoupon.value)) {
        temp = {
          ...temp,
          discountPrice: toNumber((temp.totalPrice - temp.paymentPrice * primeCoupon.value.discountParam.discountRate).toFixed(2)),
          paymentPrice: toNumber((temp.paymentPrice * primeCoupon.value.discountParam.discountRate).toFixed(2)),
          couponId: primeCoupon.value.couponId
        }
      }

      return temp
    }) || []
)

// 当前选中的支付方案
const paymentInfo = computed(() => paymentPlan.value[paymentPlanSelect.value])

// 获取支付链接和倒计时
const {
  dataList: payInfo,
  loading: payLoading,
  getData: refreshPayInfo
} = useRequest(userMemberPayment, paymentInfo, { immediateReqData: false })

function changePayMentPlan(index: number) {
  paymentPlanSelect.value = index
  pollingStop()
  countdownStop()
  getQrCodeTicket()
}

// 轮询扫码情况
const {
  data: scanRes,
  runAsync: pollingStart, // 启动方法
  cancel: pollingStop // 暂停方法
} = vHooksRequest(() => paymentGetPayStatus({ checkCode: payInfo.value!.checkCode }), {
  manual: true,
  pollingInterval: 1000,
  onSuccess: ({ result, code, success }) => {
    if (!success) {
      countdownStop()
      pollingStop()
      return
    }
    // 扫码成功，跳转首页
    if (code === 'SUCCESS' && result === 'SUCCESS') {
      console.log('支付成功')
      countdownStop()
      pollingStop()
    } else if (code === 'FAIL') {
      console.log('支付失败')
    }
  }
})

const refreshQrCodeCountdown = ref(120) // 刷新二维码倒计时
/**
 * @description: 设置计时器
 * @return {*}
 */
const {
  resume: countdownStart, // 启动方法
  pause: countdownStop, // 暂停方法
  isActive: refreshQrCodeIsActive // 是否激活
} = useIntervalFn(
  () => {
    if (refreshQrCodeCountdown.value > 0) {
      refreshQrCodeCountdown.value--
    } else {
      pollingStop()
      countdownStop()
    }
  },
  1000,
  { immediate: false }
)

/**
 * @description: 开始倒计时、轮询
 * @return {*}
 */
function start() {
  pollingStart() // 开始轮询二维码绑定情况
  countdownStart() // 开始倒计时
}

async function getQrCodeTicket() {
  payLoading.value = true
  await cancelAllPayInfo()
  await getCouponList()
  await refreshPayInfo()
  if (visible) {
    refreshQrCodeCountdown.value = dayjs(payInfo.value?.effectiveTime).diff(new Date(), 's')
    // 请求支付信息成功后开始轮询支付状态
    start()
  }
}

// 二维码状态
const qrCodeStatus = computed(() => {
  if (payLoading.value) return 'loading'
  if (!refreshQrCodeIsActive.value || scanRes.value?.result === 'FAIL' || !payInfo.value?.paymentUrl) return 'expired'
  return scanRes.value?.result === 'PAY_RUNNING' ? 'scanned' : 'active'
})

function handlePaySuccess() {
  window.location.reload()
}

async function openModal() {
  // 判断是否登录
  if (isEmpty(userStore.getToken)) {
    authCheck()
    return false
  } else {
    visible.value = true
    await cancelAllPayInfo()
    getCouponList()
  }
  // 判断之前是否有获取过二维码
  if (isEmpty(payInfo.value?.paymentUrl)) {
    getQrCodeTicket()
  } else {
    pollingStart()
  }
}

function handleClose() {
  if (scanRes.value?.result === 'SUCCESS') {
    handlePaySuccess()
  } else {
    pollingStop()
    visible.value = false
  }
}

onMounted(() => {
  if (!isEmpty(userStore.getToken)) {
    getMemberPrices()
  }
})

defineExpose({
  openModal
})
</script>

<style lang="less">
// 关闭按钮的样式
// .closeIcon {
//   position: absolute;
//   top: -20px;
//   right: -20px;
//   color: #fff;
// }

.exchangeCoupons {
  .ant-popover-inner {
    background: linear-gradient(180deg, #fff5e3, #fff);
    padding: 16px;
  }

  .ant-popover-arrow:before {
    background: #fff5e3;
  }

  p {
    text-align: center;
    margin-bottom: 16px;
  }

  p {
    color: #974d1c;
    font-size: 15px;
    font-weight: 600;
    line-height: 21px;
  }
}

.vipModal {
  .ant-modal {
    // &-body {
    //   position: relative;
    // }
    &-content {
      padding: 0;
      // overflow: hidden;
    }
  }

  .priceWrap {
    scrollbar-width: none;
    overflow: auto;
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;

    .priceList {
      .priceItem {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 144px;
        margin: 0 auto;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #f0c992;
        background: #fefaee;
        border-radius: 16px;
        color: #c15f32;
        text-align: center;
      }

      .priceItemSelected {
        background: linear-gradient(288.44deg, #eabc88 -18.63%, #ebbf8d -11.17%, #fadbaf 1.29%, #fff1d4 54.41%, #fffefa 116.17%);
        border: 1px solid #dba67c;
      }

      .tip {
        background: linear-gradient(270deg, #ff715f, #ff3f43);
        border-radius: 12px 0 12px 0;
        color: #fff;
        font-size: 14px;
        font-weight: 400;
        left: -1px;
        line-height: 14px;
        padding: 4px 12px;
        position: absolute;
        top: -1px;
      }
    }

    .vipLabel {
      &::before,
      &::after {
        position: relative;
        width: 100px;
        border-block-start: 1px solid transparent;
        border-block-start-color: inherit;
        border-block-end: 0;
        transform: translateY(50%);
        content: '';
      }
    }

    .permissionComparison {
      font-size: 14px;
      border: 1px solid #eeeeee;
      border-radius: 8px;
      .ant-row {
        &:last-of-type {
          .ant-col {
            border-bottom: none;
          }
        }
      }
      .ant-col {
        padding: 8px 0;
        border-bottom: 1px solid #eeeeee;

        &:nth-of-type(2) {
          border-bottom: 1px solid #fff8e1;
          background-color: #f9efcc;
        }
      }
    }
  }

  .discountTip {
    position: relative;
    &::before {
      position: absolute;
      top: -5px;
      left: 30px;
      content: ' ';
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 5px 5px 5px;
      border-color: transparent transparent #ff3f43 transparent;
    }
    background: linear-gradient(270deg, #ff715f, #ff3f43);
  }
}
</style>
