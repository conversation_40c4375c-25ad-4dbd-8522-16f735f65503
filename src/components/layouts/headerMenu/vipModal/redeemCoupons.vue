<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-10-12 11:03:58
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-12 14:49:25
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/headerMenu/vipModal/redeemCoupons.vue
 * @Description: 
-->
<template>
  <div class="redeemCoupons bg-#fdf5e8 border-radius-6px color-#974d1c fs-12px h32px relative flex items-center hover-bg-#f8ecdd">
    <a-popover
      overlayClassName="exchangeCoupons "
      placement="topLeft"
      :arrow="false"
      trigger="click"
      @openChange="handlePopoverOpenState"
      v-model:open="popoverOpen"
    >
      <template #content>
        <a-spin :spinning="exchangeLoading">
          <p>兑换优惠券</p>
          <a-input
            ref="exchangeCodeInputRef"
            class="exchangeCodeInput animate__faster"
            type="text"
            placeholder="请输入兑换码"
            v-model:value.trim="exchangeCode"
          />
          <div class="exchangeBtn" @click="submitExchange">确认兑换</div>
        </a-spin>
      </template>
      <div class="px-16px cursor-pointer flex items-center justify-center">
        <iconfontIcon icon="icon-youhuiquan" style="font-size: 16px" />
        <span class="h-32px lh-32px ml-2px">兑换优惠卷</span>
      </div>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { couponExchange } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useUserStore } from '@/store'
import { animateCSS } from '@/utils/util'
import { message } from 'ant-design-vue'
import { InputRef } from 'ant-design-vue/es/vc-input/inputProps'
import { isEmpty } from 'lodash-es'
import { nextTick, ref } from 'vue'

const userStore = useUserStore()
const emits = defineEmits(['exchangeSuccessFull'])
const exchangeCodeInputRef = ref<InputRef>()

const popoverOpen = ref(false)
// 优惠卷兑换
const exchangeCode = ref()
const exchangeLoading = ref(false)
async function submitExchange() {
  try {
    if (isEmpty(exchangeCode.value)) {
      throw new Error('兑换码不能为空')
    }
    exchangeLoading.value = true
    await couponExchange({ exchangeCode: exchangeCode.value })
    message.success('兑换成功')
    exchangeCode.value = ''
    userStore.getUserMemberInfo()
    emits('exchangeSuccessFull')
    exchangeLoading.value = false
    popoverOpen.value = false
  } catch (error) {
    const dom = document.querySelector<HTMLElement>('.exchangeCodeInput')
    exchangeCode.value = ''
    exchangeLoading.value = false
    console.error(error)
    if (dom) {
      animateCSS(dom, 'shakeX')
      dom.style.borderColor = '#ff4d4f' // 设置边框为红色
    }
  }
}

function handlePopoverOpenState(visible: boolean) {
  console.log('visible: ', visible)
  if (visible) {
    nextTick(() => {
      exchangeCodeInputRef.value?.focus()
    })
  }
  const dom = document.querySelector<HTMLElement>('.exchangeCodeInput')
  if (dom) {
    dom.style.borderColor = ''
  }
  popoverOpen.value = visible
}
</script>

<style scoped lang="less">
.redeemCoupons {
  .exchangeCodeInput {
    background-color: transparent;
    border: 1px solid #f0c992;
    border-radius: 25px;
    color: #864618 !important;
    height: 36px;
    width: 180px;
    text-align: center !important;
    margin-bottom: 28px;

    &::placeholder {
      color: #864618b1;
    }
    &:hover {
      border: 1px solid #f0c992;
      box-shadow: none !important;
    }
  }

  .exchangeBtn {
    margin: 0 auto;
    text-align: center;
    background: linear-gradient(270deg, #f0c082, #ffe4b6);
    border-radius: 25px;
    color: #864618;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    height: 36px;
    line-height: 36px;
    width: 120px;
  }

  @keyframes shakeX {
    0%,
    100% {
      transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
      transform: translateX(-2px); /* 调整这里的值，增加或减小抖动幅度 */
    }
    20%,
    40%,
    60%,
    80% {
      transform: translateX(2px); /* 调整这里的值，增加或减小抖动幅度 */
    }
  }

  .animate__shakeX {
    animation-name: shakeX;
  }
}
</style>
