<template>
  <template v-if="formattedTime?.days">
    <mark class="bg-#ffffff00 color-#E02020">{{ formattedTime?.days }}</mark>
    天
  </template>
  <template v-if="formattedTime?.hours">
    <mark class="bg-#ffffff00 color-#E02020">{{ formattedTime?.hours }}</mark>
    时
  </template>
  <template v-if="formattedTime?.minutes">
    <mark class="bg-#ffffff00 color-#E02020">{{ formattedTime?.minutes }}</mark>
    分
  </template>
  <mark class="bg-#ffffff00 color-#E02020">{{ formattedTime?.seconds }}</mark>
  秒
</template>

<script setup lang="ts">
import { onUnmounted, ref, toRef } from 'vue'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

// 使用 dayjs 的 duration 插件
dayjs.extend(duration)

const props = defineProps({
  /** 格式：时间戳 单位：毫秒 */
  targetTimestamp: {
    type: Number,
    required: true,
    default: 0
  }
})

const emit = defineEmits(['end'])
const targetTimestamp = toRef(props, 'targetTimestamp')

interface formattedTimeType {
  days: number
  hours: number
  minutes: number
  seconds: number
}
const formattedTime = ref<formattedTimeType>()
// 定义倒计时函数
function countdown() {
  // 当前时间
  const now = dayjs().valueOf()

  // 计算剩余时间
  const diffInMilliseconds = targetTimestamp.value - now

  // 如果时间到了或超过，停止倒计时
  if (diffInMilliseconds <= 0) {
    clearInterval(timer) // 停止定时器
    console.log('倒计时结束')
    emit('end')
    return
  }

  // 将时间差转换为 dayjs 的 duration 对象
  const diffDuration = dayjs.duration(diffInMilliseconds)

  // 提取天、小时、分钟和秒
  const days = Math.floor(diffDuration.asDays())
  const hours = diffDuration.hours()
  const minutes = diffDuration.minutes()
  const seconds = diffDuration.seconds()

  // 格式化输出倒计时
  formattedTime.value = { days, hours, minutes, seconds }
}

// 每秒更新倒计时
const timer = setInterval(countdown, 1000)

onUnmounted(() => {
  clearInterval(timer)
})
</script>
