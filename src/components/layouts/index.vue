<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 18:02:56
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-20 10:43:20
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/index.vue
 * @Description: 
-->
<template>
  <a-layout class="mainLayout bg-#fafafe">
    <div class="content-bg"></div>
    <headerMenu class="scrollLock" />
    <a-layout-content class="scrollLock">
      <div class="relative">
        <router-view> </router-view>
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts" name="globalLayout">
import headerMenu from './headerMenu/index.vue'
</script>

<style lang="less" scoped>
.mainLayout {
  position: relative;
  z-index: 1;
  .content_container {
    flex-direction: column;
    padding-left: 92px;
    min-height: 100vh;
  }

  // background-image: url('@/assets/images/indexBg.jpg');
  // background-position: top;
  // background-repeat: no-repeat;
  // background-size: 100vw 900px;

  .scrollLock {
    padding-left: calc(100vw - 100%);
  }
  .content-bg {
    position: absolute;
    background-image: url('@/assets/images/indexBg.jpg');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    height: 900px;
    z-index: 0;
    width: 100%;
  }
}
</style>
