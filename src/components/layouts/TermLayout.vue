<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 16:54:50
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-03-28 16:40:48
 * @FilePath: /corp-elf-web-consumer/src/components/layouts/termLayout.vue
 * @Description: 
-->

<template>
  <a-layout class="termLayout bg-#fafafe">
    <div class="content-bg"></div>

    <div class="headerMenu sticky t-0 w100% z-9">
      <a-layout-header
        :style="{
          backgroundColor: `rgba(255,255,255,${transparency})`
        }"
        :class="['', transparency > 0.5 ? 'headerMenu-box-shadow' : '']"
      >
        <div class="flex items-center justify-between flex-1 max-w1400px m-0 m-x-auto">
          <logo />
          <a-space>
            <a href="https://www.bengine.com.cn/" target="_blank" class="fs-16 color-#222">前往官网</a>
            <a-divider type="vertical" style="width: 2px; background-color: #8181816e" />
            <user-menu />
          </a-space>
        </div>
      </a-layout-header>
    </div>

    <a-layout-content class="z-2">
      <div class="h100% py16px max-w1400px margin-auto mt0 mb0">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts" name="TermLayout">
import { useWindowScroll } from '@vueuse/core'
import { round } from 'lodash-es'
import { computed } from 'vue'

// 计算滚动距离，动态设置透明度
const { y } = useWindowScroll()
const MAX_SCROLL = 30
const transparency = computed(() => round(y.value / MAX_SCROLL, 3))
</script>

<style lang="less" scoped>
.termLayout {
  position: relative;
  z-index: 1;

  .content-bg {
    position: absolute;
    background-image: url('@/assets/images/indexBg.jpg');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    height: 900px;
    z-index: 0;
    width: 100%;
  }

  .headerMenu {
    .headerMenu-box-shadow {
      // box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
      box-shadow: 0 0 12px #ddd;
    }
  }

  // .content_container {
  //   flex-direction: column;
  //   padding-left: 92px;
  //   min-height: 100vh;
  // }
}
</style>
