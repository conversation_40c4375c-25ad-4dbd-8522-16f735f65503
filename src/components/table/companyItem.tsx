import { defineComponent } from 'vue'
import classes from './index.module.less'
import iconfontIcon from '@/components/tools/iconfontIcon'
import ellipsisTag from '@/components/ellipsisTag/index.vue'
import { Tag, message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { isEmpty } from 'lodash-es'
import { useClipboard } from '@vueuse/core'

const companyItem = defineComponent({
  name: 'companyItem',
  components: { iconfontIcon, ellipsisTag },
  props: ['companyInfo'],
  setup(props) {
    const router = useRouter()

    /**
     * @description: 处理点击企业名称
     * @param {Event} event
     * @param {*} item
     * @return {*}
     */
    function openCompanyInfo($event: Event, item: { companyId?: string; cid?: string; entName: string }) {
      $event.stopPropagation()

      const id = item.companyId || item.cid

      console.log('props.companyInfo: ', item)
      // if (item.companyClass === 'A') {
      //   message.warning('系统暂不提供机关、事业单位、社会团体、个体工商户、海外企业的详情展示。')
      // } else if (item.companyClass === 'B') {
      //   message.warning(`该组织已${item.openStatus},不再更新组织详情`)
      // } else
      if (!id) {
        message.warning('该组织详情正在准备中')
      } else {
        router.push({
          path: '/companyInfo/index',
          name: 'companyInfo-index',
          query: {
            companyId: id,
            companyName: item.entName
          }
        })
      }
    }

    /**
     * @description: 拷贝企业名称
     * @param {Event} event
     * @param {string} name
     * @return {*}
     */
    // const source = ref<string>('')
    const { copy } = useClipboard({ legacy: true })
    function copyCompanyName(_event: Event, name: string) {
      // $event.stopPropagation()
      // source.value = name
      console.log('name: ', name)
      copy(name)
        .then(() => {
          message.success('复制成功')
        })
        .catch(err => {
          console.error(err)
          message.error('复制失败')
        })
    }

    /**
     * @description: 点击打开企业官网
     * @param {Event} event
     * @param {string} website
     * @return {*}
     */
    function openWebsite($event: Event, website: string) {
      $event.stopPropagation()

      if (website) {
        let url = website
        if (!(url.includes('https://') || url.includes('http://'))) {
          url = `http://${url}`
        }
        window.open(url, '_blank')
      }
    }

    return () => (
      <div class={classes.companyItem}>
        <div class={`${classes.companyNameBox} flexCenter  ellipsis `}>
          {/* 企业名称 */}
          <p class={`${classes.name} hoverPrimaryColor ellipsis`} onClick={event => openCompanyInfo(event, props.companyInfo)}>
            {props.companyInfo.companyClass === 'B'
              ? `${props.companyInfo.entName}（${props.companyInfo.openStatus}）`
              : props.companyInfo.entName}
          </p>

          <div class={`flexCenter flex1`}>
            {/* 官网按钮 */}
            {props.companyInfo.website && props.companyInfo.website !== '-' ? (
              <a class={`flexCenter ellipsis`} onClick={event => openWebsite(event, props.companyInfo.website)}>
                <iconfont-icon icon="icon-link" style="font-size: 18px" />
                官网
              </a>
            ) : (
              ''
            )}

            {/* 复制按钮 */}
            <div class={classes.copyBtn}>
              <iconfont-icon
                icon="icon-copy"
                class={`hoverPrimaryColor ${classes.iconfontIcon}`}
                onClick={(event: Event) => copyCompanyName(event, props.companyInfo.entName)}
              />
            </div>
          </div>
        </div>

        {/* 企业标签 */}
        {!isEmpty(props.companyInfo.showTags) ? (
          <div class={`${classes.tagBox} ellipsis`}>
            {/* 我的客户、企业客户的标签 */}
            {(props.companyInfo.companyLabels || []).map((item: string) => (
              <Tag color="#FDE59C"> {item} </Tag>
            ))}

            {/* 其他标签 */}
            {/* <ellipsisTag tagList={props.companyInfo.showTags} type="simple"></ellipsisTag> */}
            {(props.companyInfo.showTags || []).map((item: string) => (
              <Tag> {item}</Tag>
            ))}
          </div>
        ) : null}
      </div>
    )
  }
})

export default companyItem
