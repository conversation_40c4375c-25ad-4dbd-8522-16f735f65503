/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-23 11:03:33
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-10 15:45:00
 * @FilePath: /corp-elf-web-consumer/src/components/table/index.tsx
 * @Description:
 */
import { computed, createVNode, defineComponent, PropType, ref, renderSlot, watch } from 'vue'
import classes from './index.module.less'
import companyCard from '@/components/companyCard/index.vue'
import { PaginationProps, TableProps } from 'ant-design-vue'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { ColumnsType } from 'ant-design-vue/lib/table'
import { debounce, isEmpty } from 'lodash-es'
import companyItem from './companyItem'
import { transformLocation, transformAnnualRevenue } from '@/utils/util'
import { Card, Table, Space, Dropdown, MenuItem, Menu } from 'ant-design-vue'
import { Key } from 'ant-design-vue/es/_util/type'
import { ColumnType, GetComponentProps } from 'ant-design-vue/es/vc-table/interface'

/**
 * @description: T记录的类型 N字段的类型
 * @return {*}
 */
export interface bodyCell<T, N = any> {
  text: N
  record: T
  index: number
  column: ColumnType<N>
}

/**
 * @description: T记录的类型 N字段的类型
 * @return {*}
 */
export interface actionsBodyCell<T, N = any> extends bodyCell<T, N> {
  visibility: boolean
}

export default defineComponent({
  name: 'Table',
  emits: ['update:sort', 'selectedRowsChange'],
  components: { companyCard, iconfontIcon },
  props: {
    title: { type: String },
    columns: { type: Array as PropType<ColumnsType>, required: true }, // 表头数据
    dataSource: { type: Array, required: true }, // 列表数据
    // as PropType<companyCardType[]>
    loading: { type: Boolean, required: false }, // 加载
    pagination: {
      type: Object as PropType<PaginationProps>, // 分页
      default: {
        current: 1, // 当前页数
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总条目数
        showSizeChanger: true, // 显示pagesize修改
        showQuickJumper: false, // 显示快速跳转
        hideOnSinglePage: true, // 只有一页时是否隐藏
        pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
        responsive: true // 当 size 未指定时，根据屏幕宽度自动调整尺寸
      }
    },
    isHaveCheckBox: { type: Boolean, default: true },
    // 排序
    sort: { type: String },
    sortConfig: { type: Array as PropType<Array<{ label: string; value: string }>>, default: () => [] },
    rowKey: { type: String, default: 'id' } // 表格关键id的key
  },
  setup(_props, { attrs, slots, expose, emit }) {
    // const router = useRouter()
    const TableRef = ref()
    expose({ TableRef })

    // ============================== 排序类型 ==============================
    const sortType = ref(_props.sortConfig.length === 0 ? undefined : isEmpty(_props.sort) ? _props.sortConfig[0].value : _props.sort)
    const sortOptions = _props.sortConfig
    // 排序方式变动
    function sortTypeChange(item: { label: string; value: string }) {
      sortType.value = item.value
      emit('update:sort', sortType.value)
    }
    const renderSortMenu = () => {
      let returnDom
      if (slots.sort?.()) {
        returnDom = <div class={classes.sortMenu}>{renderSlot(slots, 'sort')}</div>
      } else if (_props.sortConfig.length !== 0) {
        returnDom = (
          <div class={classes.sortMenu}>
            <Dropdown trigger="click" placement="bottomRight">
              {{
                default: () => (
                  <a style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <iconfontIcon icon="icon-order-descending" style="font-size: 24px"></iconfontIcon>
                  </a>
                ),
                overlay: () => (
                  <Menu>
                    {sortOptions.map(item => (
                      <MenuItem onClick={() => sortTypeChange(item)} key={item.value}>
                        <span class={sortType.value === item.value ? 'color-#6553ee' : ''} style={{ width: 'max-content' }}>
                          {item.label}
                        </span>
                      </MenuItem>
                    ))}
                  </Menu>
                )
              }}
            </Dropdown>
          </div>
        )
      }
      return returnDom
    }

    // ========================
    /**
     * @description: 分页参数
     * @param {*}
     * @return {*}
     */
    const paginationProps = computed(() => {
      const pp = {
        // ...defaultPaginationProps,
        total: _props.dataSource.length,
        ..._props.pagination
      }

      const largestPage = Math.ceil(pp.total / (pp.pageSize || 0))
      if ((pp.current || 0) > largestPage) {
        pp.current = largestPage
      }
      return pp
    })

    // ========================
    const selectedRowKeys = ref<Key[]>([]) // 选中的企业
    const selectedRows = ref<any>([]) // 选中的企业
    // 监听表格数据，如果变化就将多选框的数据清空
    watch(
      () => _props.dataSource,
      (_newVal, _oldVal) => {
        selectedRowKeys.value = []
        selectedRows.value = []
      }
    )

    // ========================
    // 表格复选框监听勾选
    watch(
      () => selectedRowKeys.value,
      (_newVal, _oldVal) => {
        emit('selectedRowsChange', selectedRowKeys.value, selectedRows.value)
      }
    )

    // 复选框配置
    const hideSelectAll = ref(true)
    const hoverRowId = ref<string>()
    const rowSelection = computed<TableProps['rowSelection']>(() => ({
      hideSelectAll: hideSelectAll.value,
      selectedRowKeys: selectedRowKeys.value,
      // columnWidth: 50,
      onChange: (keys, rows) => {
        selectedRowKeys.value = keys
        selectedRows.value = rows
        hideSelectAll.value = !keys.length
      },
      getCheckboxProps: record => {
        return {
          name: record.name,
          style: {
            visibility: selectedRowKeys.value?.includes(record[_props.rowKey]) || hoverRowId.value === record[_props.rowKey] ? '' : 'hidden'
          }
        }
      }
    }))

    // 表头相关方法
    const customHeaderRow: GetComponentProps<ColumnType<any>[]> = (_columns, _index) => {
      return {
        onClick: () => {},
        onMouseenter: _event => (hideSelectAll.value = false),
        onMouseleave: _event => (hideSelectAll.value = !selectedRowKeys.value.length)
      }
    }
    // 行相关方法
    //     DataType
    // number
    // ColumnType
    // (data: DataType, index?: number, column?: ColumnType<any>)
    const customRow: GetComponentProps<any> = (columns, _index) => {
      return {
        onClick: () => {},
        // onMouseenter: event => (hoverRowId.value = columns[_props.rowKey]),
        // onMouseleave: event => (hoverRowId.value = '')
        onMouseenter: debounce(_event => (hoverRowId.value = columns[_props.rowKey]), 30),
        onMouseleave: debounce(_event => (hoverRowId.value = ''), 30)
      }
    }

    // ===============内置的渲染方法===============
    const renderCell = {
      companyCard: (item: any) => createVNode(companyItem, { companyInfo: item }),
      address: (item: any) => transformLocation(item),
      annualRevenue: (item: any) => transformAnnualRevenue(item)
    }

    // 最后渲染
    return () => (
      <div class={classes.Table}>
        <Card class={classes.tableCard} bodyStyle={{ padding: '16px 16px 0' }} style="width: 100%">
          {/* ; overflow: hidden */}
          {{
            title: () => (
              <div class={classes.titleBox}>
                <div class={classes.title}>{!!slots['cardTitle'] ? renderSlot(slots, 'cardTitle') : _props.title}</div>
                <Space>
                  {renderSlot(slots, 'button')}
                  {renderSortMenu()}
                  {renderSlot(slots, 'extra')}
                </Space>
              </div>
            ),
            default: () => (
              <Table
                ref={TableRef}
                {...attrs}
                rowKey={_props.rowKey}
                rowSelection={_props.isHaveCheckBox ? rowSelection.value : undefined}
                customHeaderRow={customHeaderRow}
                customRow={customRow}
                columns={_props.columns}
                dataSource={_props.dataSource}
                pagination={paginationProps.value}
                loading={_props.loading}
                scroll={isEmpty(attrs.scroll) ? { x: true } : (attrs.scroll as TableProps['scroll'])}
                v-slots={{
                  ...slots,
                  bodyCell: (item: any) => {
                    const { column, text, record, index } = item
                    let renderFunc

                    switch (column.slotName) {
                      // 企业卡片
                      case 'companyCard':
                        renderFunc = renderCell['companyCard'](record)
                        break
                      // 地址
                      case 'address':
                        renderFunc = renderCell['address'](record)
                        break
                      // 营收
                      case 'annualRevenue':
                        renderFunc = renderCell['annualRevenue'](record)
                        break
                      // 操作按钮
                      case 'actions':
                        renderFunc = renderSlot(slots, 'actions', {
                          visibility: hoverRowId.value === record[_props.rowKey],
                          column,
                          text,
                          record,
                          index
                        })
                        break
                      default:
                        renderFunc = !!slots[column.slotName] ? renderSlot(slots, column.slotName, { column, text, record, index }) : text
                        break
                    }

                    return renderFunc
                  }
                }}
              ></Table>
            )
          }}
        </Card>
      </div>
    )
  }
})
