.Table {
  position: relative;
  width: 100%;
  height: 100%;
  .titleBox {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 16px;
    }
  }

  .tableCard {
    height: 100%;
    width: 100%;
  }

  .sortMenu {
    height: 100%;
  }

  :global {
    .ant-card-head-title {
      overflow: initial;
      white-space: initial;
    }
  }
}

.companyItem {
  width: auto;
  display: inline-grid;
  height: 62px;

  .companyNameBox {
    line-height: 22px;
    cursor: pointer;
    display: flex;
    align-items: center;
    // margin-bottom: 10px;
    overflow: hidden;
    .name {
      font-style: normal;
      font-weight: 500;
      font-size: 18px;
      line-height: 18px;
      color: rgba(0, 0, 0, 0.85);
      // margin-right: 8px;
    }

    .copyBtn {
      margin-left: 6px;
      width: 25px;
      height: 20px;
      .iconfontIcon {
        color: #777777;
        display: none;
      }
    }

    &:hover {
      .iconfontIcon {
        display: block;
      }
    }
  }

  .tagBox {
    margin-top: 12px;
    // min-height: 24px;
    display: inline-block;
  }
}
