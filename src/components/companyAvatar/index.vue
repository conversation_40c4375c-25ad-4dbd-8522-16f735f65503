<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-12 16:40:50
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-17 11:36:22
 * @FilePath: /corp-elf-web-consumer/src/components/companyAvatar/index.vue
 * @Description: 
-->
<template>
  <div class="companyAvatar">
    <a-image :width="30" :height="30" :src="logoUrl" :fallback="companyDefaultLogo" :preview="false" />
    <!-- <iconfontIcon icon="icon-qiye" class="iconLogo" /> -->

    <div @click="openCompanyInfo" :class="['companyName', isShowClickState ? 'companyNameHover' : '']">
      {{ companyData.companyName || companyData.entName }}
    </div>
  </div>
</template>

<script setup lang="ts">
import companyDefaultLogo from '@/assets/icon/companyDefaultLogo.svg'
import { message } from 'ant-design-vue'
import { isEmpty } from 'lodash-es'
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const emit = defineEmits(['companyClickHandler'])
const props = withDefaults(
  defineProps<{
    companyData: {
      logo?: string
      entLogo?: string
      cid?: string
      companyId?: string
      companyName?: string
      entName?: string
    }
    avatarSize?: number
    isShowClickState?: boolean
    clickHandlerInSide?: boolean
  }>(),
  {
    avatarSize: 30,
    isShowClickState: true,
    clickHandlerInSide: true
  }
)

const logoUrl = computed(() => {
  if (!isEmpty(props.companyData.logo) || !isEmpty(props.companyData.entLogo)) {
    return props.companyData.logo || props.companyData.entLogo
  }
  return ''
})

// 打开公司
function openCompanyInfo() {
  if (props.clickHandlerInSide) {
    if (props.companyData.companyId || props.companyData.cid) {
      router.push({
        path: '/companyInfo/index',
        name: 'companyInfo-index',
        query: {
          companyId: props.companyData.companyId || props.companyData.cid,
          companyName: props.companyData.companyName || props.companyData.entName
        }
      })
    } else {
      message.warning('该组织详情正在准备中')
    }
  } else {
    emit('companyClickHandler', {
      id: props.companyData.companyId || props.companyData.cid
    })
  }
}
</script>

<style lang="less" scoped>
.companyAvatar {
  display: flex;
  align-items: center;

  .companyName {
    flex: 1;
    font-size: 14px;
    font-weight: 400;
    color: #050505;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px;
  }
  .companyNameHover {
    &:hover {
      // color: @primary-color;
      cursor: pointer;
    }
  }

  .iconLogo {
    // background-color: color(~`colorPalette('@{primary-color}', 0.14) `);
    // color: @primary-color;
    padding: 6px;
    font-size: 18px;
    border-radius: 4px;
  }
}
</style>
