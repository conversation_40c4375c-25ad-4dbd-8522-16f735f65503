<template>
  <div class="emptyBox">
    <a-empty
      :image="empty"
      :image-style="{
        height: '164px',
        display: 'flex',
        'align-items': 'center',
        'justify-content': 'center'
      }"
    >
      <template #description>
        <div class="emptyDescription">
          <slot name="description">
            {{ props.emptyText }}
          </slot>
        </div>
      </template>
    </a-empty>
  </div>
</template>

<script setup lang="ts">
import empty from '@/assets/empty.svg'
const props = withDefaults(defineProps<{ emptyText?: string }>(), { emptyText: '无数据' })
</script>

<style lang="less" scoped>
.emptyBox {
  padding: 40px 0;
  .emptyDescription {
    color: #999999;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: center;
  }
}
</style>
