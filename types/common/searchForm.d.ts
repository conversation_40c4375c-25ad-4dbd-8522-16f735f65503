/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 14:26:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-06 11:28:04
 * @FilePath: /corp-elf-web-consumer/types/common/searchForm.d.ts
 * @Description:
 */

export interface searchFormItemConditionType {
  conditionType: string | undefined
  conditionValue: boolean
  conditionName?: string | undefined
}
export interface searchFormItemData {
  dates: Array<string>
  nums: Array<number>
  values: Array<string>
}

/**
 * SearchConditionField
 */
export interface searchFormItemType {
  /**
   * 词缀类型
   */
  affixType?: string
  /**
   * 字段前缀或后缀值
   */
  affixValue?: string
  conditionType?: ConditionType
  /**
   * fieldType为bool时
   */
  conditionValue?: boolean
  /**
   * 介于、不介于传两个，其它传一个
   */
  dates?: string[]
  /**
   * 枚举类、字符串
   */
  ENUMValues?: string[][]
  fieldIndex?: string
  fieldName?: string
  fieldType?: FieldType
  isIntegratedSort?: boolean
  /**
   * 是否前缀
   */
  isPrefix?: boolean
  /**
   * 布尔类型 是否要判断字段存不存在
   */
  judgeExistField?: boolean
  /**
   * 介于、不介于传两个，其它传一个
   */
  nums?: string[]
  searchIndex?: string
  /**
   * 枚举类、字符串
   */
  values?: string[]
  [property: string]: any
}

export enum ConditionType {
  All = 'ALL',
  Any = 'ANY',
  Bet = 'BET',
  Contains = 'CONTAINS',
  Eq = 'EQ',
  False = 'FALSE',
  Gt = 'GT',
  Gte = 'GTE',
  Have = 'HAVE',
  LTE = 'LTE',
  Lt = 'LT',
  Nbet = 'NBET',
  Neq = 'NEQ',
  Nocontains = 'NOCONTAINS',
  None = 'NONE',
  True = 'TRUE'
}

export enum FieldType {
  Bool1 = 'BOOL_1',
  Bool2 = 'BOOL_2',
  Bool3 = 'BOOL_3',
  Date = 'DATE',
  Enum1 = 'ENUM_1',
  Enum2 = 'ENUM_2',
  ID = 'ID',
  Num1 = 'NUM_1',
  Num2 = 'NUM_2',
  Num3 = 'NUM_3',
  Num4 = 'NUM_4',
  String = 'STRING'
}
