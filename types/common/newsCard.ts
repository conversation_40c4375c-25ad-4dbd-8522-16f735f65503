import { tagType } from './tagType'
/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 17:38:03
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 18:58:55
 * @FilePath: /corp-elf-web-consumer/types/common/newsCard.ts
 * @Description:
 */
export interface newsCardTypeBaseType {
  /** 标题 */
  title: string
  /** 内容 */
  content: string
  /** 简介 */
  summary: string
  /** 发布日期 */
  publishDate: string
  /** 发布时间（时间戳） */
  publishTime: number
  /** 用户id */
  uid: string
  /** 原文地址 */
  url: string
  /** 作者 */
  author: string
  /** 作者url */
  authorUrl: string
  /** 事件类型 */
  events: string[]
  /** 关键字 */
  keywords: string[]
  /** 公司名 */
  companys: string[]
  /** 来源 */
  siteName: string
  /** 数据类型 */
  dataType: string
  /** 行业 */
  industries: string[]
  /** 品牌 */
  brands: string[]
  /** 点赞数 */
  likeCnt: number
  /** 转发数 */
  shareCnt: number
  /** 评论 */
  reviewCnt: number
  /** 互动量 */
  interactionCnt: number
  /** 视频收藏数 */
  collectionVideoCnt: number
  /** 客户公司 */
  customerCompanies: tagType[]
  /** 潜客公司 */
  potentialCustomerCompanies: tagType[]
  /** 熟知公司 */
  wellKnowCustomerCompanies: tagType[]
  /** 竟对公司 */
  rivalCompanies: tagType[]
  /** 竞对品牌 */
  rivalBrands: tagType[]
  /** 关联公司 */
  relationCompanies: tagType[]
  /** 自定义分组公司 */
  customerCollectCompanies: tagType[]
  industryRankIds: string[]
  /** 新闻主题 */
  newsTopics: string[]
  /** 收藏视频数 */
  collectionViedoCnt: number
}
