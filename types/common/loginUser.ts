/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-15 10:42:20
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-06 14:25:23
 * @FilePath: /corp-elf-web-consumer/types/common/loginUser.ts
 * @Description: 登录接口通用返回参数格式
 */

export interface loginBaseResponseType {
  token: string
  userVo: UserVo
}

export interface loginBaseRequestType {
  checkKey: number
  username: string
  password: string
  remember_me: boolean
}

// 用户类型
interface UserVo {
  /** 用户名 */
  username: string
  /** 真实姓名 */
  realName: string
  /** 角色ID */
  roleId: string
  /** 手机号 */
  mobile: string
  /** 角色列表 */
  roles: string[]
  /** 用户组ID */
  groupId: string
  /** 用户ID */
  id: string
}
