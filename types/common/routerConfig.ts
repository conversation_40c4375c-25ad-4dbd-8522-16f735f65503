/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-05-11 15:33:22
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-30 15:29:49
 * @FilePath: /corp-elf-web-consumer/types/common/routerConfig.ts
 * @Description:
 */

import { RouteRecordRaw } from 'vue-router'

// type routerConfig = RouteRecordRaw[] | routerConfig[]

export interface routerConfig extends Omit<RouteRecordRaw, 'component' | 'children'> {
  /** 路线记录的名称。 必须是独一无二的。 */
  name?: string
  /** 渲染组件在项目中的位置 */
  component: string | RouteRecordRaw['component']
  path: string
  /**  重定向地址 */
  redirect?: string
  meta?: Record<string | number | symbol, any>
  children?: routerConfig[]
}

// {
//   name: 'industry-insights-overview',
//   path: '/industryInsights/overview',
//   component: () => import('@/views/industryInsights/index.vue'),
//   meta: {
//     title: '行业洞察',
//     keepAlive: true
//   }
// }
