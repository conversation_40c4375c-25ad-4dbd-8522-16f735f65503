/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-19 17:45:21
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-05 15:25:02
 * @FilePath: /corp-elf-web-consumer/types/common/companyCardType.ts
 * @Description:
 */

import { tagType } from './tagType'

// 公司card类型
export interface companyCardType {
  aboveBachelorRate: number
  annualRevenue: number
  area: string
  avgSalary: number
  bachelorRate: number
  capitalType: string
  checkDate: string
  cid: string
  city: string
  cloudLevel: string
  cloudServer: string[]
  companyClass: string
  contactDesc: string
  copyrightNums: number
  companyId: string
  collectId: string
  dataSource: string
  disabled: number
  email: string
  entLogo: string
  entName: string
  entType: string
  financingNums: number
  financingRound: string
  govCategory: string
  govType: string
  hasContact: boolean
  hasEmail: boolean
  hasPhone: boolean
  hasWebsite: boolean
  id: string
  intentionDegree: string
  itRate: number
  managerRate: number
  matchingDegree: string
  maxMembers: number
  membersStr: string
  minMembers: number
  openStatus: string
  patentNums: number
  phone: string
  powerfulRankScore: string
  province: string
  realCapital: number
  regAddr: string
  regCapital: number
  relationOrgTypeTags: tagType[]
  score: number
  searchContent: string
  searchRegAddr: string
  shortName: string
  showTags: string[]
  softwareNums: number
  startDate: string
  tags: string[]
  totalScore: number
  trademarkNums: number
  website: string
  yxqyTags: string[]
  popupScore: number
  popupDto: {
    popupDescDtos: { isActive: boolean; description: string; score: number }
    score: number
    title: string
  }
}
