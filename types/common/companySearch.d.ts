export interface CompanySearchReqType {
  annualRevenue?: string
  /**
   * 营收
   */
  annualRevenues?: string[]
  /**
   * 资本类型
   */
  capitalType?: string
  /**
   * 资本类型
   */
  capitalTypes?: string[]
  /**
   * 公司id
   */
  cIds?: string[]
  /**
   * 标签
   */
  cloudLevels?: string[]
  /**
   * 公司范围
   */
  companyRange?: string
  /**
   * 企业类型
   */
  enterpriseType?: string
  /**
   * 企业类型
   */
  enterpriseTypes?: string[]
  entNames?: string[]
  establishmentTime?: string
  /**
   * 公司id
   */
  excludeCIds?: string[]
  /**
   * 融资信息
   */
  financingInformation?: string
  /**
   * 融资信息
   */
  financingInformations?: string[]
  groupId?: string
  /**
   * 是否有联系人
   */
  hasContact?: boolean
  /**
   * 是否有邮箱
   */
  hasEmail?: boolean
  /**
   * 是否有联系方式
   */
  hasPhone?: boolean
  /**
   * 是否有官网
   */
  hasWebsite?: boolean
  /**
   * 行业分类
   */
  industryClassification?: string[]
  /**
   * 行业公司分布
   */
  industryCompanyDistributions?: IndustryCompanyDistributionSearchDto[]
  industryCompanyDistributionsOr?: IndustryCompanyDistributionSearchDto[]
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 是否年
   */
  isEstablishmentTimeYear?: boolean
  /**
   * 隐藏潜客
   */
  isHidCustomer?: boolean
  /**
   * 是否是综合排序
   */
  isIntegratedSort?: boolean
  /**
   * 最大营收规模(元)
   */
  maxAnnualRevenue?: number
  /**
   * 最大成立时间（单位、年）
   */
  maxEstablishmentTime?: number
  /**
   * 最大实力分数
   */
  maxRankPowerfulScore?: number
  /**
   * 最大注册资本
   */
  maxRegCapital?: number
  /**
   * 最小营收规模(元)
   */
  minAnnualRevenue?: number
  /**
   * 最小成立时间（单位、年）
   */
  minEstablishmentTime?: number
  /**
   * 最小实力分数
   */
  minRankPowerfulScore?: number
  /**
   * 最小注册资本
   */
  minRegCapital?: number
  /**
   * 名录企业标签
   */
  mlqyTags?: string[]
  /**
   * 企业状态
   */
  openStatus?: string
  /**
   * 企业状态
   */
  openStatuses?: string[]
  /**
   * 排序字段
   */
  orderColumns?: CompanySearchOrderColumnDto[]
  /**
   * 机构类型
   */
  organizationType?: string
  /**
   * 机构类型
   */
  organizationTypes?: string[]
  /**
   * orgType
   */
  orgType?: string[]
  /**
   * 页码（最低1页）
   */
  pageNo?: number
  /**
   * 页数（最高1000条）
   */
  pageSize?: number
  /**
   * 关联产品标签字段
   */
  productLabelField?: string
  /**
   * 关联产品标签值
   */
  productLabelValue?: string
  /**
   * 省区
   */
  provincesAndRegions?: string[]
  regCapital?: string
  /**
   * 排序字段
   */
  scriptOrderColumnDtos?: CompanySearchScriptOrderColumnDto[]
  /**
   * 搜索内容
   */
  searchContent?: string
  searchContentIsMatchPhrase?: boolean
  /**
   * 人员规模
   */
  staffSize?: string
  /**
   * 人员规模(多个)
   */
  staffSizes?: string[]
  /**
   * 标签
   */
  tag2s?: string[]
  /**
   * 标签
   */
  tags?: string[]
  userId?: string
  /**
   * 优选企业
   */
  yxqyTags?: string[]
  [property: string]: any
}

/**
 * IndustryCompanyDistributionSearchDto
 */
export interface IndustryCompanyDistributionSearchDto {
  /**
   * 头腰尾行业id
   */
  industries?: string[]
  /**
   * 类型
   */
  type?: Type
  [property: string]: any
}

/**
 * 类型
 */
export enum Type {
  Bottom = 'BOTTOM',
  Head = 'HEAD',
  Middle = 'MIDDLE'
}

/**
 * CompanySearchOrderColumnDto
 */
export interface CompanySearchOrderColumnDto {
  /**
   * 是否升序
   */
  asc?: boolean
  /**
   * 排序字段
   */
  columnName?: string
  [property: string]: any
}

/**
 * CompanySearchScriptOrderColumnDto
 */
export interface CompanySearchScriptOrderColumnDto {
  /**
   * 是否升序
   */
  asc?: boolean
  /**
   * 脚本
   */
  script?: EsScriptDto
  /**
   * 类型
   */
  type?: string
  [property: string]: any
}

/**
 * 脚本
 *
 * EsScriptDto
 */
export interface EsScriptDto {
  /**
   * 脚本
   */
  inline?: string
  /**
   * 语言
   */
  lang?: string
  /**
   * 请求参数
   */
  params?: { [key: string]: any }
  [property: string]: any
}

export interface CompanySearchResType {
  /** 企业名称 */
  entName: string
  /** 省份 */
  province: string
  /** 城市 */
  city: string
  /** 区 */
  area: string
  /** 联系电话 */
  phone: string
  /** 邮箱 */
  email: string
  /** 企业类型 */
  entType: string
  /** 企业logo */
  entLogo: string
  /** 政府类别 */
  govCategory: string
  /** 政府类型 */
  govType: string
  /** 最大员工数 */
  maxMembers: number
  /** 经营状态 */
  openStatus: string
  /** 成立日期 */
  startDate: string
  /** 注册资本 */
  regCapital: number
  /** 实缴资本 */
  realCapital: number
  /** 注册地址 */
  regAddr: string
  /** 网站 */
  website: string
  /** 核查日期 */
  checkDate: string
  /** 是否有联系方式 */
  hasContact: boolean
  /** 标签列表 */
  tags: string[]
  /** 营销企业标签 */
  yxqyTags: string[]
  /** 资本类型 */
  capitalType: string
  /** 最小员工数 */
  minMembers: number
  /** 年收入 */
  annualRevenue: number
  /** 年收入字符串 */
  annualRevenueStr: string
  /** 是否有邮箱 */
  hasEmail: boolean
  /** 是否有电话 */
  hasPhone: boolean
  /** 是否有网站 */
  hasWebsite: boolean
  /** 搜索内容 */
  searchContent: string
  /** 数据来源 */
  dataSource: string
  /** 云等级 */
  cloudLevel: string
  /** 融资轮次 */
  financingRound: string
  /** 融资信息 */
  financingInfo: string[]
  /** 员工规模字符串 */
  membersStr: string
  /** 注册地址搜索 */
  searchRegAddr: string
  /** 公司类别 */
  companyClass: string
  /** 是否禁用 */
  disabled: number
  /** 实力排名得分 */
  powerfulRankScore: string
  /** 法人代表 */
  legalPerson: string
  /** 公司标签 */
  companyLabels: string[]
  /** 展示的标签 */
  showTags: string[]
  /** 简称 */
  shortName: string
  /** 云服务厂商 */
  cloudServers: string[]
  /** 融资轮次 */
  financingNums: number
  /** it人员占比 */
  itRate: number
  /** 本科人员占比 */
  bachelorRate: number
  /** 本科以上占比 */
  aboveBachelorRate: number
  /** 经理级以上占比 */
  managerRate: number
  /** 平均薪资 */
  avgSalary: number
  /** 专利数量 */
  patentNums: number
  /** 商标数量 */
  trademarkNums: number
  /** 软著数量 */
  softwareNums: number
  /** 作品著作权数量 */
  copyrightNums: number
  /** 客户数量 */
  clientNums: number
  /** 供应商数量 */
  supplierNums: number
  /** 分支数量 */
  branchNums: number
  /** 对外投资数量 */
  investNums: number
  /** 中标数量 */
  zhongBiaoNums: number
  /** 投标数量 */
  touBiaoNums: number
  /** 招标数量 */
  zhaoBiaoNums: number
  /** 评分等级 */
  matchingDegree: string
  /** 意向度等级 */
  intentionDegree: string
  /** 联系人描述 */
  contactDesc: string
  totalScore: number
  /** 使用的产品品牌 */
  productBrands: string[]
  /** 使用的erp产品 */
  erpProducts: string[]
  /** 使用的hrm产品 */
  hrmProducts: string[]
  /** 使用的crm产品 */
  crmProducts: string[]
  /** 使用的协同办公产品 */
  oaProducts: string[]
  /** 使用的产品品牌 */
  productBrandsV2: string[]
  /** 使用的erp产品 */
  erpProductsV2: string[]
  /** 使用的hrm产品 */
  hrmProductsV2: string[]
  /** 使用的crm产品 */
  crmProductsV2: string[]
  /** 使用的协同办公产品 */
  oaProductsV2: string[]
  /** 1/3/6/12个月内有融资 */
  hasCompanyFinancingOneMonth: boolean
  hasCompanyFinancingThreeMonth: boolean
  hasCompanyFinancingSixMonth: boolean
  hasCompanyFinancingTwelveMonth: boolean
  /** 1/3/6/12个月融资次数 */
  companyFinancingNumsOneMonth: number
  companyFinancingNumsThreeMonth: number
  companyFinancingNumsSixMonth: number
  companyFinancingNumsTwelveMonth: number
  /** 最新融资时间 */
  companyLatestFinancingTime: string
  /** 最新融资金额 */
  companyLatestFinancingAmount: number
  /** 1/3/6/12个月内上市 */
  hasCompanyListingOneMonth: boolean
  hasCompanyListingThreeMonth: boolean
  hasCompanyListingSixMonth: boolean
  hasCompanyListingTwelveMonth: boolean
  /** 上市板块 */
  companyListingSecuritiesCategory: string
  /** 1/3/6月内有新改扩建项目 */
  hasExpandProjectOneMonth: boolean
  hasExpandProjectThreeMonth: boolean
  hasExpandProjectSixMonth: boolean
  /** 1/3/6月内有数字化项目 */
  hasDigitalProjectOneMonth: boolean
  hasDigitalProjectThreeMonth: boolean
  hasDigitalProjectSixMonth: boolean
  /** 1/3/6个月内媒体数字化声量 */
  companyDigitalVolume1: number
  companyDigitalVolume3: number
  companyDigitalVolume6: number
  /** 1/3/6个月内媒体数字化声量飙升 */
  digitalVolumeSoar1: boolean
  digitalVolumeSoar3: boolean
  digitalVolumeSoar6: boolean
  /** 1/3/6/12月内有招标 */
  hasProjectTenderOneMonth: boolean
  hasProjectTenderThreeMonth: boolean
  hasProjectTenderSixMonth: boolean
  hasProjectTenderTwelveMonth: boolean
  /** 1/3/6/12月内有中标 */
  hasProjectBidderOneMonth: boolean
  hasProjectBidderThreeMonth: boolean
  hasProjectBidderSixMonth: boolean
  hasProjectBidderTwelveMonth: boolean
  /** 1/3/6个月内招聘IT岗位扩张 */
  companyItRecruitSoar1: boolean
  companyItRecruitSoar3: boolean
  companyItRecruitSoar6: boolean
  /** 1/3/6个月内招聘扩张 */
  companyRecruitSoar1: boolean
  companyRecruitSoar3: boolean
  companyRecruitSoar6: boolean
  /** 是否央企 */
  isCentralCompany: boolean
  /** 是否国企 */
  isStateCompany: boolean
  /** 是否ict集成商 */
  isIctIntegratorCompany: boolean
  /** 是否ict代理商 */
  isIctAgentCompany: boolean
  /** 榜单名录 政府认定 */
  governmentRankList: string[]
  /** 榜单名录 机构榜单 */
  institutionRankList: string[]
  /** ICT资质类型 */
  ictCertificateTypes: string[]
  /** ICT资质证书 */
  ictCertificates: string[]
  /** 三年内年均ICT项目招标数量 */
  ictProjectTenderAvgAnnualNum: number
  /** 三年内年均ICT项目中标数量 */
  ictProjectBidderAvgAnnualNum: number
  /** 三年内年均ICT项目招标金额 */
  ictProjectTenderAvgAnnualAmount: number
  /** 三年内年均ICT项目中标金额 */
  ictProjectBidderAvgAnnualAmount: number
  /** 1/3/6月内有ICT招标项目 */
  hasIctProjectTenderOneMonth: boolean
  hasIctProjectTenderThreeMonth: boolean
  hasIctProjectTenderSixMonth: boolean
  /** 1/3/6月内有ICT中标项目 */
  hasIctProjectBidderOneMonth: boolean
  hasIctProjectBidderThreeMonth: boolean
  hasIctProjectBidderSixMonth: boolean
  /** 招IT岗位占比 */
  companyItJobRate: number
  /** 本科以上占比 */
  companyUniversityRate: number
  /** 管理岗占比 */
  companyManageJobRate: number
  /** 平均薪资 */
  companyAvgMonthlyJobSalary: number
  /** 在招岗位数量 */
  companyJobNum: number
  /** 近3年招标信息数量 */
  projectTenderNum: number
  /** 近3年中标信息数量 */
  projectBidderNum: number
  /** 同集团成员企业数量 */
  companyGroupMemberNum: number
}
