import { executiveDetailResType } from './executiveDetail'
import { paginationBaseRequest } from '~/types/common/pagination'

/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-01 16:26:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-01-22 14:46:11
 * @FilePath: /corp-elf-web-consumer/types/api/executiveSaid/executiveSaidList.ts
 * @Description:
 */
export interface executiveSaidListReqType extends paginationBaseRequest {
  /** 关键词 */
  keyword?: string
  /** 公司id */
  companyUniId?: string
  /** 行业名称 */
  industry?: string
  /** 高管Id */
  executiveId?: string
  /** 岗位展示方式 1 高管说列表、2 公司详情-高管说列表、3高管详情 */
  showPostType?: number
  /** 关注企业传true */
  followCompany?: boolean
  /** 关注高管列表传true */
  relationExecutive?: boolean
  /** 全部标识（关注企业以及关注高管) */
  allFlag?: boolean
  /** 是否精选 */
  isHandpick?: boolean
}

export interface executiveSaidListResType {
  /**
   * 关联公司
   */
  companyExecutive: executiveDetailResType
  /**
   * 言论内容
   */
  executiveComment: string
  id: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 站点
   */
  siteName: string
  /**
   * 标题
   */
  title: string
  /**
   * 原文地址
   */
  url: string
}
