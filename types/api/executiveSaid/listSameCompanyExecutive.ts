/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-01 16:22:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-02 10:52:48
 * @FilePath: /corp-elf-web-consumer/types/api/executiveSaid/listSameCompanyExecutive.ts
 * @Description:
 */

import { paginationBaseRequest } from '~/types/common/pagination'
import { executiveDetailResType } from './executiveDetail'

export interface listSameCompanyExecutiveReqType extends paginationBaseRequest {
  /**
   * 高管id
   */
  executiveId: string
}

export interface listSameCompanyExecutiveResType extends executiveDetailResType {}
