/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-01 16:22:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-09 10:38:48
 * @FilePath: /corp-elf-web-consumer/types/api/executiveSaid/executiveDetail.ts
 * @Description:
 */

export interface executiveDetailReqType {
  /**
   * 高管id
   */
  executiveId?: string
}

export interface executiveDetailResType {
  /**
   * 关联公司
   */
  companyName: string
  /**
   * 关联公司id
   */
  companyUniId: string
  /**
   * 高管id
   */
  executiveId: string
  /**
   * 高管名称
   */
  executiveName: string
  /**
   * 最新摘录时间
   */
  latestExcerptTime: number
  /**
   * 高管任职信息
   */
  postList: postList[]
}

export interface postList {
  /**
   * 职位名称
   */
  post: string
  /**
   * 状态 1有效 0无效
   */
  status: number
}
