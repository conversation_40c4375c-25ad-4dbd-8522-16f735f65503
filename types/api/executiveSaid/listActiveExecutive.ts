/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-01 16:22:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-08 09:57:09
 * @FilePath: /corp-elf-web-consumer/types/api/executiveSaid/listActiveExecutive.ts
 * @Description:
 */
import { postList } from './executiveDetail'

export interface listActiveExecutiveReqType {
  /** 是关注企业传true 不是关注企业传false */
  followCompany: boolean
  industry?: string
}

export interface listActiveExecutiveResType {
  /**
   * 公司名称
   */
  companyName: string
  /**
   * 公司id
   */
  companyUniId: string
  /**
   * 高管id
   */
  executiveId: string
  /**
   * 高管名称
   */
  executiveName: string
  /**
   * 言论次数
   */
  num: number
  postList: postList[]
}
