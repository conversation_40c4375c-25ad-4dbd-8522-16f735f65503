/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-10-15 17:30:02
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-10-21 14:50:07
 * @FilePath: /corp-elf-web-consumer/types/api/executiveSaid/relationList.ts
 * @Description:
 */
import { paginationBaseRequest } from './../../common/pagination'

export interface executiveRelationListReqType extends paginationBaseRequest {
  keyword?: string
}

export interface executiveRelationListResType {
  companyExecutivePostList?: CompanyExecutivePostVo[]
  executiveId: string
  executiveName: string
  num: string
}

export interface CompanyExecutivePostVo {
  companyName: string
  companyUniId: string
  post: string
}
