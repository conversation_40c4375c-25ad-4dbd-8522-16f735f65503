/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-07-01 16:22:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-05 15:54:53
 * @FilePath: /corp-elf-web-consumer/types/api/executiveSaid/listCompanyExecutive.ts
 * @Description:
 */

import { paginationBaseRequest } from '~/types/common/pagination'
import { executiveDetailResType } from './executiveDetail'

export interface listCompanyExecutiveReqType extends paginationBaseRequest {
  /** 关键字 */
  keyword?: string
  /** 公司id */
  companyUniId: string
}

export interface listCompanyExecutiveResType extends executiveDetailResType {}
