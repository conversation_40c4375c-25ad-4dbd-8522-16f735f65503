/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-05-11 15:30:38
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 11:44:40
 * @FilePath: /corp-elf-web-consumer/types/api/auth/getUserPermission.ts
 * @Description:
 */
import { routerConfig } from '~/types/common/routerConfig'

export interface getUserPermissionRequestType {
  token: string
}

export interface getUserPermissionResponseType {
  menu: routerConfig[]
  sysTenantConfigVo: {
    customModel: boolean
    customModelLimit: number
    userLimit: number
  }
}
