/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-04 19:42:21
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-11-07 16:57:13
 * @FilePath: /corp-elf-web-consumer/types/api/auth/bindMp.ts
 * @Description:微信公众号登录绑定
 */
export interface bindMpRequestType {
  provider: 'wechatMp'
  /** ticket */
  code: string
  /** 手机号 */
  phone: string
  /** 验证码 */
  phoneCode: string
  /** 密码 */
  password: string
  /** 邀请码 */
  inviteCode?: string
}
