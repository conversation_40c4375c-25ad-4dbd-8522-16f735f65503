/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-29 11:42:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 16:00:04
 * @FilePath: /corp-elf-web-consumer/types/api/userMember/payment.ts
 * @Description:
 */
export interface userMemberPaymentReqType {
  /** 购买会员月数 */
  memberMonth: number
  /** 总价 */
  totalPrice: number
  /** 优惠价 */
  discountPrice: number
  /** 实付价 */
  paymentPrice: number
  /** 优惠卷id */
  couponId?: string
  /** 会员类型 */
  memberType: string
  /** 支付类型 */
  payType: 'WECHAT_PAY_NATIVE' | 'WECHAT_PAY_JSAPI'
}
export interface userMemberPaymentResType {
  /** 支付链接 */
  paymentUrl: string
  /** 预支付id */
  prepayId: string
  /** 支付链接有效时间 */
  effectiveTime: number
  /** 支付状态校验凭证 */
  checkCode: string
}
