/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 10:55:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 11:06:27
 * @FilePath: /corp-elf-web-consumer/types/api/customer/customerGroup.ts
 * @Description:
 */

export interface customerGroupRequestType {
  /** 分组id */
  collectId: string
  /** 公司id */
  companyId: string[]
  /** 公司集合 */
  companyMaps: Record<string, string>
  /** 分组名称 */
  collectName?: string
}

export type customerGroupResponseType = boolean
