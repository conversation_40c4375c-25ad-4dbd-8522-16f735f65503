/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-06 17:23:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-02-06 17:24:41
 * @FilePath: /corp-elf-web-consumer/types/api/customer/receive.ts
 * @Description:
 */
export interface receiveRequestType {
  collectId: string
  company: companyType[]
  appType: 'LITE'
}

interface companyType {
  companyId: string
  companyName: string
}
