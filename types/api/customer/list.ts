/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-06 17:23:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-02-07 14:58:59
 * @FilePath: /corp-elf-web-consumer/types/api/customer/list.ts
 * @Description:
 */

import { paginationBaseRequest } from '~/types/common/pagination'

export interface customerListRequestType extends paginationBaseRequest {
  /** 用户分组ID，当分组为全部时，传-1 */
  collectId: string
  isFirst: false
  customerType: 'PERSONAL'
  isAsc: false
  orderColumns: { columnName: string; asc: boolean }[]
  // {
  //   columnName: 'powerful_rank_score'
  //   asc: false
  // }
}

export interface customerListResponseType {
  id: string
  entName: string
  website: string
  regCapital: string
  industry: string
  staffSize: string
  companyId: string
  /** 0不是客户 1是客户 */
  isCustomer: number
  customerLevel: string
  actions: Actions[]
  collectId: string
  collectName: string
  annualRevenue: string
  powerfulRankScore: string
  userName: string
  userId: string
  receiveTime: string
  showTags: string[]
  clueNumbers: string
  province: string
  city: string
  events: string
}

// 公司操作
export interface Actions {
  label: string
  type: string
  disabled: boolean
}
