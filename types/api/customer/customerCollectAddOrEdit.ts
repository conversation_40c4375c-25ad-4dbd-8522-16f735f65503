/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 11:23:55
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 11:33:54
 * @FilePath: /corp-elf-web-consumer/types/api/customer/customerCollectAddOrEdit.ts
 * @Description:
 */
export interface customerCollectAddOrEditRequestType {
  /** 分组id，新增不用传 */
  collectId?: string
  /** 分组名称 */
  collectName: string
  /** 应用表示 */
  appType: 'LITE'
}

export type customerCollectAddOrEditResponseType = boolean
