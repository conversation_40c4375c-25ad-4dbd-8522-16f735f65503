/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-06 17:23:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 11:22:58
 * @FilePath: /corp-elf-web-consumer/types/api/customer/getCustomerCollectGroup.ts
 * @Description:
 */

export interface getCustomerCollectGroupRequestType {
  appType: 'LITE'
}

export interface getCustomerCollectGroupResponseType {
  /** 分类id */
  collectId: string
  /** 分类名称 */
  collectName: string
  /** 公司数量 */
  companyNum: number
}
