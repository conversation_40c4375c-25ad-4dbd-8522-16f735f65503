/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 12:00:15
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 12:02:11
 * @FilePath: /corp-elf-web-consumer/types/api/common/getCompanyType.ts
 * @Description:
 */

export interface getCompanyTypeReqType {
  /** 公司名称或者公司id */
  companyIdOrCompanyName: string
}

export interface getCompanyTypeResType {
  /** 是否能打开详情页面 */
  isEnterDetail: boolean
  /** 无法打开的提示语 */
  notEnterMsg: string
  /** 公司类型 */
  companyType: string
}
