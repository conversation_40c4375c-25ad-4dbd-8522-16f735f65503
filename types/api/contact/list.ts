/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 14:07:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 14:41:48
 * @FilePath: /corp-elf-web-consumer/types/api/contact/list.ts
 * @Description:
 */

import { paginationBaseRequest } from './../../common/pagination'

export interface contactListReqType extends paginationBaseRequest {
  /** 公司id */
  companyId: string
  /** 类型 */
  type: ContactTypeEnum
}

export interface contactListResType {
  name: string
  value: string
  type: ContactTypeEnum
  job: string
  dataFrom: string[]
}

/**
 ** 全部 ALL
 ** 手机 PHONE
 ** 固话 FIXED
 ** 邮箱 MAIL
 ** 领英 LINKED
 ** 脉脉 MAIMAI
 ** 传真 FACSIMILE
 ** 电话 TELEPHONE
 */
export type ContactTypeEnum = 'ALL' | 'PHONE' | 'FIXED' | 'MAIL' | 'LINKED' | 'MAIMAI' | 'FACSIMILE' | 'TELEPHONE'
