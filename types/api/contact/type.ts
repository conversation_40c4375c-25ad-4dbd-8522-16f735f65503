/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 14:07:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 14:43:04
 * @FilePath: /corp-elf-web-consumer/types/api/contact/type.ts
 * @Description:
 */

import { ContactTypeEnum } from './list'

export interface contactTypeReqType {}
export interface contactTypeResType {
  disabled: boolean
  label: string
  value: ContactTypeEnum
}
