/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-09 17:53:12
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-09 17:53:20
 * @FilePath: /corp-elf-web-consumer/types/api/layer/detail/showDetail.d.ts
 * @Description: 
 */
export interface layerDetailShowDetailResType {
  detailInfo: {
    id: string
    briefIntroduction: string // 简介
    city: string // 城市
    clientCnt: string // 圈层名称
    companyCnt: string // 圈层名称
    contactAddress: string // 联系地址
    contactInformation: string // 联系方式
    isReceive: boolean // 是否领取
    layerLevel: string // 圈层等级
    layerName: string // 圈层名称
    managerName: string // 会长/理事长
    province: string // 省份
    secretaryGeneralName: string // 秘书长
    website: string // 官网
  }
}
