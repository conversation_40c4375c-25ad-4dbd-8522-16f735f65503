import { paginationBaseRequest } from './../../common/pagination'
/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-04 11:44:57
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-11 09:41:24
 * @FilePath: /corp-elf-web-consumer/types/api/layer/showLayer.ts
 * @Description:
 */
export interface ShowLayerRequest extends paginationBaseRequest {
  filter: {
    /** 行业id */
    industryId?: string
    isHidCustomer?: boolean
    isOwner?: boolean
    layerName?: string
    isOnlyOwner?: boolean
    orderColumns?: [
      {
        columnName: 'company_cnt' | 'client_cnt2'
        asc: boolean
      }
    ]
  }
}

export interface ShowLayerResponse {
  id: string
  layerName: string
  website: string
  province: string
  city: string
  layerLevel: string
  managerName: string
  companyCnt: number
  clientCnt: number
  isReceive: boolean
}
