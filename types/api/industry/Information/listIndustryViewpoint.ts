/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-02 17:14:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-03-14 14:48:09
 * @FilePath: /corp-elf-web-consumer/types/api/industry/Information/listIndustryViewpoint.ts
 * @Description:
 */

export interface ListIndustryViewpointRequest {
  industryId: string
  viewpointType?: ViewpointType
}

export type ViewpointType = 'CHALLENGE' | 'CIRCUMSTANCE' | 'CURRENT_SITUATION' | 'INNOVATE' | 'OPPORTUNITY' | 'OTHER' | 'PROSPECT'

export interface ListIndustryViewpointResponse {
  author: string
  publishDate: string
  title: string
  viewpointList: ViewpointList[]
}

export interface ViewpointList {
  viewpointContent: string
  viewpointTypeName: string
}
