/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-02 17:14:51
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-02-06 17:32:52
 * @FilePath: /corp-elf-web-consumer/types/api/industry/Information/listIndustryExhibition.ts
 * @Description:
 */
export interface ListIndustryExhibitionRequest {
  industryId: string
}

export interface ListIndustryExhibitionResponse {
  endExhibitionDate: string
  exhibitionDate: string
  id: string
  name: string
  place: string
  sourceUrl: string
}
