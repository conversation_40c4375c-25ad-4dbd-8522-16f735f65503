/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-03-12 18:38:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-12 20:55:53
 * @FilePath: /corp-elf-web-consumer/types/api/industry/Information/industryCompanyViewpoint.ts
 * @Description:
 */

import { paginationBaseRequest } from '~/types/common/pagination'

export interface industryCompanyViewpointRequestType extends paginationBaseRequest {
  /**  行业ID */
  industryId: string
  /**  观点类型 */
  viewpointType: string
  /** 关键字 */
  keyword?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
  /** 公司id 点击加载更多要传 */
  companyUniId?: string
}

export interface industryCompanyViewpointResponseType {
  /** 关联公司全称 */
  companyName: string
  /** 关联公司简称 */
  companyAbbr: string
  /** 关联公司ID */
  companyUniId: string
  /** 是否有加载更多按钮 */
  moreData: boolean

  /** 公司观点列表 */
  list: companyCaseViewpointType[]
}

export interface companyCaseViewpointType {
  /** 关联公司全称 */
  companyName: string
  /** 关联公司简称 */
  companyAbbr: string
  /** 关联公司ID */
  companyUniId: string
  /** id */
  id: string
  /** 作者 */
  author: string
  /** 标题 */
  title: string
  /** 来源地址 */
  sourceUrl: string
  /** 信息类型 news新闻 */
  informationType: string
  /** 内容 */
  viewpointContent: string
  /**  */
  viewpointTypeName: string
  /** 发布时间 */
  publishDate: string
}
