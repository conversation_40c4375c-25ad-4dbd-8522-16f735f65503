/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-02 17:14:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-02-05 15:25:11
 * @FilePath: /corp-elf-web-consumer/types/api/industry/Information/getIndustryInformationConfig.ts
 * @Description: 行业洞察-获取配置接口（行业分类、洞见类型）
 */

export interface GetIndustryInformationConfigResponse {
  unlockIndustryList: UnlockIndustryList[]
  viewpointTypeList: ViewpointTypeList[]
}

export interface UnlockIndustryList {
  industryId: string
  industryName: string
}

export interface ViewpointTypeList {
  viewpointTypeCode: 'CHALLENGE' | 'CIRCUMSTANCE' | 'CURRENT_SITUATION' | 'INNOVATE' | 'OPPORTUNITY' | 'OTHER' | 'PROSPECT'
  viewpointTypeName: string
}
