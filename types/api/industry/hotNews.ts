/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-05 11:08:44
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-05-22 11:17:45
 * @FilePath: /corp-elf-web-consumer/types/api/industry/hotNews.ts
 * @Description:
 */

export interface HotNewsRequest {
  industryId?: string
  collectId?: string
  eventTypes?: string[]
  timeRange?: string
}

export interface HotNewsResponse {
  customerCollectDetails: CustomerCollectDetail[]
  events: string[]
  industries: string[]
  keyWords: string[]
  newsAuthor: string
  newsContent: string
  summary: string
  newsDataFrom: string
  newsTitle: string
  oriUrl: string
  publishDate: string
  rankList: RankList[]
  companyList: CompanyList[]
  newsTopics: string[]
}

export interface CustomerCollectDetail {
  collectId: string
  collectName: string
  relations: Relation[]
}

export interface Relation {
  companyId: string
  companyName: string
  id: string
}

export interface RankList {
  id: string
  rankTitle: string
}

export interface CompanyList {
  companyName: string
  companyUniId: string
}
