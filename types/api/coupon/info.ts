export interface couponInfoReqType {}
export interface couponInfoResType {
  /** 优惠卷Id */
  couponId: string
  /** 优惠卷类型 */
  couponType: string
  /** 优惠卷名称 */
  couponName: string
  /** 优惠卷有效时间 */
  effectiveTime: number
  /** 优惠卷折扣参数 */
  discountParam: baseDiscountParams
}

interface baseDiscountParams {
  /** 类型 */
  type: string
  /** 时长 */
  afterMonth: number
  /** 优惠率 */
  discountRate: number
}
//  DISCOUNT
