/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-02 14:52:22
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-16 11:04:25
 * @FilePath: /corp-elf-web-consumer/types/api/executeSaidExplain/executeSaidExplainHistory.ts
 * @Description: 
 */
import { paginationBaseRequest } from './../../common/pagination'
export interface executeSaidExplainHistoryReqType extends paginationBaseRequest {
  executiveId: string
}
export interface executeSaidExplainHistoryResType {
  id: string
  content: string
  /* 0: 默认，1：停止 */
  contentType: 1 | 2
  /* 1: 问题，2：回答 */
  isStop: 0 | 1
}
