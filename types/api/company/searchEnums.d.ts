/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 16:50:35
 * @FilePath: /corp-elf-web-consumer/types/api/company/searchEnums.d.ts
 * @Description: 
 */
export type SearchEnumsResType = Record<string, EnumsItem>

interface EnumsItem {
  fieldIndex: string
  value: EnumsItemValue
}
interface EnumsItemValue {
  label: string
  value: string
  children?: EnumsItemValue[]
}
