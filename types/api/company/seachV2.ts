/**
 * CompanySearchRequestV2
 */
export interface Request {
  annualRevenue?: null | string
  /**
   * 营收
   */
  annualRevenues?: string[] | null
  /**
   * 资本类型
   */
  capitalType?: null | string
  /**
   * 资本类型
   */
  capitalTypes?: string[] | null
  /**
   * 公司id
   */
  cIds?: string[] | null
  /**
   * 标签
   */
  cloudLevels?: string[] | null
  /**
   * 公司范围
   */
  companyRange?: null | string
  conditions?: SearchConditionField[] | null
  /**
   * 企业类型
   */
  enterpriseType?: null | string
  /**
   * 企业类型
   */
  enterpriseTypes?: string[] | null
  entNames?: string[] | null
  establishmentTime?: null | string
  /**
   * 公司id
   */
  excludeCIds?: string[] | null
  /**
   * 融资信息
   */
  financingInformation?: null | string
  /**
   * 融资信息
   */
  financingInformations?: string[] | null
  groupId?: null | string
  /**
   * 是否有联系人
   */
  hasContact?: boolean | null
  /**
   * 是否有邮箱
   */
  hasEmail?: boolean | null
  /**
   * 是否有联系方式
   */
  hasPhone?: boolean | null
  /**
   * 是否有官网
   */
  hasWebsite?: boolean | null
  /**
   * 行业分类
   */
  industryClassification?: string[] | null
  /**
   * 行业公司分布
   */
  industryCompanyDistributions?: IndustryCompanyDistributionSearchDto[] | null
  /**
   * 行业id
   */
  industryId?: null | string
  /**
   * 筛选条件关系 true:且 false:或
   */
  isAnd?: boolean | null
  /**
   * 是否年
   */
  isEstablishmentTimeYear?: boolean | null
  /**
   * 隐藏潜客
   */
  isHidCustomer?: boolean | null
  /**
   * 是否是综合排序
   */
  isIntegratedSort?: boolean | null
  /**
   * 最大营收规模(元)
   */
  maxAnnualRevenue?: number | null
  /**
   * 最大成立时间（单位、年）
   */
  maxEstablishmentTime?: number | null
  /**
   * 最大实力分数
   */
  maxRankPowerfulScore?: number | null
  /**
   * 最大注册资本
   */
  maxRegCapital?: number | null
  /**
   * 最小营收规模(元)
   */
  minAnnualRevenue?: number | null
  /**
   * 最小成立时间（单位、年）
   */
  minEstablishmentTime?: number | null
  /**
   * 最小实力分数
   */
  minRankPowerfulScore?: number | null
  /**
   * 最小注册资本
   */
  minRegCapital?: number | null
  /**
   * 名录企业标签
   */
  mlqyTags?: string[] | null
  /**
   * 企业状态
   */
  openStatus?: null | string
  /**
   * 企业状态
   */
  openStatuses?: string[] | null
  /**
   * 排序字段
   */
  orderColumns?: CompanySearchOrderColumnDto[] | null
  /**
   * 机构类型
   */
  organizationType?: null | string
  /**
   * 机构类型
   */
  organizationTypes?: string[] | null
  /**
   * orgType
   */
  orgType?: string[] | null
  /**
   * 页码（最低1页）
   */
  pageNo?: number | null
  /**
   * 页数（最高1000条）
   */
  pageSize?: number | null
  /**
   * 关联产品标签字段
   */
  productLabelField?: null | string
  /**
   * 关联产品标签值
   */
  productLabelValue?: null | string
  /**
   * 省区
   */
  provincesAndRegions?: string[] | null
  regCapital?: null | string
  /**
   * 排序字段
   */
  scriptOrderColumnDtos?: CompanySearchScriptOrderColumnDto[] | null
  /**
   * 搜索内容
   */
  searchContent?: null | string
  searchContentIsMatchPhrase?: boolean | null
  /**
   * 人员规模
   */
  staffSize?: null | string
  /**
   * 人员规模(多个)
   */
  staffSizes?: string[] | null
  /**
   * 标签
   */
  tag2s?: string[] | null
  /**
   * 标签
   */
  tags?: string[] | null
  userId?: null | string
  /**
   * 优选企业
   */
  yxqyTags?: string[] | null
}

/**
 * SearchConditionField
 */
export interface SearchConditionField {
  /**
   * 词缀类型
   */
  affixType?: null | string
  /**
   * 字段前缀或后缀值
   */
  affixValue?: null | string
  conditionType?: ConditionType
  /**
   * fieldType为bool时
   */
  conditionValue?: boolean | null
  /**
   * 介于、不介于传两个，其它传一个
   */
  dates?: string[] | null
  fieldIndex?: null | string
  fieldName?: null | string
  fieldType?: FieldType
  isIntegratedSort?: boolean | null
  /**
   * 是否前缀
   */
  isPrefix?: boolean | null
  /**
   * 布尔类型 是否要判断字段存不存在
   */
  judgeExistField?: boolean | null
  /**
   * 介于、不介于传两个，其它传一个
   */
  nums?: string[] | null
  searchIndex?: null | string
  /**
   * 枚举类、字符串
   */
  values?: string[] | null
}

export enum ConditionType {
  All = 'ALL',
  Any = 'ANY',
  Bet = 'BET',
  Contains = 'CONTAINS',
  Eq = 'EQ',
  False = 'FALSE',
  Gt = 'GT',
  Gte = 'GTE',
  Have = 'HAVE',
  LTE = 'LTE',
  Lt = 'LT',
  Nbet = 'NBET',
  Neq = 'NEQ',
  Nocontains = 'NOCONTAINS',
  None = 'NONE',
  True = 'TRUE'
}

export enum FieldType {
  Bool1 = 'BOOL_1',
  Bool2 = 'BOOL_2',
  Bool3 = 'BOOL_3',
  Date = 'DATE',
  Enum1 = 'ENUM_1',
  Enum2 = 'ENUM_2',
  ID = 'ID',
  Num1 = 'NUM_1',
  Num2 = 'NUM_2',
  Num3 = 'NUM_3',
  Num4 = 'NUM_4',
  String = 'STRING'
}

/**
 * IndustryCompanyDistributionSearchDto
 */
export interface IndustryCompanyDistributionSearchDto {
  /**
   * 头腰尾行业id
   */
  industries?: string[] | null
  /**
   * 类型
   */
  type?: Type
}

/**
 * 类型
 */
export enum Type {
  Bottom = 'BOTTOM',
  Head = 'HEAD',
  Middle = 'MIDDLE'
}

/**
 * CompanySearchOrderColumnDto
 */
export interface CompanySearchOrderColumnDto {
  /**
   * 是否升序
   */
  asc?: boolean | null
  /**
   * 排序字段
   */
  columnName?: null | string
}

/**
 * CompanySearchScriptOrderColumnDto
 */
export interface CompanySearchScriptOrderColumnDto {
  /**
   * 是否升序
   */
  asc?: boolean | null
  /**
   * 脚本
   */
  script?: EsScriptDto
  /**
   * 类型
   */
  type?: null | string
}

/**
 * 脚本
 *
 * EsScriptDto
 */
export interface EsScriptDto {
  /**
   * 脚本
   */
  inline?: null | string
  /**
   * 语言
   */
  lang?: null | string
  /**
   * 请求参数
   */
  params?: { [key: string]: any }
}
