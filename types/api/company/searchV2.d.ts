/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-06 11:21:54
 * @FilePath: /corp-elf-web-consumer/types/api/company/searchV2.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from '~/types/common/pagination'
import { CompanySearchResType, CompanySearchReqType } from '~/types/common/companySearch'

export interface SearchV2ReqType extends CompanySearchReqType, paginationBaseRequest {}

export type SearchV2ResType = paginationBaseResponse<CompanySearchResType>
