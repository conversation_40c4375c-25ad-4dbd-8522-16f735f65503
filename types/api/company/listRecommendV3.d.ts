/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-05 15:09:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-05 15:39:09
 * @FilePath: /corp-elf-web-consumer/types/api/company/listRecommendV3.d.ts
 * @Description:
 */

import { CompanySearchResType } from '../../common/companySearch'
import { paginationBaseRequest, paginationBaseResponse } from './../../common/pagination'

export interface ListRecommendV3ReqType {}

export type ListRecommendV3ResType = paginationBaseResponse<CompanySearchResType>
