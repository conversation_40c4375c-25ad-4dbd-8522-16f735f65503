/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-04 14:23:53
 * @FilePath: /corp-elf-web-consumer/types/api/company/showSearchConditionV2.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from '~/types/common/pagination'

export interface ShowSearchConditionV2ReqType extends paginationBaseRequest {
  /**
   * 条件名称
   */
  name?: string
  /**
   * 类型(默认:COMPANY_SEARCH,可传COMPANY_SEARCH、RECOMMEND_SEARCH)
   */
  type?: 'COMPANY_SEARCH' | 'RECOMMEND_SEARCH'
  /**
   * 版本号(默认:V_2_0)
   */
  version?: string
}

export interface CompanySearchConditionDto {
  /**
   * id
   */
  id: string
  /**
   * 名字
   */
  name: string
  /**
   * 搜索内容
   */
  searchContent: { [key: string]: any }
}

export interface OrderItem {
  /**
   * 是否正序排列，默认 true
   */
  asc?: boolean
  /**
   * 需要进行排序的字段
   */
  column?: string
  [property: string]: any
}

export interface ShowSearchConditionV2ResType extends paginationBaseResponse<CompanySearchConditionDto> {
  /**
   * countId
   */
  countId?: string
  /**
   * 是否命中count缓存
   */
  hitCount?: boolean
  /**
   * 是否进行 count 查询
   */
  isSearchCount?: boolean
  /**
   * countId
   */
  maxLimit?: number
  /**
   * 自动优化 COUNT SQL
   */
  optimizeCountSql?: boolean
  /**
   * 排序字段信息
   */
  orders?: OrderItem[]
}
