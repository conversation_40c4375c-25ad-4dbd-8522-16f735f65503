/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:40:00
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/financing.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailFinancingResType extends paginationBaseRequest {
  cId: string
}

export type CompanyDetailFinancingReqType = paginationBaseResponse<{
  /**
   * id
   */
  id: number
  /**
   * companyId
   */
  companyId: string
  /**
   * companyName
   */
  companyName: string
  /**
   * uniKey
   */
  uniKey: string
  /**
   * dataFrom
   */
  dataFrom: number
  /**
   * isDeleted
   */
  isDeleted: number
  /**
   * createTime
   */
  createTime: Date | string
  /**
   * updateTime
   */
  updateTime: Date | string
  extParam: string
  /**
   * 披露日期
   */
  disclosureDate: string
  /**
   * 新闻来源
   */
  newsSources: string
  /**
   * 投资方
   */
  investor: string
  /**
   * 估值
   */
  valuation: string
  /**
   * 比例
   */
  proportion: string
  /**
   * 事件日期
   */
  eventDate: string
  /**
   * 交易金额
   */
  transactionAmount: string
  /**
   * 融资轮次
   */
  financingRounds: string
  companyUniId: string
}>
