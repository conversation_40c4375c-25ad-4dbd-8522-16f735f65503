/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:56:40
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/bidding.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailBiddingReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailBiddingResType = paginationBaseResponse<{
  /** 项目名称 */
  title: string
  /** 采购金额 */
  purchaseAmount: string
  /** 发布日期 */
  publishDate: string
  //    private String detailUrl;
  /** 招标/采购单位 */
  tender: string
  /** 公司ID */
  cId: string
  /** 数据来源 */
  dataFrom: number
  /**
   * A、b 类企业
   */
  companyClass: 'A' | 'B'

  /**
   * 是否置灰 0否 1是
   */
  disabled: 0 | 1
}>
