/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:41:19
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/shareholder.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailShareholderReqType extends paginationBaseRequest {
  cId: string
}

export type CompanyDetailShareholderResType = paginationBaseResponse<{
  /** 出资比例 */
  contributionRatio: string
  /** 认缴出资日期 */
  regDate: string
  /** 股东 */
  shareholder: string
  /** 认缴出资额 */
  regCapital: string
}>
