/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 19:02:18
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/head.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailHeadReqType {
  companyId: string
}

export type CompanyDetailHeadResType = {
  /** 企业名称 */
  entName: string
  /** 法人代表 */
  legalPerson: string
  /** 注册资本 */
  regCap: string
  /** 成立日期 */
  startDate: string
  /** 经营状态 */
  openStatus: string
  /** 企业logo */
  entLogo: string
  /** 公司ID */
  cId: string
} | null
