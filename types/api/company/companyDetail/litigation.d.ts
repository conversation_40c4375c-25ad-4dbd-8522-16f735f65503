/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:21:44
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/litigation.d.ts
 * @Description: 
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailLitigationReqType extends paginationBaseRequest {
  cId: string
}

export type CompanyDetailLitigationResType = paginationBaseResponse<{
  /** 案号 */
  caseNo: string
  /** 案由 */
  causeAction: string
  /** 日期 */
  caseDate: string
  /** 案件身份 */
  caseStatus: string
  /** 案件名称 */
  caseName: string
  /** 案件名称网址 */
  caseUrl: string
}>
