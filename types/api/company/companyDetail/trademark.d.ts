import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailTrademarkReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailTrademarkResType = paginationBaseResponse<{
  /** 注册号 */
  regNo: string
  /** 国际分类 */
  internationalClassification: string
  /** 申请日期 */
  requestDate: string
  /** 商标 */
  trademark: string
  /** 详情 */
  detailContent: string
  /** 商标名称 */
  trademarkName: string
  /** 商标状态 */
  status: string
  /** 数据来源 */
  dataFrom: string
}>
