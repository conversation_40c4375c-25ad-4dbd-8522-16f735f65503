/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:23:47
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/certificate.d.ts
 * @Description: 
 */
import { paginationBaseRequest, paginationBaseResponse } from '../../../common/pagination'

export interface CompanyDetailCertificateReqType extends paginationBaseRequest {
  cId: string
}
export type CompanyDetailCertificateResType = paginationBaseResponse<{
  /** 截止日期 */
  closingDate: string
  /** 证书请求编号 */
  certificateRequestNo: string
  /** 发证日期 */
  issueDate: string
  /** 证书编号 */
  certificateNo: string
  /** 证书类型 */
  certificateType: string
}>
