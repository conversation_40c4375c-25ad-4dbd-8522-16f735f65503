import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailPatentReqType extends paginationBaseRequest {
  cId: string
}

export type CompanyDetailPatentResType = paginationBaseResponse<{
  /** 申请公布号 */
  requestPublicationNo: string
  /** 申请公布日 */
  publicationDate: string
  /** 专利名称 */
  patentName: string
  /** 申请号 */
  publicationNo: string
  /** 专利类型 */
  patentType: string
  /** 详情 */
  detailContent: string
}>
