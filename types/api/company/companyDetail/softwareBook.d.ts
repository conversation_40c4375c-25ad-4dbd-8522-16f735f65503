/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:25:17
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/softwareBook.d.ts
 * @Description: 
 */
import { paginationBaseResponse, paginationBaseRequest } from '../../../common/pagination'

export interface CompanyDetailSoftwareBookReqType extends paginationBaseRequest {
  cId: string
}
export type CompanyDetailSoftwareBookResType = paginationBaseResponse<{
  /** 版本号 */
  versionNo: string
  /** 分类号 */
  classificationNo: string
  /** 软件简称 */
  softwareAbbreviation: string
  /** 批准日期 */
  checkDate: string
  /** 软件全称 */
  softwareName: string
  /** 详情 */
  detailContent: string
  /** 登记号 */
  registrationNo: string
}>
