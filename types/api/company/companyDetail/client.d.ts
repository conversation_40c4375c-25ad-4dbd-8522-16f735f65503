/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:57:47
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/client.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailClientReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailClientResType = paginationBaseResponse<{
  /** 企业logo */
  entLogo: string
  /** 客户名称 */
  customer: string
  /** 销售份额 */
  salesShare: string
  /** 销售金额 */
  salesAmount: string
  /** 报告期 */
  reportingPeriod: string
  /** 数据来源 */
  dataSources: string
  /** 关系 */
  relationship: string
  /** 公司ID */
  cId: string
  /** 公司简称 */
  ShortName: string
  /**
   * A、b 类企业
   */
  companyClass: 'A' | 'B'

  /**
   * 是否置灰 0否 1是
   */
  disabled: 0 | 1
}>
