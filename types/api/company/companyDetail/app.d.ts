/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:54:58
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/app.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailAppReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailAppResType = paginationBaseResponse<{
  /** 应用名称 */
  name: string
  /** logo图片地址 */
  logo: string
  /** 应用类型名称 */
  kindName: string
  /** 下载描述 */
  downDesc: string
  /** 版本号 */
  versionCode: string
  /** logo信息 */
  logoMsg: string
}>
