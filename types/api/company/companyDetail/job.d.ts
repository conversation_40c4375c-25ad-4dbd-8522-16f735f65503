/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:53:44
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/job.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailJobReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailJobResType = paginationBaseResponse<{
  /** 职位名称 */
  jobName: string
  /** 薪资 */
  salary: string
  /** 发布日期 */
  publishDate: string
  /** 工作地点 */
  location: string
  /** 学历要求 */
  education: string
  /** 工作年限 */
  year: string
  /** 数据来源 */
  dataFrom: number
}>
