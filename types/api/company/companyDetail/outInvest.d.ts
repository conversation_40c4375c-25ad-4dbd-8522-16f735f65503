/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:37:11
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/outInvest.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailOutInvestReqType extends paginationBaseRequest {
  cId: string
}

export type CompanyDetailOutInvestResType = paginationBaseResponse<{
  /** 被投资法定代表人 */
  legalPerson: string
  /** 投资占比 */
  regRate: string
  /** 注册资本 */
  regCapital: string
  /** 被投资企业名称 */
  entName: string
  /** 经营状态 */
  openStatus: string
  /** 成立日期 */
  startDate: string
  /**
   * A、b 类企业
   */
  companyClass: 'A' | 'B'

  /**
   * 是否置灰 0否 1是
   */
  disabled: 0 | 1
  /**
   * 法人id
   */
  legalId: string
}>
