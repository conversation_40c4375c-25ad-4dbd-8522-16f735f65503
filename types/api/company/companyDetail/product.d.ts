/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:40:56
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/product.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailProductReqType extends paginationBaseRequest {
  cId: string
}

export type CompanyDetailProductResType = paginationBaseResponse<{
  /** 产品简称 */
  productAbbreviation: string
  /** 领域 */
  domain: string
  /** 详情 */
  detailContent: string
  /** 产品分类 */
  classification: string
  /** 产品名称 */
  productName: string
}>
