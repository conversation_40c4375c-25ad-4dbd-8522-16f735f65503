/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 16:30:51
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 18:46:07
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/companyBaseInfo.ts
 * @Description: 
 */
export interface companyBaseInfoReqType {
  cId: string
}

export interface companyBaseInfoResType {
  id: string
  officeAddr: string
  realCapital: string
  regNo: string
  city: string
  openTime: string
  taxpayerQualification: string
  industry: string
  oldEntName: string
  staffSize: string
  openStatus: string
  englishName: string
  taxNo: string
  province: string
  checkDate: string
  scope: string
  orgNo: string
  createDate: string
  email: string
  startDate: string
  area: string
  legalPerson: string
  website: string
  regCapital: string
  entType: string
  unifiedCode: string
  licenseNumber: string
  phone: string
  districtCode: string
  authority: string
  annualDate: string
  district: string
  numberOfInsuredPersons: string
  regAddr: string
  entName: string
  desc: string
  showTags: []
  shortName: string
  entLogo: string
  totalSocre: string
  updateTime: string
  cid: string
}
