/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:59:42
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/branch.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailBranchReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailBranchResType = paginationBaseResponse<{
  /** 企业名称 */
  entName: string
  /** 法人代表 */
  legalPerson: string
  /** 经营状态 */
  openStatus: string
  /** 企业logo */
  entLogo: string
  /** 公司ID */
  cId: string
  /** 公司简称 */
  shortName: string
}>
