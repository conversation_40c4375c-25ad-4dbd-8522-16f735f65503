/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 17:06:38
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 17:56:04
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/newsBusiness.ts
 * @Description:
 */

import { paginationBaseRequest } from '~/types/common/pagination'
import { newsCardTypeBaseType } from '~/types/common/newsCard'

export interface newsBusinessReqType extends paginationBaseRequest {
  companyId: string
  /** 类型 */
  type: string | undefined
}

export interface newsBusinessResType extends newsCardTypeBaseType {

}
