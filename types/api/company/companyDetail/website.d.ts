/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:50:22
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/website.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailWebsiteReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailWebsiteResType = paginationBaseResponse<{
  /** 网站名称 */
  siteName: string
  /** 网址 */
  homeSite: string
  /** 域名 */
  domain: string
  /** 网站备案/许可证号 */
  icpNo: string
  /** 审核日期 */
  checkDate: string
}>
