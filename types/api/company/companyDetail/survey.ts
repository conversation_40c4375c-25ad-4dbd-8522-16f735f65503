/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 18:00:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 18:21:18
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/survey.ts
 * @Description:
 */

export interface surveyReqType {
  companyId: string
}

export interface surveyResType {
  tag: surveyTagType
  mainIndustry: JsonVo[]
  deputyIndustry: JsonVo[]
  /** 母公司 */
  baseCompany?: { companyId: string; companyName: string }
}

export interface surveyTagType {
  /** 发展阶段 */
  round: JsonVo
  /** 营收规模 */
  annualRevenue: JsonVo
  /** 人员规模 */
  staffSize: JsonVo
  /** 云应用量 */
  cloud: JsonVo
  /** 实力指数 */
  totalSocre: string
}

interface JsonVo {
  label: string
  value: string | number
  disabled: boolean
  score?: number
  children?: JsonVo[]
}
