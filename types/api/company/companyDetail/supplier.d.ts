/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:58:41
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/supplier.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailSupplierReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailSupplierResType = paginationBaseResponse<{
  /** 企业logo */
  entLogo: string
  /** 供应商名称 */
  supplier: string
  /** 采购金额 */
  purchaseAmount: string
  /** 采购占比 */
  purchaseProportion: string
  /** 报告期 */
  reportingPeriod: string
  /** 数据来源 */
  dataSources: string
  /** 关系 */
  relationship: string
  /** 公司ID */
  cId: string
  /** 公司简称 */
  ShortName: string
  /**
   * A、b 类企业
   */
  companyClass: 'A' | 'B'

  /**
   * 是否置灰 0否 1是
   */
  disabled: 0 | 1
}>
