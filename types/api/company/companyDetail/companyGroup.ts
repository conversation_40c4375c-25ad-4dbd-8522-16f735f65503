/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-06-20 18:22:42
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-06-20 18:27:56
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/companyGroup.ts
 * @Description:
 */

import { paginationBaseRequest } from '~/types/common/pagination'

export interface companyGroupReqType extends paginationBaseRequest {
  companyId: string
}

export interface companyGroupResType {
  /** 主体公司id */
  baseCompanyId: string
  /** 主体公司 */
  baseCompany: string
  /** 集团名 */
  groupName: string
  /** 集团logo */
  groupLogo: string
  /** 简称 */
  shortName: string
  /** 集团成员 */
  members: []
  current: number
  size: number
  total: number
}
