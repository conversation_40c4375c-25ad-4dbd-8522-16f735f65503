/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:48:41
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/business.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailBusinessReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailBusinessResType = paginationBaseResponse<{
  /**
   * countId
   */
  countId?: string
  /**
   * 当前页
   */
  current?: number
  /**
   * 是否命中count缓存
   */
  hitCount?: boolean
  /**
   * 是否进行 count 查询
   */
  isSearchCount?: boolean
  /**
   * countId
   */
  maxLimit?: number
  /**
   * 自动优化 COUNT SQL
   */
  optimizeCountSql?: boolean
  /**
   * 排序字段信息
   */
  orders?: OrderItem[]
  /**
   * 查询数据列表
   */
  records?: CompanyBusinessVo[]
  /**
   * 每页显示条数，默认 10
   */
  size?: number
  /**
   * 总数
   */
  total?: number
}>

interface OrderItem {
  /**
   * 是否正序排列，默认 true
   */
  asc?: boolean
  /**
   * 需要进行排序的字段
   */
  column?: string
}

interface CompanyBusinessVo {
  logo?: string
  /**
   * 介绍
   */
  productDesc?: string
  /**
   * 业务
   */
  productName?: string
  shortName?: string
}
