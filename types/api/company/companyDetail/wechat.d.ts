/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:52:15
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/wechat.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailWechatReqType extends paginationBaseRequest {
  companyId: string
}

export type CompanyDetailWechatResType = paginationBaseResponse<{
  /** 微信公众号 */
  wechatName: string
  /** 微信号 */
  wechatId: string
  /** 简介 */
  wechatDesc: string
  /** 二维码 */
  qrcode: string
  /** 头像 */
  logo: string
}>
