/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 18:55:15
 * @FilePath: /corp-elf-web-consumer/types/api/company/companyDetail/ipo.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../../common/pagination'
export interface CompanyDetailIpoReqType {
  companyId: string
}

export type CompanyDetailIpoResType = paginationBaseResponse<{
  /** 上市类型 */
  sharesType: string
  /** 成交额 */
  turnover: string
  /** 涨停 */
  increase: string
  /** 跌停 */
  decrease: string
  /** 市净 */
  netRate: string
  /** 总市值 */
  totalMarketValue: string
  /** 成交量 */
  volume: string
  /** 市盈 */
  marketProfit: string
  /** 更新时间 */
  visitTime: string
  /** 上市日期 */
  marketDate: string
  /** 网上发行日期 */
  internetReleasesDate: string
  /** 流通市值 */
  marketCapitalization: string
  /** 代码 */
  code: string
}>
