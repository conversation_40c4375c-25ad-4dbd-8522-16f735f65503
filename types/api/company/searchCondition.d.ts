/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 17:05:53
 * @FilePath: /corp-elf-web-consumer/types/api/company/searchCondition.d.ts
 * @Description: 
 */
export type SearchConditionResType = Record<string, SearchConditionItem>

interface SearchConditionItem {
  fieldType: string
  condition: SearchConditionItemValue
}
interface SearchConditionItemValue {
  conditionName: string
  conditionType: string
  conditionValue: boolean
}
