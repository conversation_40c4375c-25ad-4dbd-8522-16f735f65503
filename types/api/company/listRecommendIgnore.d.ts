/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-05 15:09:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-05 16:45:12
 * @FilePath: /corp-elf-web-consumer/types/api/company/listRecommendIgnore.d.ts
 * @Description:
 */
import { CompanySearchReqType, CompanySearchResType } from '../../common/companySearch'
import { paginationBaseRequest, paginationBaseResponse } from '../../common/pagination'

export interface ListRecommendIgnoreReqType extends paginationBaseRequest {
  filter: CompanySearchReqType
}
export type ListRecommendIgnoreResType = paginationBaseResponse<CompanySearchResType>
