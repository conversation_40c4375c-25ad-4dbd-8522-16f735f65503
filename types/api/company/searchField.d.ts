/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-03 16:26:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-03 17:21:52
 * @FilePath: /corp-elf-web-consumer/types/api/company/searchField.d.ts
 * @Description: 
 */
export type SearchFieldResType = Record<string, SearchFieldItem>

type SearchFieldItem = Record<string, FieldItem[]>

export interface FieldItem {
  fieldIndex: string
  fieldName: string
  fieldType: string
  isPreField: boolean
  isShow: boolean
  path: string
  path_one: string
  path_two: string
  searchIndex: string
  sorted: string
}
