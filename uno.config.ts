/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 14:59:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-07-17 14:18:24
 * @FilePath: /corp-elf-web-consumer/uno.config.ts
 * @Description:
 */
// uno.config.ts
import { defineConfig, presetAttributify, presetUno, transformerVariantGroup, transformerDirectives } from 'unocss'
import { presetScalpel } from 'unocss-preset-scalpel'
import { presetScrollbar } from 'unocss-preset-scrollbar'

export default defineConfig({
  presets: [
    presetAttributify(),
    presetUno(),
    presetScalpel(),
    presetScrollbar()
    // ...自定义预设
  ],
  transformers: [transformerVariantGroup(), transformerDirectives()]
})
