{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "tsc": "vue-tsc --noEmit", "build-noTsc": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build", "build:production": "vite build --mode production", "build:staging": "vite build --mode staging"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@types/mockjs": "^1.0.10", "@unocss/reset": "^0.58.0", "@vueuse/components": "^10.7.0", "@vueuse/core": "^10.7.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "ant-design-vue": "~4.1.2", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "device-detector-js": "^3.0.3", "driver.js": "^1.3.1", "file-saver": "^2.0.5", "hex-rgb": "^5.0.0", "highlight.js": "^11.11.1", "jquery": "^3.7.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "nzh": "^1.0.14", "pinia": "^2.1.7", "qs": "^6.11.2", "swiper": "^8.4.7", "ua-parser-js": "^1.0.39", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.5.16", "vue-hooks-plus": "^1.8.8", "vue-router": "4", "vue3-text-clamp": "^0.1.2", "xgplayer": "^3.0.16", "xlsx": "^0.18.5"}, "devDependencies": {"@types/crypto-js": "^4.2.1", "@types/jquery": "^3.5.29", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^18.6.2", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.10", "@types/ua-parser-js": "^0.7.39", "@unocss/eslint-config": "^0.58.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "eslint-plugin-vue": "^9.30.0", "less": "^4.2.0", "rollup-plugin-visualizer": "^5.12.0", "typescript": "^5.2.2", "unocss": "^66.1.4", "unocss-preset-scalpel": "^1.2.7", "unocss-preset-scrollbar": "^3.2.0", "unplugin-vue-components": "^0.26.0", "vite": "^6.3.5", "vite-plugin-compression2": "^1.3.0", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^1.8.25"}}