server {
  listen 80;

  # gzip
  gzip on;
  gzip_static on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  gzip_http_version 1.1;
  gzip_comp_level 3;
  gzip_types text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/xml+rss image/svg+xml application/json;
  gzip_vary on;
  gzip_proxied expired no-cache no-store private auth;
  gzip_disable "MSIE [1-6].";

  client_max_body_size 10m;
  # 配置代理超时时间（连接、读取、发送）
  proxy_connect_timeout 300s;
  proxy_read_timeout 300s;
  proxy_send_timeout 300s;


  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ @rewrites;
  }

  location @rewrites {
    rewrite ^(.+)$ /index.html last;
  }


  # 高管说解读-测试环境
  location /sse/corp_elf_test {
    rewrite ^/sse/corp_elf_test/(.*)$ /$1 break;
    # 添加以下配置以处理SSE
    proxy_set_header Accept text/event-stream;
    #proxy_cache off;
    proxy_buffering off;

    # 设置代理头信息
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://**************:31854/; # 不走网关地址，走真实后端地址
  }

  # 高管说解读-正式环境
  location /sse/corp_elf {
    rewrite ^/sse/corp_elf/(.*)$ /$1 break;
    # 添加以下配置以处理SSE
    proxy_set_header Accept text/event-stream;
    #proxy_cache off;
    proxy_buffering off;

    # 设置代理头信息
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://**************:31854/; # 不走网关地址，走真实后端地址
  }

  # 商瞳接口-测试
  location /corp_elf_test {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    rewrite ^/corp_elf_test/(.*)$ /$1 break;
    # proxy_pass http://**************:31854; # 后端真实地址
    proxy_pass http://************:31962; # 网关地址

  }

  # 商瞳接口-正式
  location /corp_elf {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    rewrite ^/corp_elf/(.*)$ /$1 break;
    # proxy_pass http://**************:31854; # 后端真实地址
    proxy_pass http://************:31962/; # 网关地址
  }


  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
