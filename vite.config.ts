/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-10-14 16:57:57
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-12 15:21:25
 * @FilePath: /corp-elf-web-consumer/vite.config.ts
 * @Description:
 */
/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 14:45:12
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-11-19 16:02:31
 * @FilePath: /corp-elf-web-consumer/vite.config.ts
 * @Description:
 */
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import UnoCSS from 'unocss/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import vueJsx from '@vitejs/plugin-vue-jsx'
import VueDevTools from 'vite-plugin-vue-devtools'
import VueSetupExtend from 'vite-plugin-vue-setup-extend'
import { visualizer } from 'rollup-plugin-visualizer'
import { compression } from 'vite-plugin-compression2'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode, isSsrBuild, isPreview }) => {
  return {
    optimizeDeps: { exclude: ['swiper/vue', 'swiper/types'] },
    css: {
      // devSourcemap: true,
      preprocessorOptions: {
        less: {
          modifyVars: {
            // 'primary-color1': '#6553EE',
            // 'link-color': '#6553EE',
            // 'layout-body-background': '#f4f5f7',
            // 'card-padding-base': '16px',
            // 'border-radius-base': '4px',
            // 'table-header-bg': 'rgba(152, 133, 237, 0.06)',
            // 'tag-default-bg': '#EAE7FB',
            // 'text-color': '#050505'
          },
          javascriptEnabled: true
        }
      }
    },
    plugins: [
      vue(),
      vueJsx(),
      VueSetupExtend(),
      Components({
        resolvers: [
          AntDesignVueResolver({
            importStyle: false // css in js
          })
        ]
      }),
      VueDevTools(),
      UnoCSS(),
      compression()
      // visualizer({
      //   open: true, // 直接在浏览器中打开分析报告
      //   filename: 'stats.html', // 输出文件的名称
      //   gzipSize: true, // 显示gzip后的大小
      //   brotliSize: true // 显示brotli压缩后的大小
      // })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@comp': fileURLToPath(new URL('./src/components', import.meta.url)),
        '@api': fileURLToPath(new URL('./src/api', import.meta.url)),
        '@assets': fileURLToPath(new URL('./src/assets', import.meta.url))
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json']
    },
    esbuild: {
      drop: mode === 'production' ? ['console', 'debugger'] : []
    },
    build: {
      reportCompressedSize: false,
      sourcemap: false,
      chunkSizeWarningLimit: 1024, // chunk 大小警告的限制（单位kb）
      rollupOptions: {
        output: {
          experimentalMinChunkSize: 100 * 1024, // 最小chunk大小（单位b）
          manualChunks(id) {
            // 创建一个对象映射，用于存储库名及其对应的chunk名称
            const libraryChunkMap = {
              lodash: 'lodash-es',
              'ant-design-vue': 'ant-design-vue',
              axios: 'axios',
              jquery: 'jquery',
              swiper: 'swiper',
              mockjs: 'mockjs',
              xgplayer: 'xgplayer',
              '@wangeditor': '@wangeditor'
              // vue: ['vue']
            }

            // 检查模块ID是否包含'node_modules'，即是否为第三方依赖
            if (id.includes('node_modules')) {
              const matchedLibrary = Object.keys(libraryChunkMap).find(library => id.includes(library))
              // 如果找到了匹配的库名，返回对应的chunk名称（从libraryChunkMap中获取）
              if (matchedLibrary) {
                return `${libraryChunkMap[matchedLibrary]}-vendor`
              } else {
                // 如果未找到匹配的库名，将该第三方依赖归入默认的'vendor' chunk
                return 'vendor'
              }
            }
          },
          chunkFileNames: chunkInfo => (chunkInfo.name.includes('vendor') ? `assets/js/[name]-[hash].js` : 'assets/js/chunk-[hash].js'),
          entryFileNames: chunkInfo => (chunkInfo.name.includes('vendor') ? `assets/js/[name]-[hash].js` : 'assets/js/chunk-[hash].js'),
          assetFileNames: 'assets/[ext]/[hash].[ext]'
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: true,
      // https: true,
      proxy: {
        '/sse/corp_elf_test': {
          // target: 'http://************:31854/', // 请求本地
          // target: 'http://*************:31854/', // 请求本地
          // target: 'http://localhost:31854',
          target: 'https://insight.bengine.com.cn/sse/corp_elf_test',
          changeOrigin: true,
          // bypass(req, res, options) {
          //   const proxyURL = options.target + options.rewrite(req.url)
          //   res.setHeader('x-req-proxyURL', proxyURL) // 将真实请求地址设置到响应头中
          // },
          rewrite: path => path.replace(/^\/sse\/corp_elf_test/, '')
        },
        '/corp_elf_test': {
          // target: 'http://************:31854/', // 请求本地
          // target: 'http://*************:31854/', // 请求本地
          // target: 'http://localhost:31854',
          target: 'https://insight.bengine.com.cn/corp_elf_test',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/corp_elf_test/, ''),
          // bypass(req, res, options) {
          //   const proxyURL = options.target + options.rewrite(req.url)
          //   res.setHeader('x-req-proxyURL', proxyURL) // 将真实请求地址设置到响应头中
          // }
        },
        '/corp_elf': {
          // target: 'http://**************:31854', // 请求本地
          // target: 'http://**************:31854', // 请求本地
          // target: 'http://localhost:38984',
          target: 'https://insight.bengine.com.cn',
          changeOrigin: true
          // rewrite: path => path.replace(/^\/corp_elf/, '/')
        },
        '/mock': {
          target: 'http://127.0.0.1:4523/m1/4748419-4401507-default',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/mock/, '/')
        }
      }
    }
  }
})
