---
apiVersion: v1
kind: Service
metadata:
  annotations:
    app.vuejs.org/commit-id: ${CI_COMMIT_SHA}
    app.vuejs.org/build-timestamp: ${BUILD_TIME}
  labels:
    app.kubernetes.io/name: ${APP_NAME}
    app.kubernetes.io/version: "${VERSION}"
  name: ${APP_NAME}
spec:
  type: NodePort # 主要改这个地方
  ports:
    - name: http
      protocol: TCP
      port: 48145
      targetPort: 80
  selector:
    app.kubernetes.io/name: ${APP_NAME}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    app.vuejs.org/commit-id: ${CI_COMMIT_SHA}
    app.vuejs.org/build-timestamp: ${BUILD_TIME}
  labels:
    app.kubernetes.io/name: ${APP_NAME}
    app.kubernetes.io/version: "${VERSION}"
  name: ${APP_NAME}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: ${APP_NAME}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        app.vuejs.org/commit-id: ${CI_COMMIT_SHA}
        app.vuejs.org/build-timestamp: "${VERSION}"
      labels:
        app.kubernetes.io/name: ${APP_NAME}
        app.kubernetes.io/version: "${VERSION}"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node-group
                    operator: In
                    values:
                      - b2b
      tolerations:
      - effect: NoSchedule
        key: node-group
        value: b2b
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      containers:
        - name: ${APP_NAME}
          image: ${DOCKER_REGISTRY}/${IMAGE_TAG}
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 250m
              memory: 256Mi
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: 80
              scheme: HTTP
            initialDelaySeconds: 0
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 10
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: 80
              scheme: HTTP
            initialDelaySeconds: 0
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 10
